"""
筛选模块测试
测试期货筛选和评估功能
"""

import unittest
import os
import sys

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from modules.screening.futures_scanner import FuturesScanner
from modules.screening.evaluation_engine import EvaluationEngine


class TestScreeningModule(unittest.TestCase):
    """筛选模块测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.futures_scanner = FuturesScanner()
        self.evaluation_engine = EvaluationEngine()
    
    def test_futures_scanner_initialization(self):
        """测试期货筛选器初始化"""
        self.assertIsNotNone(self.futures_scanner)
        print("✓ 期货筛选器初始化测试通过")
    
    def test_evaluation_engine_initialization(self):
        """测试评估引擎初始化"""
        self.assertIsNotNone(self.evaluation_engine)
        print("✓ 评估引擎初始化测试通过")
    
    def test_scanner_methods(self):
        """测试筛选器方法"""
        # 测试方法存在
        self.assertTrue(hasattr(self.futures_scanner, 'run_scan'))
        self.assertTrue(hasattr(self.futures_scanner, 'get_latest_results'))
        print("✓ 筛选器方法测试通过")


if __name__ == "__main__":
    unittest.main()
