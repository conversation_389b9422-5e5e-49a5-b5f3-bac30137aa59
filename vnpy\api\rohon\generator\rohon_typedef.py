TThostFtdcTraderIDType = "string"
TThostFtdcInvestorIDType = "string"
TThostFtdcBrokerIDType = "string"
TThostFtdcBrokerAbbrType = "string"
TThostFtdcBrokerNameType = "string"
TThostFtdcExchangeInstIDType = "string"
TThostFtdcOrderRefType = "string"
TThostFtdcParticipantIDType = "string"
TThostFtdcUserIDType = "string"
TThostFtdcPasswordType = "string"
TThostFtdcClientIDType = "string"
TThostFtdcInstrumentIDType = "string"
TThostFtdcInstrumentCodeType = "string"
TThostFtdcMarketIDType = "string"
TThostFtdcProductNameType = "string"
TThostFtdcExchangeIDType = "string"
TThostFtdcExchangeNameType = "string"
TThostFtdcExchangeAbbrType = "string"
TThostFtdcExchangeFlagType = "string"
TThostFtdcMacAddressType = "string"
TThostFtdcSystemIDType = "string"
TThostFtdcExchangePropertyType = "char"
TThostFtdcDateType = "string"
TThostFtdcTimeType = "string"
TThostFtdcLongTimeType = "string"
TThostFtdcInstrumentNameType = "string"
TThostFtdcSettlementGroupIDType = "string"
TThostFtdcOrderSysIDType = "string"
TThostFtdcTradeIDType = "string"
TThostFtdcCommandTypeType = "string"
TThostFtdcIPAddressType = "string"
TThostFtdcIPPortType = "int"
TThostFtdcProductInfoType = "string"
TThostFtdcProtocolInfoType = "string"
TThostFtdcBusinessUnitType = "string"
TThostFtdcDepositSeqNoType = "string"
TThostFtdcIdentifiedCardNoType = "string"
TThostFtdcIdCardTypeType = "char"
TThostFtdcOrderLocalIDType = "string"
TThostFtdcUserNameType = "string"
TThostFtdcPartyNameType = "string"
TThostFtdcErrorMsgType = "string"
TThostFtdcFieldNameType = "string"
TThostFtdcFieldContentType = "string"
TThostFtdcSystemNameType = "string"
TThostFtdcContentType = "string"
TThostFtdcInvestorRangeType = "char"
TThostFtdcDepartmentRangeType = "char"
TThostFtdcDataSyncStatusType = "char"
TThostFtdcBrokerDataSyncStatusType = "char"
TThostFtdcExchangeConnectStatusType = "char"
TThostFtdcTraderConnectStatusType = "char"
TThostFtdcFunctionCodeType = "char"
TThostFtdcBrokerFunctionCodeType = "char"
TThostFtdcOrderActionStatusType = "char"
TThostFtdcOrderStatusType = "char"
TThostFtdcOrderSubmitStatusType = "char"
TThostFtdcPositionDateType = "char"
TThostFtdcPositionDateTypeType = "char"
TThostFtdcTradingRoleType = "char"
TThostFtdcProductClassType = "char"
TThostFtdcAPIProductClassType = "char"
TThostFtdcInstLifePhaseType = "char"
TThostFtdcDirectionType = "char"
TThostFtdcPositionTypeType = "char"
TThostFtdcPosiDirectionType = "char"
TThostFtdcSysSettlementStatusType = "char"
TThostFtdcRatioAttrType = "char"
TThostFtdcHedgeFlagType = "char"
TThostFtdcBillHedgeFlagType = "char"
TThostFtdcClientIDTypeType = "char"
TThostFtdcOrderPriceTypeType = "char"
TThostFtdcOffsetFlagType = "char"
TThostFtdcForceCloseReasonType = "char"
TThostFtdcOrderTypeType = "char"
TThostFtdcTimeConditionType = "char"
TThostFtdcVolumeConditionType = "char"
TThostFtdcContingentConditionType = "char"
TThostFtdcActionFlagType = "char"
TThostFtdcTradingRightType = "char"
TThostFtdcOrderSourceType = "char"
TThostFtdcTradeTypeType = "char"
TThostFtdcSpecPosiTypeType = "char"
TThostFtdcPriceSourceType = "char"
TThostFtdcInstrumentStatusType = "char"
TThostFtdcInstStatusEnterReasonType = "char"
TThostFtdcOrderActionRefType = "int"
TThostFtdcInstallCountType = "int"
TThostFtdcInstallIDType = "int"
TThostFtdcErrorIDType = "int"
TThostFtdcSettlementIDType = "int"
TThostFtdcVolumeType = "int"
TThostFtdcFrontIDType = "int"
TThostFtdcSessionIDType = "int"
TThostFtdcSequenceNoType = "int"
TThostFtdcCommandNoType = "int"
TThostFtdcMillisecType = "int"
TThostFtdcVolumeMultipleType = "int"
TThostFtdcTradingSegmentSNType = "int"
TThostFtdcRequestIDType = "int"
TThostFtdcYearType = "int"
TThostFtdcMonthType = "int"
TThostFtdcBoolType = "int"
TThostFtdcPriceType = "double"
TThostFtdcCombOffsetFlagType = "string"
TThostFtdcCombHedgeFlagType = "string"
TThostFtdcRatioType = "double"
TThostFtdcMoneyType = "double"
TThostFtdcLargeVolumeType = "double"
TThostFtdcSequenceSeriesType = "int"
TThostFtdcCommPhaseNoType = "int"
TThostFtdcSequenceLabelType = "string"
TThostFtdcUnderlyingMultipleType = "double"
TThostFtdcPriorityType = "int"
TThostFtdcContractCodeType = "string"
TThostFtdcCityType = "string"
TThostFtdcIsStockType = "string"
TThostFtdcChannelType = "string"
TThostFtdcAddressType = "string"
TThostFtdcZipCodeType = "string"
TThostFtdcTelephoneType = "string"
TThostFtdcFaxType = "string"
TThostFtdcMobileType = "string"
TThostFtdcEMailType = "string"
TThostFtdcMemoType = "string"
TThostFtdcCompanyCodeType = "string"
TThostFtdcWebsiteType = "string"
TThostFtdcTaxNoType = "string"
TThostFtdcBatchStatusType = "char"
TThostFtdcPropertyIDType = "string"
TThostFtdcPropertyNameType = "string"
TThostFtdcLicenseNoType = "string"
TThostFtdcAgentIDType = "string"
TThostFtdcAgentNameType = "string"
TThostFtdcAgentGroupIDType = "string"
TThostFtdcAgentGroupNameType = "string"
TThostFtdcReturnStyleType = "char"
TThostFtdcReturnPatternType = "char"
TThostFtdcReturnLevelType = "char"
TThostFtdcReturnStandardType = "char"
TThostFtdcMortgageTypeType = "char"
TThostFtdcInvestorSettlementParamIDType = "char"
TThostFtdcExchangeSettlementParamIDType = "char"
TThostFtdcSystemParamIDType = "char"
TThostFtdcTradeParamIDType = "char"
TThostFtdcSettlementParamValueType = "string"
TThostFtdcCounterIDType = "string"
TThostFtdcInvestorGroupNameType = "string"
TThostFtdcBrandCodeType = "string"
TThostFtdcWarehouseType = "string"
TThostFtdcProductDateType = "string"
TThostFtdcGradeType = "string"
TThostFtdcClassifyType = "string"
TThostFtdcPositionType = "string"
TThostFtdcYieldlyType = "string"
TThostFtdcWeightType = "string"
TThostFtdcSubEntryFundNoType = "int"
TThostFtdcFileIDType = "char"
TThostFtdcFileNameType = "string"
TThostFtdcFileTypeType = "char"
TThostFtdcFileFormatType = "char"
TThostFtdcFileUploadStatusType = "char"
TThostFtdcTransferDirectionType = "char"
TThostFtdcUploadModeType = "string"
TThostFtdcAccountIDType = "string"
TThostFtdcBankFlagType = "string"
TThostFtdcBankAccountType = "string"
TThostFtdcOpenNameType = "string"
TThostFtdcOpenBankType = "string"
TThostFtdcBankNameType = "string"
TThostFtdcPublishPathType = "string"
TThostFtdcOperatorIDType = "string"
TThostFtdcMonthCountType = "int"
TThostFtdcAdvanceMonthArrayType = "string"
TThostFtdcDateExprType = "string"
TThostFtdcInstrumentIDExprType = "string"
TThostFtdcInstrumentNameExprType = "string"
TThostFtdcSpecialCreateRuleType = "char"
TThostFtdcBasisPriceTypeType = "char"
TThostFtdcProductLifePhaseType = "char"
TThostFtdcDeliveryModeType = "char"
TThostFtdcLogLevelType = "string"
TThostFtdcProcessNameType = "string"
TThostFtdcOperationMemoType = "string"
TThostFtdcFundIOTypeType = "char"
TThostFtdcFundTypeType = "char"
TThostFtdcFundDirectionType = "char"
TThostFtdcFundStatusType = "char"
TThostFtdcBillNoType = "string"
TThostFtdcBillNameType = "string"
TThostFtdcPublishStatusType = "char"
TThostFtdcEnumValueIDType = "string"
TThostFtdcEnumValueTypeType = "string"
TThostFtdcEnumValueLabelType = "string"
TThostFtdcEnumValueResultType = "string"
TThostFtdcSystemStatusType = "char"
TThostFtdcSettlementStatusType = "char"
TThostFtdcRangeIntTypeType = "string"
TThostFtdcRangeIntFromType = "string"
TThostFtdcRangeIntToType = "string"
TThostFtdcFunctionIDType = "string"
TThostFtdcFunctionValueCodeType = "string"
TThostFtdcFunctionNameType = "string"
TThostFtdcRoleIDType = "string"
TThostFtdcRoleNameType = "string"
TThostFtdcDescriptionType = "string"
TThostFtdcCombineIDType = "string"
TThostFtdcCombineTypeType = "string"
TThostFtdcInvestorTypeType = "char"
TThostFtdcBrokerTypeType = "char"
TThostFtdcRiskLevelType = "char"
TThostFtdcFeeAcceptStyleType = "char"
TThostFtdcPasswordTypeType = "char"
TThostFtdcAlgorithmType = "char"
TThostFtdcIncludeCloseProfitType = "char"
TThostFtdcAllWithoutTradeType = "char"
TThostFtdcCommentType = "string"
TThostFtdcVersionType = "string"
TThostFtdcTradeCodeType = "string"
TThostFtdcTradeDateType = "string"
TThostFtdcTradeTimeType = "string"
TThostFtdcTradeSerialType = "string"
TThostFtdcTradeSerialNoType = "int"
TThostFtdcFutureIDType = "string"
TThostFtdcBankIDType = "string"
TThostFtdcBankBrchIDType = "string"
TThostFtdcBankBranchIDType = "string"
TThostFtdcOperNoType = "string"
TThostFtdcDeviceIDType = "string"
TThostFtdcRecordNumType = "string"
TThostFtdcFutureAccountType = "string"
TThostFtdcFuturePwdFlagType = "char"
TThostFtdcTransferTypeType = "char"
TThostFtdcFutureAccPwdType = "string"
TThostFtdcCurrencyCodeType = "string"
TThostFtdcRetCodeType = "string"
TThostFtdcRetInfoType = "string"
TThostFtdcTradeAmtType = "string"
TThostFtdcUseAmtType = "string"
TThostFtdcFetchAmtType = "string"
TThostFtdcTransferValidFlagType = "char"
TThostFtdcCertCodeType = "string"
TThostFtdcReasonType = "char"
TThostFtdcFundProjectIDType = "string"
TThostFtdcSexType = "char"
TThostFtdcProfessionType = "string"
TThostFtdcNationalType = "string"
TThostFtdcProvinceType = "string"
TThostFtdcRegionType = "string"
TThostFtdcCountryType = "string"
TThostFtdcLicenseNOType = "string"
TThostFtdcCompanyTypeType = "string"
TThostFtdcBusinessScopeType = "string"
TThostFtdcCapitalCurrencyType = "string"
TThostFtdcUserTypeType = "char"
TThostFtdcBranchIDType = "string"
TThostFtdcRateTypeType = "char"
TThostFtdcNoteTypeType = "char"
TThostFtdcSettlementStyleType = "char"
TThostFtdcBrokerDNSType = "string"
TThostFtdcSentenceType = "string"
TThostFtdcSettlementBillTypeType = "char"
TThostFtdcUserRightTypeType = "char"
TThostFtdcMarginPriceTypeType = "char"
TThostFtdcBillGenStatusType = "char"
TThostFtdcAlgoTypeType = "char"
TThostFtdcHandlePositionAlgoIDType = "char"
TThostFtdcFindMarginRateAlgoIDType = "char"
TThostFtdcHandleTradingAccountAlgoIDType = "char"
TThostFtdcPersonTypeType = "char"
TThostFtdcQueryInvestorRangeType = "char"
TThostFtdcInvestorRiskStatusType = "char"
TThostFtdcLegIDType = "int"
TThostFtdcLegMultipleType = "int"
TThostFtdcImplyLevelType = "int"
TThostFtdcClearAccountType = "string"
TThostFtdcOrganNOType = "string"
TThostFtdcClearbarchIDType = "string"
TThostFtdcUserEventTypeType = "char"
TThostFtdcUserEventInfoType = "string"
TThostFtdcCloseStyleType = "char"
TThostFtdcStatModeType = "char"
TThostFtdcParkedOrderStatusType = "char"
TThostFtdcParkedOrderIDType = "string"
TThostFtdcParkedOrderActionIDType = "string"
TThostFtdcVirDealStatusType = "char"
TThostFtdcOrgSystemIDType = "char"
TThostFtdcVirTradeStatusType = "char"
TThostFtdcVirBankAccTypeType = "char"
TThostFtdcVirementStatusType = "char"
TThostFtdcVirementAvailAbilityType = "char"
TThostFtdcVirementTradeCodeType = "char"
TThostFtdcPhotoTypeNameType = "string"
TThostFtdcPhotoTypeIDType = "string"
TThostFtdcPhotoNameType = "string"
TThostFtdcTopicIDType = "int"
TThostFtdcReportTypeIDType = "string"
TThostFtdcCharacterIDType = "string"
TThostFtdcAMLParamIDType = "string"
TThostFtdcAMLInvestorTypeType = "string"
TThostFtdcAMLIdCardTypeType = "string"
TThostFtdcAMLTradeDirectType = "string"
TThostFtdcAMLTradeModelType = "string"
TThostFtdcAMLOpParamValueType = "double"
TThostFtdcAMLCustomerCardTypeType = "string"
TThostFtdcAMLInstitutionNameType = "string"
TThostFtdcAMLDistrictIDType = "string"
TThostFtdcAMLRelationShipType = "string"
TThostFtdcAMLInstitutionTypeType = "string"
TThostFtdcAMLInstitutionIDType = "string"
TThostFtdcAMLAccountTypeType = "string"
TThostFtdcAMLTradingTypeType = "string"
TThostFtdcAMLTransactClassType = "string"
TThostFtdcAMLCapitalIOType = "string"
TThostFtdcAMLSiteType = "string"
TThostFtdcAMLCapitalPurposeType = "string"
TThostFtdcAMLReportTypeType = "string"
TThostFtdcAMLSerialNoType = "string"
TThostFtdcAMLStatusType = "string"
TThostFtdcAMLGenStatusType = "char"
TThostFtdcAMLSeqCodeType = "string"
TThostFtdcAMLFileNameType = "string"
TThostFtdcAMLMoneyType = "double"
TThostFtdcAMLFileAmountType = "int"
TThostFtdcCFMMCKeyType = "string"
TThostFtdcCFMMCTokenType = "string"
TThostFtdcCFMMCKeyKindType = "char"
TThostFtdcAMLReportNameType = "string"
TThostFtdcIndividualNameType = "string"
TThostFtdcCurrencyIDType = "string"
TThostFtdcCustNumberType = "string"
TThostFtdcOrganCodeType = "string"
TThostFtdcOrganNameType = "string"
TThostFtdcSuperOrganCodeType = "string"
TThostFtdcSubBranchIDType = "string"
TThostFtdcSubBranchNameType = "string"
TThostFtdcBranchNetCodeType = "string"
TThostFtdcBranchNetNameType = "string"
TThostFtdcOrganFlagType = "string"
TThostFtdcBankCodingForFutureType = "string"
TThostFtdcBankReturnCodeType = "string"
TThostFtdcPlateReturnCodeType = "string"
TThostFtdcBankSubBranchIDType = "string"
TThostFtdcFutureBranchIDType = "string"
TThostFtdcReturnCodeType = "string"
TThostFtdcOperatorCodeType = "string"
TThostFtdcClearDepIDType = "string"
TThostFtdcClearBrchIDType = "string"
TThostFtdcClearNameType = "string"
TThostFtdcBankAccountNameType = "string"
TThostFtdcInvDepIDType = "string"
TThostFtdcInvBrchIDType = "string"
TThostFtdcMessageFormatVersionType = "string"
TThostFtdcDigestType = "string"
TThostFtdcAuthenticDataType = "string"
TThostFtdcPasswordKeyType = "string"
TThostFtdcFutureAccountNameType = "string"
TThostFtdcMobilePhoneType = "string"
TThostFtdcFutureMainKeyType = "string"
TThostFtdcFutureWorkKeyType = "string"
TThostFtdcFutureTransKeyType = "string"
TThostFtdcBankMainKeyType = "string"
TThostFtdcBankWorkKeyType = "string"
TThostFtdcBankTransKeyType = "string"
TThostFtdcBankServerDescriptionType = "string"
TThostFtdcAddInfoType = "string"
TThostFtdcDescrInfoForReturnCodeType = "string"
TThostFtdcCountryCodeType = "string"
TThostFtdcSerialType = "int"
TThostFtdcPlateSerialType = "int"
TThostFtdcBankSerialType = "string"
TThostFtdcCorrectSerialType = "int"
TThostFtdcFutureSerialType = "int"
TThostFtdcApplicationIDType = "int"
TThostFtdcBankProxyIDType = "int"
TThostFtdcFBTCoreIDType = "int"
TThostFtdcServerPortType = "int"
TThostFtdcRepealedTimesType = "int"
TThostFtdcRepealTimeIntervalType = "int"
TThostFtdcTotalTimesType = "int"
TThostFtdcFBTRequestIDType = "int"
TThostFtdcTIDType = "int"
TThostFtdcTradeAmountType = "double"
TThostFtdcCustFeeType = "double"
TThostFtdcFutureFeeType = "double"
TThostFtdcSingleMaxAmtType = "double"
TThostFtdcSingleMinAmtType = "double"
TThostFtdcTotalAmtType = "double"
TThostFtdcCertificationTypeType = "char"
TThostFtdcFileBusinessCodeType = "char"
TThostFtdcCashExchangeCodeType = "char"
TThostFtdcYesNoIndicatorType = "char"
TThostFtdcBanlanceTypeType = "char"
TThostFtdcGenderType = "char"
TThostFtdcFeePayFlagType = "char"
TThostFtdcPassWordKeyTypeType = "char"
TThostFtdcFBTPassWordTypeType = "char"
TThostFtdcFBTEncryModeType = "char"
TThostFtdcBankRepealFlagType = "char"
TThostFtdcBrokerRepealFlagType = "char"
TThostFtdcInstitutionTypeType = "char"
TThostFtdcLastFragmentType = "char"
TThostFtdcBankAccStatusType = "char"
TThostFtdcMoneyAccountStatusType = "char"
TThostFtdcManageStatusType = "char"
TThostFtdcSystemTypeType = "char"
TThostFtdcTxnEndFlagType = "char"
TThostFtdcProcessStatusType = "char"
TThostFtdcCustTypeType = "char"
TThostFtdcFBTTransferDirectionType = "char"
TThostFtdcOpenOrDestroyType = "char"
TThostFtdcAvailabilityFlagType = "char"
TThostFtdcOrganTypeType = "char"
TThostFtdcOrganLevelType = "char"
TThostFtdcProtocalIDType = "char"
TThostFtdcConnectModeType = "char"
TThostFtdcSyncModeType = "char"
TThostFtdcBankAccTypeType = "char"
TThostFtdcFutureAccTypeType = "char"
TThostFtdcOrganStatusType = "char"
TThostFtdcCCBFeeModeType = "char"
TThostFtdcCommApiTypeType = "char"
TThostFtdcServiceIDType = "int"
TThostFtdcServiceLineNoType = "int"
TThostFtdcServiceNameType = "string"
TThostFtdcLinkStatusType = "char"
TThostFtdcCommApiPointerType = "int"
TThostFtdcPwdFlagType = "char"
TThostFtdcSecuAccTypeType = "char"
TThostFtdcTransferStatusType = "char"
TThostFtdcSponsorTypeType = "char"
TThostFtdcReqRspTypeType = "char"
TThostFtdcFBTUserEventTypeType = "char"
TThostFtdcBankIDByBankType = "string"
TThostFtdcBankOperNoType = "string"
TThostFtdcBankCustNoType = "string"
TThostFtdcDBOPSeqNoType = "int"
TThostFtdcTableNameType = "string"
TThostFtdcPKNameType = "string"
TThostFtdcPKValueType = "string"
TThostFtdcDBOperationType = "char"
TThostFtdcSyncFlagType = "char"
TThostFtdcTargetIDType = "string"
TThostFtdcSyncTypeType = "char"
TThostFtdcFBETimeType = "string"
TThostFtdcFBEBankNoType = "string"
TThostFtdcFBECertNoType = "string"
TThostFtdcExDirectionType = "char"
TThostFtdcFBEBankAccountType = "string"
TThostFtdcFBEBankAccountNameType = "string"
TThostFtdcFBEAmtType = "double"
TThostFtdcFBEBusinessTypeType = "string"
TThostFtdcFBEPostScriptType = "string"
TThostFtdcFBERemarkType = "string"
TThostFtdcExRateType = "double"
TThostFtdcFBEResultFlagType = "char"
TThostFtdcFBERtnMsgType = "string"
TThostFtdcFBEExtendMsgType = "string"
TThostFtdcFBEBusinessSerialType = "string"
TThostFtdcFBESystemSerialType = "string"
TThostFtdcFBETotalExCntType = "int"
TThostFtdcFBEExchStatusType = "char"
TThostFtdcFBEFileFlagType = "char"
TThostFtdcFBEAlreadyTradeType = "char"
TThostFtdcFBEOpenBankType = "string"
TThostFtdcFBEUserEventTypeType = "char"
TThostFtdcFBEFileNameType = "string"
TThostFtdcFBEBatchSerialType = "string"
TThostFtdcFBEReqFlagType = "char"
TThostFtdcNotifyClassType = "char"
TThostFtdcRiskNofityInfoType = "string"
TThostFtdcForceCloseSceneIdType = "string"
TThostFtdcForceCloseTypeType = "char"
TThostFtdcInstrumentIDsType = "string"
TThostFtdcRiskNotifyMethodType = "char"
TThostFtdcRiskNotifyStatusType = "char"
TThostFtdcRiskUserEventType = "char"
TThostFtdcParamIDType = "int"
TThostFtdcParamNameType = "string"
TThostFtdcParamValueType = "string"
TThostFtdcConditionalOrderSortTypeType = "char"
TThostFtdcSendTypeType = "char"
TThostFtdcClientIDStatusType = "char"
TThostFtdcIndustryIDType = "string"
TThostFtdcQuestionIDType = "string"
TThostFtdcQuestionContentType = "string"
TThostFtdcOptionIDType = "string"
TThostFtdcOptionContentType = "string"
TThostFtdcQuestionTypeType = "char"
TThostFtdcProcessIDType = "string"
TThostFtdcSeqNoType = "int"
TThostFtdcUOAProcessStatusType = "string"
TThostFtdcProcessTypeType = "string"
TThostFtdcBusinessTypeType = "char"
TThostFtdcCfmmcReturnCodeType = "char"
TThostFtdcExReturnCodeType = "int"
TThostFtdcClientTypeType = "char"
TThostFtdcExchangeIDTypeType = "char"
TThostFtdcExClientIDTypeType = "char"
TThostFtdcClientClassifyType = "string"
TThostFtdcUOAOrganTypeType = "string"
TThostFtdcUOACountryCodeType = "string"
TThostFtdcAreaCodeType = "string"
TThostFtdcFuturesIDType = "string"
TThostFtdcCffmcDateType = "string"
TThostFtdcCffmcTimeType = "string"
TThostFtdcNocIDType = "string"
TThostFtdcUpdateFlagType = "char"
TThostFtdcApplyOperateIDType = "char"
TThostFtdcApplyStatusIDType = "char"
TThostFtdcSendMethodType = "char"
TThostFtdcEventTypeType = "string"
TThostFtdcEventModeType = "char"
TThostFtdcUOAAutoSendType = "char"
TThostFtdcQueryDepthType = "int"
TThostFtdcDataCenterIDType = "int"
TThostFtdcFlowIDType = "char"
TThostFtdcCheckLevelType = "char"
TThostFtdcCheckNoType = "int"
TThostFtdcCheckStatusType = "char"
TThostFtdcUsedStatusType = "char"
TThostFtdcRateTemplateNameType = "string"
TThostFtdcPropertyStringType = "string"
TThostFtdcBankAcountOriginType = "char"
TThostFtdcMonthBillTradeSumType = "char"
TThostFtdcFBTTradeCodeEnumType = "char"
TThostFtdcRateTemplateIDType = "string"
TThostFtdcRiskRateType = "string"
TThostFtdcTimestampType = "int"
TThostFtdcInvestorIDRuleNameType = "string"
TThostFtdcInvestorIDRuleExprType = "string"
TThostFtdcLastDriftType = "int"
TThostFtdcLastSuccessType = "int"
TThostFtdcAuthKeyType = "string"
TThostFtdcSerialNumberType = "string"
TThostFtdcOTPTypeType = "char"
TThostFtdcOTPVendorsIDType = "string"
TThostFtdcOTPVendorsNameType = "string"
TThostFtdcOTPStatusType = "char"
TThostFtdcBrokerUserTypeType = "char"
TThostFtdcFutureTypeType = "char"
TThostFtdcFundEventTypeType = "char"
TThostFtdcAccountSourceTypeType = "char"
TThostFtdcCodeSourceTypeType = "char"
TThostFtdcUserRangeType = "char"
TThostFtdcTimeSpanType = "string"
TThostFtdcImportSequenceIDType = "string"
TThostFtdcByGroupType = "char"
TThostFtdcTradeSumStatModeType = "char"
TThostFtdcComTypeType = "int"
TThostFtdcUserProductIDType = "string"
TThostFtdcUserProductNameType = "string"
TThostFtdcUserProductMemoType = "string"
TThostFtdcCSRCCancelFlagType = "string"
TThostFtdcCSRCDateType = "string"
TThostFtdcCSRCInvestorNameType = "string"
TThostFtdcCSRCOpenInvestorNameType = "string"
TThostFtdcCSRCInvestorIDType = "string"
TThostFtdcCSRCIdentifiedCardNoType = "string"
TThostFtdcCSRCClientIDType = "string"
TThostFtdcCSRCBankFlagType = "string"
TThostFtdcCSRCBankAccountType = "string"
TThostFtdcCSRCOpenNameType = "string"
TThostFtdcCSRCMemoType = "string"
TThostFtdcCSRCTimeType = "string"
TThostFtdcCSRCTradeIDType = "string"
TThostFtdcCSRCExchangeInstIDType = "string"
TThostFtdcCSRCMortgageNameType = "string"
TThostFtdcCSRCReasonType = "string"
TThostFtdcIsSettlementType = "string"
TThostFtdcCSRCMoneyType = "double"
TThostFtdcCSRCPriceType = "double"
TThostFtdcCSRCOptionsTypeType = "string"
TThostFtdcCSRCStrikePriceType = "double"
TThostFtdcCSRCTargetProductIDType = "string"
TThostFtdcCSRCTargetInstrIDType = "string"
TThostFtdcCommModelNameType = "string"
TThostFtdcCommModelMemoType = "string"
TThostFtdcExprSetModeType = "char"
TThostFtdcRateInvestorRangeType = "char"
TThostFtdcAgentBrokerIDType = "string"
TThostFtdcDRIdentityIDType = "int"
TThostFtdcDRIdentityNameType = "string"
TThostFtdcDBLinkIDType = "string"
TThostFtdcSyncDataStatusType = "char"
TThostFtdcTradeSourceType = "char"
TThostFtdcFlexStatModeType = "char"
TThostFtdcByInvestorRangeType = "char"
TThostFtdcSRiskRateType = "string"
TThostFtdcSequenceNo12Type = "int"
TThostFtdcPropertyInvestorRangeType = "char"
TThostFtdcFileStatusType = "char"
TThostFtdcFileGenStyleType = "char"
TThostFtdcSysOperModeType = "char"
TThostFtdcSysOperTypeType = "char"
TThostFtdcCSRCDataQueyTypeType = "char"
TThostFtdcFreezeStatusType = "char"
TThostFtdcStandardStatusType = "char"
TThostFtdcCSRCFreezeStatusType = "string"
TThostFtdcRightParamTypeType = "char"
TThostFtdcRightTemplateIDType = "string"
TThostFtdcRightTemplateNameType = "string"
TThostFtdcDataStatusType = "char"
TThostFtdcAMLCheckStatusType = "char"
TThostFtdcAmlDateTypeType = "char"
TThostFtdcAmlCheckLevelType = "char"
TThostFtdcAmlCheckFlowType = "string"
TThostFtdcDataTypeType = "string"
TThostFtdcExportFileTypeType = "char"
TThostFtdcSettleManagerTypeType = "char"
TThostFtdcSettleManagerIDType = "string"
TThostFtdcSettleManagerNameType = "string"
TThostFtdcSettleManagerLevelType = "char"
TThostFtdcSettleManagerGroupType = "char"
TThostFtdcCheckResultMemoType = "string"
TThostFtdcFunctionUrlType = "string"
TThostFtdcAuthInfoType = "string"
TThostFtdcAuthCodeType = "string"
TThostFtdcLimitUseTypeType = "char"
TThostFtdcDataResourceType = "char"
TThostFtdcMarginTypeType = "char"
TThostFtdcActiveTypeType = "char"
TThostFtdcMarginRateTypeType = "char"
TThostFtdcBackUpStatusType = "char"
TThostFtdcInitSettlementType = "char"
TThostFtdcReportStatusType = "char"
TThostFtdcSaveStatusType = "char"
TThostFtdcSettArchiveStatusType = "char"
TThostFtdcCTPTypeType = "char"
TThostFtdcToolIDType = "string"
TThostFtdcToolNameType = "string"
TThostFtdcCloseDealTypeType = "char"
TThostFtdcMortgageFundUseRangeType = "char"
TThostFtdcCurrencyUnitType = "double"
TThostFtdcExchangeRateType = "double"
TThostFtdcSpecProductTypeType = "char"
TThostFtdcFundMortgageTypeType = "char"
TThostFtdcAccountSettlementParamIDType = "char"
TThostFtdcCurrencyNameType = "string"
TThostFtdcCurrencySignType = "string"
TThostFtdcFundMortDirectionType = "char"
TThostFtdcBusinessClassType = "char"
TThostFtdcSwapSourceTypeType = "char"
TThostFtdcCurrExDirectionType = "char"
TThostFtdcCurrencySwapStatusType = "char"
TThostFtdcCurrExchCertNoType = "string"
TThostFtdcBatchSerialNoType = "string"
TThostFtdcReqFlagType = "char"
TThostFtdcResFlagType = "char"
TThostFtdcPageControlType = "string"
TThostFtdcRecordCountType = "int"
TThostFtdcCurrencySwapMemoType = "string"
TThostFtdcExStatusType = "char"
TThostFtdcClientRegionType = "char"
TThostFtdcWorkPlaceType = "string"
TThostFtdcBusinessPeriodType = "string"
TThostFtdcWebSiteType = "string"
TThostFtdcUOAIdCardTypeType = "string"
TThostFtdcClientModeType = "string"
TThostFtdcInvestorFullNameType = "string"
TThostFtdcUOABrokerIDType = "string"
TThostFtdcUOAZipCodeType = "string"
TThostFtdcUOAEMailType = "string"
TThostFtdcOldCityType = "string"
TThostFtdcCorporateIdentifiedCardNoType = "string"
TThostFtdcHasBoardType = "char"
TThostFtdcStartModeType = "char"
TThostFtdcTemplateTypeType = "char"
TThostFtdcLoginModeType = "char"
TThostFtdcPromptTypeType = "char"
TThostFtdcLedgerManageIDType = "string"
TThostFtdcInvestVarietyType = "string"
TThostFtdcBankAccountTypeType = "string"
TThostFtdcLedgerManageBankType = "string"
TThostFtdcCffexDepartmentNameType = "string"
TThostFtdcCffexDepartmentCodeType = "string"
TThostFtdcHasTrusteeType = "char"
TThostFtdcCSRCMemo1Type = "string"
TThostFtdcAssetmgrCFullNameType = "string"
TThostFtdcAssetmgrApprovalNOType = "string"
TThostFtdcAssetmgrMgrNameType = "string"
TThostFtdcAmTypeType = "char"
TThostFtdcCSRCAmTypeType = "string"
TThostFtdcCSRCFundIOTypeType = "char"
TThostFtdcCusAccountTypeType = "char"
TThostFtdcCSRCNationalType = "string"
TThostFtdcCSRCSecAgentIDType = "string"
TThostFtdcLanguageTypeType = "char"
TThostFtdcAmAccountType = "string"
TThostFtdcAssetmgrClientTypeType = "char"
TThostFtdcAssetmgrTypeType = "char"
TThostFtdcUOMType = "string"
TThostFtdcSHFEInstLifePhaseType = "string"
TThostFtdcSHFEProductClassType = "string"
TThostFtdcPriceDecimalType = "string"
TThostFtdcInTheMoneyFlagType = "string"
TThostFtdcCheckInstrTypeType = "char"
TThostFtdcDeliveryTypeType = "char"
TThostFtdcBigMoneyType = "double"
TThostFtdcMaxMarginSideAlgorithmType = "char"
TThostFtdcDAClientTypeType = "char"
TThostFtdcCombinInstrIDType = "string"
TThostFtdcCombinSettlePriceType = "string"
TThostFtdcDCEPriorityType = "int"
TThostFtdcTradeGroupIDType = "int"
TThostFtdcIsCheckPrepaType = "int"
TThostFtdcUOAAssetmgrTypeType = "char"
TThostFtdcDirectionEnType = "char"
TThostFtdcOffsetFlagEnType = "char"
TThostFtdcHedgeFlagEnType = "char"
TThostFtdcFundIOTypeEnType = "char"
TThostFtdcFundTypeEnType = "char"
TThostFtdcFundDirectionEnType = "char"
TThostFtdcFundMortDirectionEnType = "char"
TThostFtdcSwapBusinessTypeType = "string"
TThostFtdcOptionsTypeType = "char"
TThostFtdcStrikeModeType = "char"
TThostFtdcStrikeTypeType = "char"
TThostFtdcApplyTypeType = "char"
TThostFtdcGiveUpDataSourceType = "char"
TThostFtdcExecOrderSysIDType = "string"
TThostFtdcExecResultType = "char"
TThostFtdcStrikeSequenceType = "int"
TThostFtdcStrikeTimeType = "string"
TThostFtdcCombinationTypeType = "char"
TThostFtdcDceCombinationTypeType = "char"
TThostFtdcOptionRoyaltyPriceTypeType = "char"
TThostFtdcBalanceAlgorithmType = "char"
TThostFtdcActionTypeType = "char"
TThostFtdcForQuoteStatusType = "char"
TThostFtdcValueMethodType = "char"
TThostFtdcExecOrderPositionFlagType = "char"
TThostFtdcExecOrderCloseFlagType = "char"
TThostFtdcProductTypeType = "char"
TThostFtdcCZCEUploadFileNameType = "char"
TThostFtdcDCEUploadFileNameType = "char"
TThostFtdcSHFEUploadFileNameType = "char"
TThostFtdcCFFEXUploadFileNameType = "char"
TThostFtdcCombDirectionType = "char"
TThostFtdcStrikeOffsetTypeType = "char"
TThostFtdcReserveOpenAccStasType = "char"
TThostFtdcLoginRemarkType = "string"
TThostFtdcInvestUnitIDType = "string"
TThostFtdcBulletinIDType = "int"
TThostFtdcNewsTypeType = "string"
TThostFtdcNewsUrgencyType = "char"
TThostFtdcAbstractType = "string"
TThostFtdcComeFromType = "string"
TThostFtdcURLLinkType = "string"
TThostFtdcLongIndividualNameType = "string"
TThostFtdcLongFBEBankAccountNameType = "string"
TThostFtdcDateTimeType = "string"
TThostFtdcWeakPasswordSourceType = "char"
TThostFtdcRandomStringType = "string"
TThostFtdcOptSelfCloseFlagType = "char"
TThostFtdcBizTypeType = "char"
TThostFtdcAppTypeType = "char"
TThostFtdcAppIDType = "string"
TThostFtdcSystemInfoLenType = "int"
TThostFtdcAdditionalInfoLenType = "int"
TThostFtdcClientSystemInfoType = "string"
TThostFtdcAdditionalInfoType = "string"
TThostFtdcBase64ClientSystemInfoType = "string"
TThostFtdcBase64AdditionalInfoType = "string"
TThostFtdcCurrentAuthMethodType = "int"
TThostFtdcCaptchaInfoLenType = "int"
TThostFtdcCaptchaInfoType = "string"
TThostFtdcUserTextSeqType = "int"
TThostFtdcHandshakeDataType = "string"
TThostFtdcHandshakeDataLenType = "int"
TThostFtdcCryptoKeyVersionType = "string"
TThostFtdcRsaKeyVersionType = "int"
TThostFtdcSoftwareProviderIDType = "string"
TThostFtdcCollectTimeType = "string"
TThostFtdcQueryFreqType = "int"
TThostFtdcResponseValueType = "char"
TThostFtdcOTCTradeTypeType = "char"
TThostFtdcMatchTypeType = "char"
TThostFtdcOTCTraderIDType = "string"
TThostFtdcRiskValueType = "double"
TThostFtdcIDBNameType = "string"
