"""
告警管理器
提供告警规则管理、通知发送、告警历史记录等功能
"""

import smtplib
import json
import os
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart

from ...config import get_system_config
from ...utils.logger import get_logger
from .system_monitor import MonitorAlert, MonitorLevel


class NotificationType(Enum):
    """通知类型"""
    EMAIL = "邮件"
    LOG = "日志"
    CALLBACK = "回调"
    FILE = "文件"


@dataclass
class AlertRule:
    """告警规则"""
    name: str
    category: str
    condition: str  # 条件表达式
    level: MonitorLevel
    enabled: bool = True
    cooldown_minutes: int = 30  # 冷却时间（分钟）
    notification_types: List[NotificationType] = None
    last_triggered: Optional[datetime] = None
    
    def __post_init__(self):
        if self.notification_types is None:
            self.notification_types = [NotificationType.LOG]


@dataclass
class NotificationConfig:
    """通知配置"""
    email_enabled: bool = False
    smtp_server: str = ""
    smtp_port: int = 587
    smtp_username: str = ""
    smtp_password: str = ""
    sender_email: str = ""
    recipient_emails: List[str] = None
    
    def __post_init__(self):
        if self.recipient_emails is None:
            self.recipient_emails = []


class AlertManager:
    """告警管理器"""
    
    def __init__(self):
        """初始化告警管理器"""
        self.logger = get_logger("AlertManager")
        self.system_config = get_system_config()
        
        # 告警规则
        self.alert_rules: Dict[str, AlertRule] = {}
        
        # 通知配置
        self.notification_config = NotificationConfig()
        
        # 告警历史
        self.alert_history: List[MonitorAlert] = []
        self.max_history_size = 10000
        
        # 回调函数
        self.notification_callbacks: Dict[str, Callable] = {}
        
        # 配置文件
        self.rules_config_file = "alert_rules.json"
        self.notification_config_file = "notification_config.json"
        self.alert_history_file = "alert_history.json"
        
        # 加载配置
        self._load_configurations()
        
        self.logger.info("告警管理器初始化完成")
    
    def add_alert_rule(self, rule: AlertRule) -> bool:
        """添加告警规则"""
        try:
            self.alert_rules[rule.name] = rule
            self._save_alert_rules()
            
            self.logger.info(f"添加告警规则: {rule.name} - {rule.condition}")
            return True
        
        except Exception as e:
            self.logger.error(f"添加告警规则失败: {e}")
            return False
    
    def remove_alert_rule(self, rule_name: str) -> bool:
        """移除告警规则"""
        try:
            if rule_name not in self.alert_rules:
                self.logger.warning(f"告警规则 {rule_name} 不存在")
                return False
            
            del self.alert_rules[rule_name]
            self._save_alert_rules()
            
            self.logger.info(f"移除告警规则: {rule_name}")
            return True
        
        except Exception as e:
            self.logger.error(f"移除告警规则失败: {e}")
            return False
    
    def enable_alert_rule(self, rule_name: str) -> bool:
        """启用告警规则"""
        try:
            if rule_name not in self.alert_rules:
                self.logger.warning(f"告警规则 {rule_name} 不存在")
                return False
            
            self.alert_rules[rule_name].enabled = True
            self._save_alert_rules()
            
            self.logger.info(f"启用告警规则: {rule_name}")
            return True
        
        except Exception as e:
            self.logger.error(f"启用告警规则失败: {e}")
            return False
    
    def disable_alert_rule(self, rule_name: str) -> bool:
        """禁用告警规则"""
        try:
            if rule_name not in self.alert_rules:
                self.logger.warning(f"告警规则 {rule_name} 不存在")
                return False
            
            self.alert_rules[rule_name].enabled = False
            self._save_alert_rules()
            
            self.logger.info(f"禁用告警规则: {rule_name}")
            return True
        
        except Exception as e:
            self.logger.error(f"禁用告警规则失败: {e}")
            return False
    
    def process_alert(self, alert: MonitorAlert) -> bool:
        """处理告警"""
        try:
            # 添加到历史记录
            self.alert_history.append(alert)
            
            # 限制历史记录大小
            if len(self.alert_history) > self.max_history_size:
                self.alert_history.pop(0)
            
            # 检查是否匹配告警规则
            matching_rules = self._find_matching_rules(alert)
            
            # 处理匹配的规则
            for rule in matching_rules:
                if self._should_trigger_rule(rule):
                    self._trigger_alert_rule(rule, alert)
            
            # 保存历史记录
            self._save_alert_history()
            
            return True
        
        except Exception as e:
            self.logger.error(f"处理告警失败: {e}")
            return False
    
    def send_notification(self, alert: MonitorAlert, notification_types: List[NotificationType] = None) -> bool:
        """发送通知"""
        try:
            if notification_types is None:
                notification_types = [NotificationType.LOG]
            
            success = True
            
            for notification_type in notification_types:
                try:
                    if notification_type == NotificationType.EMAIL:
                        self._send_email_notification(alert)
                    elif notification_type == NotificationType.LOG:
                        self._send_log_notification(alert)
                    elif notification_type == NotificationType.FILE:
                        self._send_file_notification(alert)
                    elif notification_type == NotificationType.CALLBACK:
                        self._send_callback_notification(alert)
                
                except Exception as e:
                    self.logger.error(f"发送 {notification_type.value} 通知失败: {e}")
                    success = False
            
            return success
        
        except Exception as e:
            self.logger.error(f"发送通知失败: {e}")
            return False
    
    def add_notification_callback(self, name: str, callback: Callable[[MonitorAlert], None]):
        """添加通知回调函数"""
        self.notification_callbacks[name] = callback
        self.logger.debug(f"添加通知回调函数: {name}")
    
    def remove_notification_callback(self, name: str) -> bool:
        """移除通知回调函数"""
        if name in self.notification_callbacks:
            del self.notification_callbacks[name]
            self.logger.debug(f"移除通知回调函数: {name}")
            return True
        return False
    
    def update_notification_config(self, config: NotificationConfig) -> bool:
        """更新通知配置"""
        try:
            self.notification_config = config
            self._save_notification_config()
            
            self.logger.info("通知配置更新成功")
            return True
        
        except Exception as e:
            self.logger.error(f"更新通知配置失败: {e}")
            return False
    
    def get_alert_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """获取告警统计"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_alerts = [alert for alert in self.alert_history if alert.timestamp >= cutoff_time]
            
            # 按级别统计
            level_stats = {}
            for level in MonitorLevel:
                level_stats[level.value] = len([a for a in recent_alerts if a.level == level])
            
            # 按类别统计
            category_stats = {}
            for alert in recent_alerts:
                category_stats[alert.category] = category_stats.get(alert.category, 0) + 1
            
            # 按小时统计
            hourly_stats = {}
            for alert in recent_alerts:
                hour_key = alert.timestamp.strftime('%H:00')
                hourly_stats[hour_key] = hourly_stats.get(hour_key, 0) + 1
            
            return {
                'total_alerts': len(recent_alerts),
                'level_statistics': level_stats,
                'category_statistics': category_stats,
                'hourly_statistics': hourly_stats,
                'time_range': f"最近 {hours} 小时"
            }
        
        except Exception as e:
            self.logger.error(f"获取告警统计失败: {e}")
            return {}
    
    def _find_matching_rules(self, alert: MonitorAlert) -> List[AlertRule]:
        """查找匹配的告警规则"""
        matching_rules = []
        
        for rule in self.alert_rules.values():
            if not rule.enabled:
                continue
            
            # 简单的匹配逻辑（可以扩展为更复杂的条件表达式）
            if rule.category == alert.category or rule.category == "*":
                if rule.level == alert.level or rule.level == MonitorLevel.INFO:
                    matching_rules.append(rule)
        
        return matching_rules
    
    def _should_trigger_rule(self, rule: AlertRule) -> bool:
        """检查是否应该触发规则"""
        if rule.last_triggered is None:
            return True
        
        # 检查冷却时间
        cooldown_time = timedelta(minutes=rule.cooldown_minutes)
        if datetime.now() - rule.last_triggered < cooldown_time:
            return False
        
        return True
    
    def _trigger_alert_rule(self, rule: AlertRule, alert: MonitorAlert):
        """触发告警规则"""
        try:
            # 更新触发时间
            rule.last_triggered = datetime.now()
            
            # 发送通知
            self.send_notification(alert, rule.notification_types)
            
            self.logger.info(f"触发告警规则: {rule.name}")
        
        except Exception as e:
            self.logger.error(f"触发告警规则失败: {e}")
    
    def _send_email_notification(self, alert: MonitorAlert):
        """发送邮件通知"""
        if not self.notification_config.email_enabled:
            return
        
        try:
            # 创建邮件
            msg = MimeMultipart()
            msg['From'] = self.notification_config.sender_email
            msg['To'] = ', '.join(self.notification_config.recipient_emails)
            msg['Subject'] = f"[{alert.level.value}] 系统告警 - {alert.category}"
            
            # 邮件内容
            body = f"""
系统告警通知

告警级别: {alert.level.value}
告警类别: {alert.category}
告警时间: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
告警消息: {alert.message}

详细信息:
{json.dumps(alert.details, ensure_ascii=False, indent=2) if alert.details else '无'}

请及时处理相关问题。
            """
            
            msg.attach(MimeText(body, 'plain', 'utf-8'))
            
            # 发送邮件
            server = smtplib.SMTP(self.notification_config.smtp_server, self.notification_config.smtp_port)
            server.starttls()
            server.login(self.notification_config.smtp_username, self.notification_config.smtp_password)
            server.send_message(msg)
            server.quit()
            
            self.logger.info("邮件通知发送成功")
        
        except Exception as e:
            self.logger.error(f"发送邮件通知失败: {e}")
    
    def _send_log_notification(self, alert: MonitorAlert):
        """发送日志通知"""
        log_message = f"[告警通知] {alert.level.value} - {alert.category} - {alert.message}"
        
        if alert.level == MonitorLevel.CRITICAL:
            self.logger.critical(log_message)
        elif alert.level == MonitorLevel.ERROR:
            self.logger.error(log_message)
        elif alert.level == MonitorLevel.WARNING:
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
    
    def _send_file_notification(self, alert: MonitorAlert):
        """发送文件通知"""
        try:
            alert_file = f"alerts_{datetime.now().strftime('%Y%m%d')}.txt"
            
            with open(alert_file, 'a', encoding='utf-8') as f:
                f.write(f"{alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')} "
                       f"[{alert.level.value}] {alert.category} - {alert.message}\n")
        
        except Exception as e:
            self.logger.error(f"写入告警文件失败: {e}")
    
    def _send_callback_notification(self, alert: MonitorAlert):
        """发送回调通知"""
        for name, callback in self.notification_callbacks.items():
            try:
                callback(alert)
            except Exception as e:
                self.logger.error(f"回调通知 {name} 执行失败: {e}")
    
    def _load_configurations(self):
        """加载配置"""
        try:
            # 加载告警规则
            if os.path.exists(self.rules_config_file):
                with open(self.rules_config_file, 'r', encoding='utf-8') as f:
                    rules_data = json.load(f)
                    for rule_data in rules_data:
                        rule = AlertRule(**rule_data)
                        self.alert_rules[rule.name] = rule
            
            # 加载通知配置
            if os.path.exists(self.notification_config_file):
                with open(self.notification_config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    self.notification_config = NotificationConfig(**config_data)
            
            # 加载告警历史
            if os.path.exists(self.alert_history_file):
                with open(self.alert_history_file, 'r', encoding='utf-8') as f:
                    history_data = json.load(f)
                    for alert_data in history_data:
                        alert = MonitorAlert(**alert_data)
                        self.alert_history.append(alert)
        
        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
    
    def _save_alert_rules(self):
        """保存告警规则"""
        try:
            rules_data = []
            for rule in self.alert_rules.values():
                rules_data.append(asdict(rule))
            
            with open(self.rules_config_file, 'w', encoding='utf-8') as f:
                json.dump(rules_data, f, ensure_ascii=False, indent=2, default=str)
        
        except Exception as e:
            self.logger.error(f"保存告警规则失败: {e}")
    
    def _save_notification_config(self):
        """保存通知配置"""
        try:
            with open(self.notification_config_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(self.notification_config), f, ensure_ascii=False, indent=2)
        
        except Exception as e:
            self.logger.error(f"保存通知配置失败: {e}")
    
    def _save_alert_history(self):
        """保存告警历史"""
        try:
            # 只保存最近的告警历史
            recent_alerts = self.alert_history[-1000:] if len(self.alert_history) > 1000 else self.alert_history
            
            history_data = []
            for alert in recent_alerts:
                history_data.append(asdict(alert))
            
            with open(self.alert_history_file, 'w', encoding='utf-8') as f:
                json.dump(history_data, f, ensure_ascii=False, indent=2, default=str)
        
        except Exception as e:
            self.logger.error(f"保存告警历史失败: {e}")
    
    def display_alert_summary(self):
        """显示告警摘要"""
        stats = self.get_alert_statistics(24)
        
        self.logger.info("=" * 80)
        self.logger.info("告警管理摘要")
        self.logger.info("=" * 80)
        
        self.logger.info(f"告警规则数量: {len(self.alert_rules)}")
        enabled_rules = sum(1 for rule in self.alert_rules.values() if rule.enabled)
        self.logger.info(f"启用规则: {enabled_rules}, 禁用规则: {len(self.alert_rules) - enabled_rules}")
        
        self.logger.info(f"\n{stats['time_range']}告警统计:")
        self.logger.info(f"总告警数: {stats['total_alerts']}")
        
        if stats['level_statistics']:
            self.logger.info("\n按级别统计:")
            for level, count in stats['level_statistics'].items():
                self.logger.info(f"{level}: {count}")
        
        if stats['category_statistics']:
            self.logger.info("\n按类别统计:")
            for category, count in stats['category_statistics'].items():
                self.logger.info(f"{category}: {count}")


if __name__ == "__main__":
    # 测试告警管理器
    alert_manager = AlertManager()
    
    # 添加告警规则
    rule = AlertRule(
        name="CPU高使用率",
        category="系统资源",
        condition="cpu_percent > 80",
        level=MonitorLevel.WARNING,
        notification_types=[NotificationType.LOG, NotificationType.FILE]
    )
    alert_manager.add_alert_rule(rule)
    
    # 创建测试告警
    test_alert = MonitorAlert(
        timestamp=datetime.now(),
        level=MonitorLevel.WARNING,
        category="系统资源",
        message="CPU使用率过高: 85.5%",
        details={"cpu_percent": 85.5}
    )
    
    # 处理告警
    alert_manager.process_alert(test_alert)
    
    # 显示告警摘要
    alert_manager.display_alert_summary()
    
    print("告警管理器测试完成")
