import os
import pandas as pd
import numpy as np
from datetime import datetime
from pathlib import Path
import json
import sys
import gc
import weakref
from io import StringIO
from contextlib import redirect_stdout, redirect_stderr
from typing import Dict, List, Tuple, Any, Optional
import hashlib
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
from multiprocessing import cpu_count
import threading
import time

from vnpy.app.cta_strategy.backtesting import OptimizationSetting, BacktestingEngine
from vnpy.trader.constant import Interval

# 导入自定义模块
from multi_symbol_backtest import MultiSymbolBacktest
from contract_info import ContractInfo

# 导入策略
from vnpy.app.cta_strategy.strategies.fenzhouqiplus_strategy import FenZhouQiPlusStrategy


class MemoryManager:
    """内存管理器 - 负责数据缓存、内存清理和回测引擎管理"""
    
    def __init__(self, max_cache_size: int = 50):
        """初始化内存管理器
        
        参数:
            max_cache_size: 最大缓存数据集数量
        """
        self.data_cache = {}  # 数据缓存 {cache_key: data}
        self.cache_access_time = {}  # 缓存访问时间
        self.max_cache_size = max_cache_size
        self.engine_pool = []  # 回测引擎对象池
        self.used_engines = set()  # 正在使用的引擎ID
        
    def generate_cache_key(self, symbol: str, interval: str, start: datetime, end: datetime) -> str:
        """生成数据缓存键"""
        key_str = f"{symbol}_{interval}_{start.strftime('%Y%m%d')}_{end.strftime('%Y%m%d')}"
        return hashlib.md5(key_str.encode()).hexdigest()[:16]
    
    def get_cached_data(self, symbol: str, interval: str, start: datetime, end: datetime):
        """获取缓存的数据"""
        cache_key = self.generate_cache_key(symbol, interval, start, end)
        if cache_key in self.data_cache:
            self.cache_access_time[cache_key] = datetime.now()
            return self.data_cache[cache_key]
        return None
    
    def cache_data(self, symbol: str, interval: str, start: datetime, end: datetime, data):
        """缓存数据"""
        cache_key = self.generate_cache_key(symbol, interval, start, end)
        
        # 检查缓存大小，如果超限制则清理最久未访问的数据
        if len(self.data_cache) >= self.max_cache_size:
            self._clean_old_cache()
        
        self.data_cache[cache_key] = data
        self.cache_access_time[cache_key] = datetime.now()
    
    def _clean_old_cache(self):
        """清理最久未访问的缓存数据"""
        if not self.cache_access_time:
            return
            
        # 找到最久未访问的缓存
        oldest_key = min(self.cache_access_time.keys(), 
                        key=lambda k: self.cache_access_time[k])
        
        # 删除最久的缓存
        if oldest_key in self.data_cache:
            del self.data_cache[oldest_key]
        if oldest_key in self.cache_access_time:
            del self.cache_access_time[oldest_key]
        
        # 强制垃圾回收
        gc.collect()
    
    def get_engine(self) -> BacktestingEngine:
        """从对象池获取回测引擎 - 增强版本，增加污染检测"""
        # 重要修复：为了完全避免数据污染，直接创建新引擎而不复用
        # 虽然内存开销更大，但能确保数据隔离
        engine = self.create_fresh_engine()
        
        # 验证新引擎状态
        if not self.validate_engine_state(engine):
            print("⚠️  新创建的引擎状态异常，强制清理...")
            del engine
            gc.collect()
            engine = BacktestingEngine()  # 重新创建
        
        return engine
    
    def return_engine(self, engine: BacktestingEngine):
        """归还回测引擎 - 增强版本，直接销毁避免状态污染"""
        # 重要修复：不再复用引擎，直接销毁避免任何可能的状态污染
        try:
            # 彻底清理引擎状态
            self._thorough_engine_cleanup(engine)
            
            # 强制删除引擎引用
            del engine
            
            # 立即垃圾回收
            gc.collect()
            
        except Exception as e:
            print(f"⚠️  引擎销毁过程中出错: {str(e)}")
            gc.collect()
    
    def _thorough_engine_cleanup(self, engine: BacktestingEngine):
        """彻底清理引擎 - 比_reset_engine更彻底的清理"""
        try:
            # === 第一层：暴力清理所有属性 ===
            if hasattr(engine, '__dict__'):
                attrs_to_clear = list(engine.__dict__.keys())
                for attr in attrs_to_clear:
                    try:
                        setattr(engine, attr, None)
                    except:
                        pass
            
            # === 第二层：清理可能的隐藏引用 ===
            for attr_name in dir(engine):
                if not attr_name.startswith('__'):  # 保留系统属性
                    try:
                        attr_value = getattr(engine, attr_name)
                        if hasattr(attr_value, 'clear'):
                            attr_value.clear()
                        elif isinstance(attr_value, (list, dict, set)):
                            attr_value.clear()
                    except:
                        pass
            
            # === 第三层：强制垃圾回收 ===
            import gc
            gc.collect()
            
        except Exception as e:
            # 如果清理失败，记录但不抛出异常
            print(f"引擎清理警告: {str(e)}")

    def create_fresh_engine(self) -> BacktestingEngine:
        """创建全新的回测引擎，避免任何可能的状态污染"""
        # 重要修复：为了完全避免数据污染，每次都创建全新的引擎实例
        engine = BacktestingEngine()
        
        # 设置引擎唯一标识，便于追踪和调试
        engine._creation_time = datetime.now()
        engine._unique_id = hashlib.md5(f"{id(engine)}_{engine._creation_time}".encode()).hexdigest()[:8]
        
        return engine
    
    def validate_engine_state(self, engine: BacktestingEngine) -> bool:
        """验证引擎状态是否干净，检测数据污染"""
        try:
            # 检查关键状态是否为初始状态
            contamination_indicators = [
                hasattr(engine, 'strategy') and engine.strategy is not None,
                hasattr(engine, 'history_data') and engine.history_data,
                hasattr(engine, 'trades') and engine.trades,
                hasattr(engine, 'orders') and engine.orders,
                hasattr(engine, 'daily_results') and engine.daily_results,
                hasattr(engine, 'datetime') and engine.datetime is not None,
                hasattr(engine, 'start') and engine.start is not None,
                hasattr(engine, 'end') and engine.end is not None,
            ]
            
            # 如果任何指标显示有污染，返回False
            is_contaminated = any(contamination_indicators)
            
            if is_contaminated:
                print(f"⚠️  警告：检测到引擎状态污染，引擎ID: {getattr(engine, '_unique_id', 'unknown')}")
                # 记录污染详情
                for i, indicator in enumerate(contamination_indicators):
                    if indicator:
                        attr_names = ['strategy', 'history_data', 'trades', 'orders', 'daily_results', 'datetime', 'start', 'end']
                        print(f"   污染属性: {attr_names[i]}")
            
            return not is_contaminated
            
        except Exception as e:
            print(f"⚠️  引擎状态验证失败: {str(e)}")
            return False
    
    def clear_all(self):
        """清理所有缓存和引擎池"""
        self.data_cache.clear()
        self.cache_access_time.clear()
        self.engine_pool.clear()
        self.used_engines.clear()
        gc.collect()
    
    def get_memory_info(self) -> Dict:
        """获取内存使用信息"""
        return {
            'cached_datasets': len(self.data_cache),
            'engine_pool_size': len(self.engine_pool),
            'active_engines': len(self.used_engines),
            'available_engines': len(self.engine_pool) - len(self.used_engines)
        }


class OptimizedMultiSymbolBacktest(MultiSymbolBacktest):
    """优化的多合约回测类 - 支持内存管理和引擎复用，但禁用数据缓存避免污染"""
    
    def __init__(self, memory_manager: MemoryManager):
        super().__init__()
        self.memory_manager = memory_manager
        
    def run_single_backtest(self,
                          strategy_class,
                          symbol: str,
                          interval: str,
                          start: datetime,
                          end: datetime,
                          rate: float,
                          slippage: float,
                          size: float,
                          pricetick: float,
                          capital: int,
                          setting: dict = None):
        """运行单个合约的回测 - 优化版本，完全避免数据缓存污染"""
        
        # 从引擎池获取回测引擎
        engine = self.memory_manager.get_engine()
        
        try:
            # 确保参数完全隔离，避免不同回测间的参数污染
            isolated_setting = setting.copy() if setting else {}
            
            # 生成参数哈希，确保不同参数组合不会使用相同的缓存数据
            params_str = json.dumps(isolated_setting, sort_keys=True, default=str)
            params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
            
            # 生成唯一的回测标识，确保结果隔离
            backtest_id = f"{strategy_class.__name__}_{symbol}_{interval}_{start.strftime('%Y%m%d')}_{end.strftime('%Y%m%d')}_{params_hash}"
            
            # 设置回测参数 - 每次都重新设置，确保参数隔离
            engine.set_parameters(
                vt_symbol=symbol,
                interval=interval,
                start=start,
                end=end,
                rate=rate,
                slippage=slippage,
                size=size,
                pricetick=pricetick,
                capital=capital
            )
            
            # 添加策略 - 每次创建新的策略实例，避免策略状态污染
            engine.add_strategy(strategy_class, isolated_setting)
            
            # 重要修复：完全禁用数据缓存，每次都重新加载数据
            # 这确保了不同参数组合不会意外使用相同的缓存数据
            engine.load_data()
            
            # 运行回测
            engine.run_backtesting()
            
            # 计算结果
            df = engine.calculate_result()
            stats = engine.calculate_statistics(output=False)
            
            # 为结果添加唯一标识，确保结果可追溯
            stats['backtest_id'] = backtest_id
            stats['params_hash'] = params_hash
            stats['timestamp'] = datetime.now().isoformat()
            
            return df, stats
            
        finally:
            # 归还引擎到对象池（实际上是销毁）
            self.memory_manager.return_engine(engine)


def run_single_symbol_backtest(symbol: str, 
                               param_set_name: str,
                               param_set: Dict,
                               rate: float,
                               size: float, 
                               pricetick: float,
                               start: datetime,
                               end: datetime,
                               interval: str = "1m",
                               slippage: float = 0,
                               capital: float = 50000) -> Dict:
    """并行回测的独立函数 - 单个品种的回测
    
    这个函数设计为可以被多进程调用，因此需要在函数内部创建所有必需的对象
    
    参数:
        symbol: 品种代码
        param_set_name: 参数组合名称
        param_set: 参数组合字典
        rate: 手续费率
        size: 合约乘数
        pricetick: 价格精度
        start: 开始时间
        end: 结束时间
        interval: K线周期
        slippage: 滑点
        capital: 初始资金
        
    返回:
        Dict: 回测结果字典
    """
    try:
        # 创建独立的回测器实例（每个进程都有自己的实例）
        from multi_symbol_backtest import MultiSymbolBacktest
        from vnpy.app.cta_strategy.strategies.fenzhouqiplus_strategy import FenZhouQiPlusStrategy
        
        backtester = MultiSymbolBacktest()
        
        # 处理参数 - 每次都创建全新的参数副本
        original_params = param_set['params'].copy()
        modified_params = {}
        
        # 需要乘以pricetick的参数列表
        price_related_params = ["ping_zy", "zy"]
        # 需要乘以size的参数列表  
        size_related_params = ["contract_multiplier"]
        
        for param_name, param_value in original_params.items():
            # 对价格相关参数乘以pricetick
            if param_name in price_related_params:
                modified_params[param_name] = param_value * pricetick
            # 对合约乘数相关参数乘以size
            elif param_name in size_related_params:
                modified_params[param_name] = size
            # 其他参数保持不变
            else:
                modified_params[param_name] = param_value
        
        # 创建完全独立的参数集
        isolated_params = {k: v for k, v in modified_params.items()}
        
        # 抑制输出的回测
        with redirect_stdout(StringIO()), redirect_stderr(StringIO()):
            df, stats = backtester.run_single_backtest(
                strategy_class=FenZhouQiPlusStrategy,
                symbol=symbol,
                interval=interval,
                start=start,
                end=end,
                rate=rate,
                slippage=slippage,
                size=size,
                pricetick=pricetick,
                capital=capital,
                setting=isolated_params
            )
        
        # 构建结果字典
        result_stats = stats.copy()
        result_stats['parameter_set'] = param_set_name
        result_stats['parameter_set_description'] = param_set['description']
        result_stats['symbol'] = symbol
        result_stats['start_date'] = start.strftime('%Y-%m-%d')
        result_stats['end_date'] = end.strftime('%Y-%m-%d')
        
        # 添加原始参数详情
        for param_name, param_value in param_set['params'].items():
            result_stats[f'param_{param_name}'] = param_value
        
        # 添加调整后的实际参数值
        for param_name, param_value in modified_params.items():
            if param_name in price_related_params:
                result_stats[f'actual_{param_name}'] = param_value
        
        result_stats['process_status'] = 'success'
        result_stats['process_id'] = os.getpid()
        result_stats['thread_id'] = threading.get_ident()
        
        return result_stats
        
    except Exception as e:
        # 返回错误结果
        error_result = {
            'parameter_set': param_set_name,
            'parameter_set_description': param_set['description'],
            'symbol': symbol,
            'start_date': start.strftime('%Y-%m-%d'),
            'end_date': end.strftime('%Y-%m-%d'),
            'error': str(e),
            'total_return': np.nan,
            'annual_return': np.nan,
            'max_drawdown': np.nan,
            'sharpe_ratio': np.nan,
            'process_status': 'error',
            'process_id': os.getpid(),
            'thread_id': threading.get_ident(),
        }
        return error_result


class ParameterSetEvaluator:
    """参数组合评估器 - 设计不同风格的参数组合，批量回测所有品种"""
    
    def __init__(self, result_dir=None, suppress_output=True, enable_memory_optimization=True, enable_parallel=True, max_workers=None):
        """初始化
        
        参数:
            result_dir: 结果保存目录
            suppress_output: 是否抑制策略输出，默认True
            enable_memory_optimization: 是否启用内存优化，默认True
            enable_parallel: 是否启用并行处理，默认True
            max_workers: 最大并行工作进程数，默认为CPU核心数
        """
        if result_dir is None:
            self.result_dir = os.path.join(os.getcwd(), "parameter_evaluation_results")
        else:
            self.result_dir = result_dir
        os.makedirs(self.result_dir, exist_ok=True)
        
        self.timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.all_results = []
        self.suppress_output = suppress_output
        
        # 数据泄露防护：强制禁用内存优化，避免任何可能的缓存污染
        self.enable_memory_optimization = False  # 强制禁用，不管传入参数
        if enable_memory_optimization:
            print("⚠️  为防止数据泄露，已强制禁用内存优化")
        
        # 并行处理配置
        self.enable_parallel = enable_parallel
        self.max_workers = max_workers if max_workers is not None else min(cpu_count(), 8)  # 限制最大进程数，避免过度消耗资源
        
        print(f"🛡️  数据隔离配置:")
        print(f"   内存优化: {'禁用（防泄露）' if not self.enable_memory_optimization else '启用'}")
        print(f"   批量优化: {'启用' if self.enable_parallel else '禁用'}")
        print(f"   CPU核心数: {cpu_count()}")
        print(f"   批处理大小: {self.max_workers}")
        print(f"   处理模式: {'批量优化模式' if self.enable_parallel else '单线程模式'}")
        
        # 初始化内存管理器
        if self.enable_memory_optimization:
            self.memory_manager = MemoryManager(max_cache_size=100)
        else:
            self.memory_manager = None
            
        # 数据泄露检测计数器
        self.backtest_count = 0
        self.leak_detection_interval = 50  # 每50次回测检测一次
        
    def design_parameter_sets(self) -> Dict[str, Dict]:
        """设计不同风格的参数组合 - 细化网格版本
        
        返回:
            Dict[str, Dict]: 参数组合字典，key为风格名称，value为参数字典
        """
        # 基础参数默认值（从FenZhouQiPlusStrategy获取）
        base_params = {
            "k_1": 1,
            "k_3": 3,
            "k_5": 5,
            "k_15": 15,
            "k_30": 30,
            "atr_window": 30,
            "macd_boll_count_fz": 0.05,
            "dk_fz": 0.9,
            "lots": 1,
            "donchian_period": 20,
            "use_trailing_stop": True,
            "AF": 0.002,
            "AF_max": 0.2,
            "trailing_start_ratio": 0.5,
            "daily_loss_limit": 1000,
            "contract_multiplier": 10,
        }
        
        parameter_sets = {
            # ==================== K线组合 (1,3,5) ====================
            
            "ultra_conservative_1_3_5": {
                "name": "超保守型(1,3,5)",
                "description": "极低风险型，K线组合(1,3,5)，追求资金安全，最小回撤",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 1,
                    "k_3": 3,
                    "k_5": 5,
                    "sl_multiplier": 3,       # 极小止损倍数
                    "zy": 8,                  # 极小止盈目标
                    "ping_zy": 4,             # 极小保本止盈
                }
            },
            
            "conservative_1_3_5": {
                "name": "保守型(1,3,5)",
                "description": "低风险稳健型，K线组合(1,3,5)，追求稳定收益，较小回撤",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 1,
                    "k_3": 3,
                    "k_5": 5,
                    "sl_multiplier": 4,       # 较小止损倍数
                    "zy": 10,                 # 较小止盈目标
                    "ping_zy": 5,             # 较小保本止盈
                }
            },
            
            "moderate_1_3_5": {
                "name": "温和型(1,3,5)",
                "description": "温和风险型，K线组合(1,3,5)，稳健中追求适度收益",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 1,
                    "k_3": 3,  # 修复：k_2 -> k_3
                    "k_5": 5,  # 修复：k_3 -> k_5
                    "sl_multiplier": 5,       # 温和止损倍数
                    "zy": 15,                 # 温和止盈目标
                    "ping_zy": 8,             # 温和保本止盈
                }
            },

            "balanced_1_3_5": {
                "name": "平衡型(1,3,5)",
                "description": "风险收益平衡型，K线组合(1,3,5)，追求稳定的风险调整后收益",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 1,
                    "k_3": 3,  # 修复：k_2 -> k_3
                    "k_5": 5,  # 修复：k_3 -> k_5
                    "sl_multiplier": 6,       # 中等止损倍数
                    "zy": 20,                 # 中等止盈目标
                    "ping_zy": 10,            # 中等保本止盈
                }
            },
            
            "active_1_3_5": {
                "name": "积极型(1,3,5)",
                "description": "积极收益型，K线组合(1,3,5)，追求较高收益，承受适度风险",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 1,
                    "k_3": 3,  # 修复：k_2 -> k_3
                    "k_5": 5,  # 修复：k_3 -> k_5
                    "sl_multiplier": 7,       # 积极止损倍数
                    "zy": 30,                 # 积极止盈目标
                    "ping_zy": 15,            # 积极保本止盈
                }
            },

            "aggressive_1_3_5": {
                "name": "激进型(1,3,5)",
                "description": "高风险高收益型，K线组合(1,3,5)，追求最大收益，容忍较大回撤",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 1,
                    "k_3": 3,  # 修复：k_2 -> k_3
                    "k_5": 5,  # 修复：k_3 -> k_5
                    "sl_multiplier": 8,       # 较大止损倍数
                    "zy": 40,                 # 较大止盈目标
                    "ping_zy": 20,            # 较大保本止盈
                }
            },
            
            "ultra_aggressive_1_3_5": {
                "name": "超激进型(1,3,5)",
                "description": "极高风险型，K线组合(1,3,5)，追求极大收益，容忍极大回撤",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 1,
                    "k_3": 3,  # 修复：k_2 -> k_3
                    "k_5": 5,  # 修复：k_3 -> k_5
                    "sl_multiplier": 10,      # 极大止损倍数
                    "zy": 50,                 # 极大止盈目标
                    "ping_zy": 25,            # 极大保本止盈
                }
            },

            # ==================== K线组合 (2,5,8) ====================
            
            "conservative_2_5_8": {
                "name": "保守型(2,5,8)",
                "description": "低风险稳健型，K线组合(2,5,8)，追求稳定收益",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 2,
                    "k_3": 5,  # 修复：原k_2 -> k_3
                    "k_5": 8,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 4,
                    "zy": 12,
                    "ping_zy": 6,
                }
            },
            
            "balanced_2_5_8": {
                "name": "平衡型(2,5,8)",
                "description": "风险收益平衡型，K线组合(2,5,8)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 2,
                    "k_3": 5,  # 修复：原k_2 -> k_3
                    "k_5": 8,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 6,
                    "zy": 22,
                    "ping_zy": 11,
                }
            },
            
            "aggressive_2_5_8": {
                "name": "激进型(2,5,8)",
                "description": "高风险高收益型，K线组合(2,5,8)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 2,
                    "k_3": 5,  # 修复：原k_2 -> k_3
                    "k_5": 8,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 8,
                    "zy": 35,
                    "ping_zy": 18,
                }
            },

            # ==================== K线组合 (3,5,10) ====================
            
            "ultra_conservative_3_5_10": {
                "name": "超保守型(3,5,10)",
                "description": "极低风险型，K线组合(3,5,10)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 3,
                    "k_3": 5,  # 修复：原k_2 -> k_3
                    "k_5": 10,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 3,
                    "zy": 8,
                    "ping_zy": 4,
                }
            },

            "conservative_3_5_10": {
                "name": "保守型(3,5,10)",
                "description": "低风险稳健型，K线组合(3,5,10)，追求稳定收益，较小回撤",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 3,
                    "k_3": 5,  # 修复：原k_2 -> k_3
                    "k_5": 10,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 4,
                    "zy": 10,
                    "ping_zy": 5,
                }
            },
            
            "moderate_3_5_10": {
                "name": "温和型(3,5,10)",
                "description": "温和风险型，K线组合(3,5,10)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 3,
                    "k_3": 5,  # 修复：原k_2 -> k_3
                    "k_5": 10,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 5,
                    "zy": 15,
                    "ping_zy": 8,
                }
            },

            "balanced_3_5_10": {
                "name": "平衡型(3,5,10)",
                "description": "风险收益平衡型，K线组合(3,5,10)，追求稳定的风险调整后收益",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 3,
                    "k_3": 5,  # 修复：原k_2 -> k_3
                    "k_5": 10,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 6,
                    "zy": 20,
                    "ping_zy": 10,
                }
            },
            
            "active_3_5_10": {
                "name": "积极型(3,5,10)",
                "description": "积极收益型，K线组合(3,5,10)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 3,
                    "k_3": 5,  # 修复：原k_2 -> k_3
                    "k_5": 10,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 7,
                    "zy": 30,
                    "ping_zy": 15,
                }
            },

            "aggressive_3_5_10": {
                "name": "激进型(3,5,10)",
                "description": "高风险高收益型，K线组合(3,5,10)，追求最大收益，容忍较大回撤",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 3,
                    "k_3": 5,  # 修复：原k_2 -> k_3
                    "k_5": 10,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 8,
                    "zy": 40,
                    "ping_zy": 20,
                }
            },
            
            "ultra_aggressive_3_5_10": {
                "name": "超激进型(3,5,10)",
                "description": "极高风险型，K线组合(3,5,10)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 3,
                    "k_3": 5,  # 修复：原k_2 -> k_3
                    "k_5": 10,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 10,
                    "zy": 50,
                    "ping_zy": 25,
                }
            },

            # ==================== K线组合 (3,8,15) ====================
            
            "conservative_3_8_15": {
                "name": "保守型(3,8,15)",
                "description": "低风险稳健型，K线组合(3,8,15)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 3,
                    "k_3": 8,  # 修复：原k_2 -> k_3
                    "k_5": 15,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 4,
                    "zy": 15,
                    "ping_zy": 8,
                }
            },
            
            "balanced_3_8_15": {
                "name": "平衡型(3,8,15)",
                "description": "风险收益平衡型，K线组合(3,8,15)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 3,
                    "k_3": 8,  # 修复：原k_2 -> k_3
                    "k_5": 15,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 6,
                    "zy": 25,
                    "ping_zy": 12,
                }
            },
            
            "aggressive_3_8_15": {
                "name": "激进型(3,8,15)",
                "description": "高风险高收益型，K线组合(3,8,15)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 3,
                    "k_3": 8,  # 修复：原k_2 -> k_3
                    "k_5": 15,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 8,
                    "zy": 45,
                    "ping_zy": 22,
                }
            },

            # ==================== K线组合 (5,10,15) ====================
            
            "ultra_conservative_5_10_15": {
                "name": "超保守型(5,10,15)",
                "description": "极低风险型，K线组合(5,10,15)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 5,
                    "k_3": 10,  # 修复：原k_2 -> k_3
                    "k_5": 15,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 3,
                    "zy": 15,
                    "ping_zy": 8,
                }
            },

            "conservative_5_10_15": {
                "name": "保守型(5,10,15)",
                "description": "低风险稳健型，K线组合(5,10,15)，追求稳定收益，较小回撤",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 5,
                    "k_3": 10,  # 修复：原k_2 -> k_3
                    "k_5": 15,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 4,
                    "zy": 20,
                    "ping_zy": 10,
                }
            },
            
            "moderate_5_10_15": {
                "name": "温和型(5,10,15)",
                "description": "温和风险型，K线组合(5,10,15)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 5,
                    "k_3": 10,  # 修复：原k_2 -> k_3
                    "k_5": 15,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 5,
                    "zy": 22,
                    "ping_zy": 12,
                }
            },

            "balanced_5_10_15": {
                "name": "平衡型(5,10,15)",
                "description": "风险收益平衡型，K线组合(5,10,15)，追求稳定的风险调整后收益",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 5,
                    "k_3": 10,  # 修复：原k_2 -> k_3
                    "k_5": 15,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 6,
                    "zy": 25,
                    "ping_zy": 12,
                }
            },
            
            "active_5_10_15": {
                "name": "积极型(5,10,15)",
                "description": "积极收益型，K线组合(5,10,15)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 5,
                    "k_3": 10,  # 修复：原k_2 -> k_3
                    "k_5": 15,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 7,
                    "zy": 35,
                    "ping_zy": 18,
                }
            },

            "aggressive_5_10_15": {
                "name": "激进型(5,10,15)",
                "description": "高风险高收益型，K线组合(5,10,15)，追求最大收益，容忍较大回撤",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 5,
                    "k_3": 10,  # 修复：原k_2 -> k_3
                    "k_5": 15,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 8,
                    "zy": 50,
                    "ping_zy": 25,
                }
            },
            
            "ultra_aggressive_5_10_15": {
                "name": "超激进型(5,10,15)",
                "description": "极高风险型，K线组合(5,10,15)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 5,
                    "k_3": 10,  # 修复：原k_2 -> k_3
                    "k_5": 15,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 10,
                    "zy": 60,
                    "ping_zy": 30,
                }
            },

            # ==================== K线组合 (5,10,20) ====================
            
            "conservative_5_10_20": {
                "name": "保守型(5,10,20)",
                "description": "低风险稳健型，K线组合(5,10,20)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 5,
                    "k_3": 10,  # 修复：原k_2 -> k_3
                    "k_5": 20,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 4,
                    "zy": 20,
                    "ping_zy": 10,
                }
            },
            
            "balanced_5_10_20": {
                "name": "平衡型(5,10,20)",
                "description": "风险收益平衡型，K线组合(5,10,20)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 5,
                    "k_3": 10,  # 修复：原k_2 -> k_3
                    "k_5": 20,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 6,
                    "zy": 30,
                    "ping_zy": 15,
                }
            },
            
            "aggressive_5_10_20": {
                "name": "激进型(5,10,20)",
                "description": "高风险高收益型，K线组合(5,10,20)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 5,
                    "k_3": 10,  # 修复：原k_2 -> k_3
                    "k_5": 20,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 8,
                    "zy": 50,
                    "ping_zy": 25,
                }
            },

            # ==================== K线组合 (5,10,30) ====================
            
            "ultra_conservative_5_10_30": {
                "name": "超保守型(5,10,30)",
                "description": "极低风险型，K线组合(5,10,30)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 5,
                    "k_3": 10,  # 修复：原k_2 -> k_3
                    "k_5": 30,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 3,
                    "zy": 15,
                    "ping_zy": 8,
                }
            },

            "conservative_5_10_30": {
                "name": "保守型(5,10,30)",
                "description": "低风险稳健型，K线组合(5,10,30)，追求稳定收益，较小回撤",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 5,
                    "k_3": 10,  # 修复：原k_2 -> k_3
                    "k_5": 30,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 4,
                    "zy": 20,
                    "ping_zy": 10,
                }
            },
            
            "moderate_5_10_30": {
                "name": "温和型(5,10,30)",
                "description": "温和风险型，K线组合(5,10,30)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 5,
                    "k_3": 10,  # 修复：原k_2 -> k_3
                    "k_5": 30,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 5,
                    "zy": 22,
                    "ping_zy": 12,
                }
            },

            "balanced_5_10_30": {
                "name": "平衡型(5,10,30)",
                "description": "风险收益平衡型，K线组合(5,10,30)，追求稳定的风险调整后收益",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 5,
                    "k_3": 10,  # 修复：原k_2 -> k_3
                    "k_5": 30,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 6,
                    "zy": 25,
                    "ping_zy": 12,
                }
            },
            
            "active_5_10_30": {
                "name": "积极型(5,10,30)",
                "description": "积极收益型，K线组合(5,10,30)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 5,
                    "k_3": 10,  # 修复：原k_2 -> k_3
                    "k_5": 30,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 7,
                    "zy": 35,
                    "ping_zy": 18,
                }
            },

            "aggressive_5_10_30": {
                "name": "激进型(5,10,30)",
                "description": "高风险高收益型，K线组合(5,10,30)，追求最大收益，容忍较大回撤",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 5,
                    "k_3": 10,  # 修复：原k_2 -> k_3
                    "k_5": 30,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 8,
                    "zy": 50,
                    "ping_zy": 25,
                }
            },
            
            "ultra_aggressive_5_10_30": {
                "name": "超激进型(5,10,30)",
                "description": "极高风险型，K线组合(5,10,30)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 5,
                    "k_3": 10,  # 修复：原k_2 -> k_3
                    "k_5": 30,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 10,
                    "zy": 60,
                    "ping_zy": 30,
                }
            },

            # ==================== K线组合 (8,15,30) ====================
            
            "conservative_8_15_30": {
                "name": "保守型(8,15,30)",
                "description": "低风险稳健型，K线组合(8,15,30)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 8,
                    "k_3": 15,  # 修复：原k_2 -> k_3
                    "k_5": 30,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 4,
                    "zy": 25,
                    "ping_zy": 12,
                }
            },
            
            "balanced_8_15_30": {
                "name": "平衡型(8,15,30)",
                "description": "风险收益平衡型，K线组合(8,15,30)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 8,
                    "k_3": 15,  # 修复：原k_2 -> k_3
                    "k_5": 30,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 6,
                    "zy": 35,
                    "ping_zy": 18,
                }
            },
            
            "aggressive_8_15_30": {
                "name": "激进型(8,15,30)",
                "description": "高风险高收益型，K线组合(8,15,30)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 8,
                    "k_3": 15,  # 修复：原k_2 -> k_3
                    "k_5": 30,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 8,
                    "zy": 55,
                    "ping_zy": 28,
                }
            },

            # ==================== K线组合 (10,20,30) ====================
            
            "conservative_10_20_30": {
                "name": "保守型(10,20,30)",
                "description": "低风险稳健型，K线组合(10,20,30)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 10,
                    "k_3": 20,  # 修复：原k_2 -> k_3
                    "k_5": 30,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 4,
                    "zy": 30,
                    "ping_zy": 15,
                }
            },
            
            "balanced_10_20_30": {
                "name": "平衡型(10,20,30)",
                "description": "风险收益平衡型，K线组合(10,20,30)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 10,
                    "k_3": 20,  # 修复：原k_2 -> k_3
                    "k_5": 30,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 6,
                    "zy": 40,
                    "ping_zy": 20,
                }
            },
            
            "aggressive_10_20_30": {
                "name": "激进型(10,20,30)",
                "description": "高风险高收益型，K线组合(10,20,30)",
                "params": {
                    **base_params,  # 包含所有基础参数
                    "k_1": 10,
                    "k_3": 20,  # 修复：原k_2 -> k_3
                    "k_5": 30,  # 修复：原k_3 -> k_5
                    "sl_multiplier": 8,
                    "zy": 60,
                    "ping_zy": 30,
                }
            },

            "wide_range_5_10_30": {
                "name": "宽幅波动型(5,10,30)",
                "description": "适合大波动品种，K线组合(5,10,30)，宽止损宽止盈，捕捉大行情",
                "params": {
                    **base_params,
                    "k_1": 5,
                    "k_3": 10,
                    "k_5": 30,
                    "sl_multiplier": 15,   # 止损倍数大幅提升
                    "zy": 120,             # 止盈目标大幅提升
                    "ping_zy": 60,         # 保本止盈也大幅提升
                }
            },
            "wide_range_10_20_30": {
                "name": "宽幅波动型(10,20,30)",
                "description": "适合极大波动品种，K线组合(10,20,30)，极宽止损止盈，低频大波段",
                "params": {
                    **base_params,
                    "k_1": 10,
                    "k_3": 20,
                    "k_5": 30,
                    "sl_multiplier": 20,
                    "zy": 200,
                    "ping_zy": 100,
                }
            },
            "wide_range_5_15_30": {
                "name": "宽幅波动型(5,15,30)",
                "description": "适合大波动品种，K线组合(5,15,30)，宽止损宽止盈，适合趋势行情",
                "params": {
                    **base_params,
                    "k_1": 5,
                    "k_3": 15,
                    "k_5": 30,
                    "sl_multiplier": 12,
                    "zy": 100,
                    "ping_zy": 50,
                }
            },
            "wide_range_10_20_40": {
                "name": "宽幅波动型(10,20,40)",
                "description": "极宽参数，适合极端大波动品种，低频大波段",
                "params": {
                    **base_params,
                    "k_1": 10,
                    "k_3": 20,
                    "k_5": 40,
                    "sl_multiplier": 25,
                    "zy": 300,
                    "ping_zy": 150,
                }
            },
        }
        
        return parameter_sets
    
    def setup_contracts(self, contract_file="期货全品种手续费保证金.xls", blacklist=None, specific_symbols=None):
        """设置合约信息"""
        if blacklist is None:
            blacklist = ["IF", "IH", "IC", "IM", "T", "TF", "TL"]
            
        # 加载合约信息
        contract_info = ContractInfo(contract_file, blacklist=blacklist)
        
        # 获取888合约列表
        all_symbols = contract_info.get_888_contracts()
        
        # 如果指定了品种，则只保留指定品种
        if specific_symbols is not None:
            symbols = [s for s in all_symbols if s in specific_symbols]
        else:
            symbols = all_symbols
        print(f"要回测的合约列表: {symbols}")
        
        # 获取合约参数
        params = contract_info.get_contract_params()
        rate = {k: v for k, v in params['rate'].items() if k in symbols}
        size = {k: v for k, v in params['size'].items() if k in symbols}
        pricetick = {k: v for k, v in params['pricetick'].items() if k in symbols}
        
        return symbols, rate, size, pricetick
    
    def run_backtest_for_parameter_set(self, 
                                     parameter_set: Dict, 
                                     strategy_class, 
                                     symbols: List[str], 
                                     start: datetime, 
                                     end: datetime) -> Dict:
        """运行指定参数集的回测 - 批处理优化版本"""
        
        print(f"🔄 开始参数组合: {parameter_set['name']}")
        print(f"   参数详情: {parameter_set['params']}")
        
        # 数据泄露检测 - 在每次参数组合开始时检测
        self.periodic_leak_detection()
        
        backtest_results = []
        total_symbols = len(symbols)
        
        if self.enable_parallel:
            # 批处理模式：将品种分成多个批次
            batch_size = self.max_workers
            batches = [symbols[i:i + batch_size] for i in range(0, len(symbols), batch_size)]
            
            print(f"   📦 分为{len(batches)}个批次，每批{batch_size}个品种")
            
            for batch_idx, batch_symbols in enumerate(batches):
                print(f"   🔄 处理批次 {batch_idx + 1}/{len(batches)}: {batch_symbols}")
                
                batch_results = []
                for symbol_idx, symbol in enumerate(batch_symbols):
                    try:
                        # 使用隔离的回测方法，包含资金利用率计算
                        result = self.run_single_symbol_backtest_isolated(
                            strategy_class, symbol, parameter_set['params'], start, end
                        )
                        
                        if result is not None:
                            # 添加参数组合信息
                            result['parameter_set'] = parameter_set['name']
                            result['parameter_set_description'] = parameter_set['description']
                            
                            batch_results.append(result)
                            progress = (batch_idx * batch_size + symbol_idx + 1) / total_symbols * 100
                            print(f"     ✅ {symbol}: 完成 ({progress:.1f}%)")
                        else:
                            print(f"     ❌ {symbol}: 失败")
                            
                    except Exception as e:
                        print(f"     ❌ {symbol}: 错误 - {str(e)}")
                        continue
                
                backtest_results.extend(batch_results)
                
                # 批次间清理
                if batch_idx < len(batches) - 1:  # 不是最后一批
                    print(f"     🧹 批次{batch_idx + 1}完成，执行清理...")
                    self.cleanup_memory()
                    
                    # 强制垃圾回收
                    import gc
                    gc.collect()
        else:
            # 单线程模式
            for i, symbol in enumerate(symbols):
                try:
                    # 使用隔离的回测方法，包含资金利用率计算
                    result = self.run_single_symbol_backtest_isolated(
                        strategy_class, symbol, parameter_set['params'], start, end
                    )
                    
                    if result is not None:
                        # 添加参数组合信息
                        result['parameter_set'] = parameter_set['name']
                        result['parameter_set_description'] = parameter_set['description']
                        
                        backtest_results.append(result)
                        progress = (i + 1) / total_symbols * 100
                        print(f"   ✅ {symbol}: 完成 ({progress:.1f}%)")
                    else:
                        print(f"   ❌ {symbol}: 失败")
                        
                    # 每10个品种执行一次泄露检测
                    if (i + 1) % 10 == 0:
                        self.periodic_leak_detection()
                        
                except Exception as e:
                    print(f"   ❌ {symbol}: 错误 - {str(e)}")
                    continue
        
        # 汇总统计
        if backtest_results:
            summary_stats = self.calculate_portfolio_stats(backtest_results)
            print(f"   📊 参数组合 '{parameter_set['name']}' 完成:")
            print(f"      成功品种: {len(backtest_results)}/{total_symbols}")
            print(f"      总收益率: {summary_stats.get('total_return', 0):.2f}%")
            print(f"      夏普比率: {summary_stats.get('sharpe_ratio', 0):.3f}")
            print(f"      最大回撤: {summary_stats.get('max_drawdown', 0):.2f}%")
            print(f"      平均资金利用率: {summary_stats.get('avg_capital_efficiency', 0):.3f}")
            
            # 保存参数组合信息到统计结果
            summary_stats.update({
                'parameter_set_name': parameter_set['name'],
                'parameters': parameter_set['params'],
                'symbol_count': len(backtest_results),
                'total_symbols': total_symbols,
                'success_rate': len(backtest_results) / total_symbols * 100
            })
            
            return summary_stats
        else:
            print(f"   ❌ 参数组合 '{parameter_set['name']}' 无有效结果")
            return None
    
    def run_backtest_for_parameter_set_parallel(self, param_set_name: str, param_set: Dict, 
                                               symbols: List[str], rate: Dict, size: Dict, 
                                               pricetick: Dict, start: datetime, end: datetime,
                                               interval: str = "1m", slippage: float = 0, 
                                               capital: float = 50000) -> List[Dict]:
        """为单个参数组合运行所有品种的回测 - 并行版本（改用批处理避免线程问题）
        
        使用批处理方式模拟并行效果，避免vnpy框架在多线程环境下的兼容性问题
        
        参数:
            param_set_name: 参数组合名称
            param_set: 参数组合字典
            symbols: 品种列表
            rate: 手续费率字典
            size: 合约乘数字典
            pricetick: 价格精度字典
            start: 开始时间
            end: 结束时间
            interval: K线周期
            slippage: 滑点
            capital: 初始资金
            
        返回:
            List[Dict]: 该参数组合在所有品种上的回测结果列表
        """
        print(f"\n⚡ 开始优化回测参数组合: {param_set_name} - {param_set['description']}")
        print(f"📊 品种数量: {len(symbols)}")
        print(f"🔧 回测模式: 批量优化模式（避免线程冲突）")
        print(f"原始参数: {param_set['params']}")
        
        start_time = time.time()
        results = []
        
        if not self.enable_parallel or len(symbols) <= 1:
            # 回退到串行处理
            print("📝 使用串行处理 (并行已禁用或品种数量太少)")
            return self.run_backtest_for_parameter_set(
                param_set_name, param_set, symbols, rate, size, pricetick,
                start, end, interval, slippage, capital
            )
        
        # 使用批处理方式，避免vnpy的线程问题
        batch_size = max(1, self.max_workers)  # 每批处理的品种数量
        symbol_batches = [symbols[i:i + batch_size] for i in range(0, len(symbols), batch_size)]
        
        print(f"🔄 分批处理: {len(symbol_batches)} 批，每批最多 {batch_size} 个品种")
        
        completed_count = 0
        failed_count = 0
        
        for batch_idx, symbol_batch in enumerate(symbol_batches, 1):
            print(f"\n📦 正在处理第 {batch_idx}/{len(symbol_batches)} 批，包含 {len(symbol_batch)} 个品种...")
            
            # 为当前批次的每个品种创建独立的回测器
            batch_results = []
            
            for symbol in symbol_batch:
                completed_count += 1
                
                try:
                    # 创建全新的回测器实例（避免任何可能的状态污染）
                    backtester = MultiSymbolBacktest()
                    
                    # 处理参数 - 每次都创建全新的参数副本
                    original_params = param_set['params'].copy()
                    modified_params = {}
                    
                    # 需要乘以pricetick的参数列表
                    price_related_params = ["ping_zy", "zy"]
                    # 需要乘以size的参数列表  
                    size_related_params = ["contract_multiplier"]
                    
                    for param_name, param_value in original_params.items():
                        # 对价格相关参数乘以pricetick
                        if param_name in price_related_params:
                            modified_params[param_name] = param_value * pricetick[symbol]
                        # 对合约乘数相关参数乘以size
                        elif param_name in size_related_params:
                            modified_params[param_name] = size[symbol]
                        # 其他参数保持不变
                        else:
                            modified_params[param_name] = param_value
                    
                    # 创建完全独立的参数集
                    isolated_params = {k: v for k, v in modified_params.items()}
                    
                    # 抑制输出的回测
                    if self.suppress_output:
                        with redirect_stdout(StringIO()), redirect_stderr(StringIO()):
                            df, stats = backtester.run_single_backtest(
                                strategy_class=FenZhouQiPlusStrategy,
                                symbol=symbol,
                                interval=interval,
                                start=start,
                                end=end,
                                rate=rate[symbol],
                                slippage=slippage,
                                size=size[symbol],
                                pricetick=pricetick[symbol],
                                capital=capital,
                                setting=isolated_params
                            )
                    else:
                        df, stats = backtester.run_single_backtest(
                            strategy_class=FenZhouQiPlusStrategy,
                            symbol=symbol,
                            interval=interval,
                            start=start,
                            end=end,
                            rate=rate[symbol],
                            slippage=slippage,
                            size=size[symbol],
                            pricetick=pricetick[symbol],
                            capital=capital,
                            setting=isolated_params
                        )
                    
                    # 构建结果字典
                    result_stats = stats.copy()
                    result_stats['parameter_set'] = param_set_name
                    result_stats['parameter_set_description'] = param_set['description']
                    result_stats['symbol'] = symbol
                    result_stats['start_date'] = start.strftime('%Y-%m-%d')
                    result_stats['end_date'] = end.strftime('%Y-%m-%d')
                    
                    # 获取合约参数用于计算资金利用率
                    contract_info = ContractInfo("期货全品种手续费保证金.xls")
                    contract_params = contract_info.get_contract_params()
                    
                    # 添加保证金信息并计算资金利用率
                    margin = contract_params['margin'].get(symbol, 0)
                    result_stats['margin'] = margin
                    
                    # 计算资金利用率 = 总净盈亏/品种保证金
                    if margin and margin > 0:
                        total_net_pnl = result_stats.get('total_net_pnl', 0)
                        result_stats['capital_efficiency'] = total_net_pnl / margin
                    else:
                        result_stats['capital_efficiency'] = 0
                    
                    # 添加原始参数详情
                    for param_name, param_value in param_set['params'].items():
                        result_stats[f'param_{param_name}'] = param_value
                    
                    # 添加调整后的实际参数值
                    for param_name, param_value in modified_params.items():
                        if param_name in price_related_params:
                            result_stats[f'actual_{param_name}'] = param_value
                    
                    # 添加处理状态
                    result_stats['process_status'] = 'success'
                    result_stats['batch_id'] = batch_idx
                    
                    batch_results.append(result_stats)
                    
                    # 实时进度显示
                    progress = completed_count / len(symbols) * 100
                    print(f"    ✓ [{completed_count:3d}/{len(symbols)}] {symbol:12} | "
                          f"进度: {progress:5.1f}% | "
                          f"批次: {batch_idx:2d} | "
                          f"耗时: {time.time() - start_time:.1f}s")
                    
                except Exception as e:
                    failed_count += 1
                    print(f"    ✗ [{completed_count:3d}/{len(symbols)}] {symbol:12} | 错误: {str(e)[:30]}...")
                    
                    # 创建错误结果
                    error_result = {
                        'parameter_set': param_set_name,
                        'parameter_set_description': param_set['description'],
                        'symbol': symbol,
                        'start_date': start.strftime('%Y-%m-%d'),
                        'end_date': end.strftime('%Y-%m-%d'),
                        'error': str(e),
                        'total_return': np.nan,
                        'annual_return': np.nan,
                        'max_drawdown': np.nan,
                        'sharpe_ratio': np.nan,
                        'process_status': 'error',
                        'batch_id': batch_idx,
                    }
                    batch_results.append(error_result)
                
                # 每个品种回测后进行内存清理
                gc.collect()
            
            # 将批次结果添加到总结果中
            results.extend(batch_results)
            
            # 批次完成后的统计
            batch_success = len([r for r in batch_results if r.get('process_status') == 'success'])
            batch_time = time.time() - start_time
            print(f"📦 第 {batch_idx} 批完成: 成功 {batch_success}/{len(symbol_batch)}, 累计耗时 {batch_time:.1f}s")
            
            # 批次间强制垃圾回收
            gc.collect()
        
        # 统计最终结果
        success_count = len([r for r in results if r.get('process_status') == 'success'])
        total_time = time.time() - start_time
        
        # 计算资金利用率平均值
        capital_efficiencies = [r.get('capital_efficiency', 0) for r in results 
                               if r.get('process_status') == 'success' and not pd.isna(r.get('capital_efficiency', np.nan))]
        avg_capital_efficiency = np.mean(capital_efficiencies) if capital_efficiencies else 0
        
        print(f"\n📊 优化回测完成统计:")
        print(f"   参数组合: {param_set_name}")
        print(f"   总品种数: {len(symbols)}")
        print(f"   成功回测: {success_count}")
        print(f"   失败回测: {failed_count}")
        print(f"   成功率: {success_count/len(symbols)*100:.1f}%")
        print(f"   平均资金利用率: {avg_capital_efficiency:.3f}")
        print(f"   总耗时: {total_time:.1f}秒")
        print(f"   平均每品种: {total_time/len(symbols):.1f}秒")
        print(f"   批次数量: {len(symbol_batches)}")
        
        # 强制垃圾回收
        gc.collect()
        
        return results
    
    def calculate_parameter_set_metrics(self, results: List[Dict]) -> Dict:
        """计算参数组合的整体评估指标
        
        参数:
            results: 该参数组合的所有回测结果
            
        返回:
            Dict: 参数组合的整体评估指标
        """
        # 过滤掉有错误的结果
        valid_results = [r for r in results if 'error' not in r and not pd.isna(r.get('total_return', np.nan))]
        
        if not valid_results:
            return {
                'valid_symbols_count': 0,
                'avg_total_return': np.nan,
                'avg_annual_return': np.nan,
                'avg_sharpe_ratio': np.nan,
                'avg_max_drawdown': np.nan,
                'positive_return_ratio': np.nan,
                'total_return_std': np.nan,
                'sharpe_ratio_std': np.nan,
                'max_drawdown_std': np.nan,
                'avg_capital_efficiency': np.nan,  # 新增：平均资金利用率
                'capital_efficiency_std': np.nan,  # 新增：资金利用率标准差
                'robust_score': np.nan,
            }
        
        # 提取关键指标 - 修复：使用正确的字段和数据处理
        total_returns = [r.get('total_return', 0) for r in valid_results]
        annual_returns = [r.get('annual_return', 0) for r in valid_results]
        sharpe_ratios = [r.get('sharpe_ratio', 0) for r in valid_results if not pd.isna(r.get('sharpe_ratio', np.nan))]
        
        # 提取资金利用率
        capital_efficiencies = [r.get('capital_efficiency', 0) for r in valid_results 
                               if not pd.isna(r.get('capital_efficiency', np.nan))]
        
        # 修复：使用max_ddpercent（回撤百分比）而不是max_drawdown（绝对金额）
        # max_ddpercent字段已经是百分比形式（如-1.23表示-1.23%），取绝对值
        max_drawdowns = []
        for r in valid_results:
            dd_percent = r.get('max_ddpercent', 0)
            if not pd.isna(dd_percent):
                max_drawdowns.append(abs(dd_percent))  # 转为正数，单位为%
            else:
                # 备选：如果没有max_ddpercent，尝试用max_drawdown计算
                dd_abs = r.get('max_drawdown', 0)
                capital = r.get('capital', 50000)
                if dd_abs != 0 and capital != 0:
                    dd_percent_calc = abs(dd_abs) / capital * 100
                    max_drawdowns.append(dd_percent_calc)
                else:
                    max_drawdowns.append(0)
        
        # 计算基础统计指标
        metrics = {
            'valid_symbols_count': len(valid_results),
            'avg_total_return': np.mean(total_returns),
            'avg_annual_return': np.mean(annual_returns),
            'avg_sharpe_ratio': np.mean(sharpe_ratios) if sharpe_ratios else np.nan,
            'avg_max_drawdown': np.mean(max_drawdowns),
            'positive_return_ratio': len([r for r in total_returns if r > 0]) / len(total_returns),
            'total_return_std': np.std(total_returns),
            'sharpe_ratio_std': np.std(sharpe_ratios) if sharpe_ratios else np.nan,
            'max_drawdown_std': np.std(max_drawdowns),
            'avg_capital_efficiency': np.mean(capital_efficiencies) if capital_efficiencies else np.nan,  # 新增
            'capital_efficiency_std': np.std(capital_efficiencies) if capital_efficiencies else np.nan,  # 新增
        }
        
        # 修复：计算稳健性评分时使用合理的数值范围
        try:
            # 收益分数 (0-40分) - 修复：年化收益率通常是小数形式，如0.1表示10%
            avg_annual_return_pct = metrics['avg_annual_return'] * 100  # 转换为百分比
            return_score = min(40, max(0, avg_annual_return_pct))
            
            # 风险分数 (0-30分) - 修复：回撤已经是百分比，直接使用
            avg_drawdown_pct = metrics['avg_max_drawdown']  # 已经是百分比
            risk_score = max(0, 30 - avg_drawdown_pct)
            
            # 稳定性分数 (0-30分)
            stability_score = (
                metrics['positive_return_ratio'] * 15 +  # 盈利品种占比 (0-15分)
                max(0, 15 - metrics['total_return_std'] * 50)  # 收益标准差 (0-15分)
            )
            
            metrics['robust_score'] = return_score + risk_score + stability_score
        except:
            metrics['robust_score'] = np.nan
        
        return metrics
    
    def get_chinese_column_mapping(self) -> Dict[str, str]:
        """获取中文列名映射表"""
        return {
            # 基本信息
            'parameter_set': '参数组合',
            'parameter_set_description': '参数描述',
            'symbol': '品种代码',
            'start_date': '开始日期',
            'end_date': '结束日期',
            'backtest_id': '回测标识',
            'timestamp': '时间戳',
            'error': '错误信息',
            
            # 回测统计指标
            'total_return': '总收益率',
            'annual_return': '年化收益率',
            'max_drawdown': '最大回撤',
            'max_ddpercent': '最大回撤百分比',
            'sharpe_ratio': '夏普比率',
            'return_drawdown_ratio': '收益回撤比',
            'return_std': '收益标准差',
            'total_net_pnl': '总净盈亏',
            'daily_net_pnl': '日均净盈亏',
            'total_commission': '总手续费',
            'daily_commission': '日均手续费',
            'total_slippage': '总滑点',
            'daily_slippage': '日均滑点',
            'total_turnover': '总成交额',
            'daily_turnover': '日均成交额',
            'total_trade_count': '总交易次数',
            'daily_trade_count': '日均交易次数',
            'total_return_rate': '总收益率',
            'annual_return_rate': '年化收益率',
            'profit_loss_ratio': '盈亏比',
            'win_rate': '胜率',
            'margin': '品种保证金',
            'capital_efficiency': '资金利用率',
            'avg_capital_efficiency': '平均资金利用率',
            'capital_efficiency_std': '资金利用率标准差',
            
            # 参数相关
            'param_k_1': '参数_K线1',
            'param_k_3': '参数_K线3',
            'param_k_5': '参数_K线5',
            'param_sl_multiplier': '参数_止损倍数',
            'param_zy': '参数_止盈点数',
            'param_ping_zy': '参数_保本止盈点数',
            'param_lots': '参数_手数',
            'param_daily_loss_limit': '参数_日亏损限制',
            'param_trailing_start_ratio': '参数_浮动止盈启动比例',
            'param_AF': '参数_加速因子',
            'param_AF_max': '参数_最大加速因子',
            'param_macd_boll_count_fz': '参数_MACD布林条件',
            'param_dk_fz': '参数_DK阈值',
            'param_donchian_period': '参数_唐奇安周期',
            'param_atr_window': '参数_ATR窗口',
            
            # 实际参数值
            'actual_zy': '实际_止盈价格',
            'actual_ping_zy': '实际_保本止盈价格',
            'actual_contract_multiplier': '实际_合约乘数',
            
            # 汇总统计指标
            'valid_symbols_count': '有效品种数量',
            'avg_total_return': '平均总收益率',
            'avg_annual_return': '平均年化收益率',
            'avg_sharpe_ratio': '平均夏普比率',
            'avg_max_drawdown': '平均最大回撤',
            'positive_return_ratio': '盈利品种占比',
            'total_return_std': '总收益率标准差',
            'sharpe_ratio_std': '夏普比率标准差',
            'max_drawdown_std': '最大回撤标准差',
            'robust_score': '稳健性评分',
        }
    
    def apply_chinese_headers(self, df: pd.DataFrame) -> pd.DataFrame:
        """应用中文表头"""
        chinese_mapping = self.get_chinese_column_mapping()
        
        # 创建新的列名映射，只映射存在的列
        rename_dict = {}
        for eng_col, chn_col in chinese_mapping.items():
            if eng_col in df.columns:
                rename_dict[eng_col] = chn_col
        
        # 重命名列
        df_chinese = df.rename(columns=rename_dict)
        
        return df_chinese
    
    def run_comprehensive_evaluation(self, start: datetime, end: datetime,
                                   contract_file: str = "期货全品种手续费保证金.xls",
                                   blacklist: List[str] = None,
                                   specific_symbols: List[str] = None,
                                   interval: str = "1m",
                                   slippage: float = 0,
                                   capital: float = 50000) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """运行综合评估
        
        参数:
            start: 回测开始时间
            end: 回测结束时间
            contract_file: 合约信息文件
            blacklist: 排除品种列表
            specific_symbols: 指定品种列表
            interval: K线周期
            slippage: 滑点
            capital: 初始资金
            
        返回:
            Tuple[pd.DataFrame, pd.DataFrame]: (详细结果DataFrame, 汇总结果DataFrame)
        """
        print("=" * 80)
        print("开始参数组合综合评估")
        print(f"回测区间: {start} 至 {end}")
        print(f"内存优化: {'启用' if self.enable_memory_optimization else '禁用'}")
        print(f"批量优化: {'启用' if self.enable_parallel else '禁用'}")
        if self.enable_parallel:
            print(f"批处理大小: {self.max_workers}")
        print("=" * 80)
        
        try:
            # 1. 设置合约信息
            symbols, rate, size, pricetick = self.setup_contracts(contract_file, blacklist, specific_symbols)
            
            # 2. 获取参数组合
            parameter_sets = self.design_parameter_sets()


            # 3. 为每个参数组合运行回测
            all_detailed_results = []
            summary_results = []
            
            for param_set_name, param_set in parameter_sets.items():
                # 运行该参数组合的回测 - 使用并行版本
                if self.enable_parallel:
                    param_results = self.run_backtest_for_parameter_set_parallel(
                        param_set_name=param_set_name,
                        param_set=param_set,
                        symbols=symbols,
                        rate=rate,
                        size=size,
                        pricetick=pricetick,
                        start=start,
                        end=end,
                        interval=interval,
                        slippage=slippage,
                        capital=capital
                    )
                else:
                    param_results = self.run_backtest_for_parameter_set(
                        param_set_name=param_set_name,
                        param_set=param_set,
                        symbols=symbols,
                        rate=rate,
                        size=size,
                        pricetick=pricetick,
                        start=start,
                        end=end,
                        interval=interval,
                        slippage=slippage,
                        capital=capital
                    )
                
                # 添加到详细结果
                all_detailed_results.extend(param_results)
                
                # 计算该参数组合的整体指标
                param_metrics = self.calculate_parameter_set_metrics(param_results)
                param_metrics['parameter_set'] = param_set_name
                param_metrics['parameter_set_description'] = param_set['description']
                
                # 添加参数详情到汇总
                for param_name, param_value in param_set['params'].items():
                    param_metrics[f'param_{param_name}'] = param_value
                
                summary_results.append(param_metrics)
                
                # 每完成一个参数组合就进行内存清理
                gc.collect()
            
            # 4. 转换为DataFrame
            detailed_df = pd.DataFrame(all_detailed_results)
            summary_df = pd.DataFrame(summary_results)
            
            # 5. 排序汇总结果 (按稳健性评分降序)
            summary_df = summary_df.sort_values('robust_score', ascending=False, na_position='last')
            
            # 6. 应用中文表头
            detailed_df_chinese = self.apply_chinese_headers(detailed_df)
            summary_df_chinese = self.apply_chinese_headers(summary_df)
            
            # 7. 保存结果 (保存中文版本)
            detailed_file = os.path.join(self.result_dir, f"参数评估详细结果_{self.timestamp}.csv")
            summary_file = os.path.join(self.result_dir, f"参数评估汇总结果_{self.timestamp}.csv")
            
            detailed_df_chinese.to_csv(detailed_file, index=False, encoding='utf-8-sig')
            summary_df_chinese.to_csv(summary_file, index=False, encoding='utf-8-sig')
            
            # 8. 另外保存Excel格式 (更好的中文显示效果)
            detailed_excel_file = os.path.join(self.result_dir, f"参数评估详细结果_{self.timestamp}.xlsx")
            summary_excel_file = os.path.join(self.result_dir, f"参数评估汇总结果_{self.timestamp}.xlsx")
            
            with pd.ExcelWriter(detailed_excel_file, engine='openpyxl') as writer:
                detailed_df_chinese.to_excel(writer, sheet_name='详细结果', index=False)
            
            with pd.ExcelWriter(summary_excel_file, engine='openpyxl') as writer:
                summary_df_chinese.to_excel(writer, sheet_name='汇总结果', index=False)
            
            print("\n" + "=" * 80)
            print("参数组合评估完成!")
            print(f"详细结果CSV: {detailed_file}")
            print(f"汇总结果CSV: {summary_file}")
            print(f"详细结果Excel: {detailed_excel_file}")
            print(f"汇总结果Excel: {summary_excel_file}")
            
            # 显示最终内存状态
            if self.memory_manager:
                final_mem_info = self.memory_manager.get_memory_info()
                print(f"最终内存状态: 缓存数据集 {final_mem_info['cached_datasets']}, 引擎池 {final_mem_info['engine_pool_size']}")
            
            # 9. 打印汇总结果 (使用中文表头) - 修复显示格式
            print("\n参数组合排名 (按稳健性评分):")
            print("-" * 120)
            for i, row in summary_df_chinese.iterrows():
                param_set_col = '参数组合' if '参数组合' in row.index else 'parameter_set'
                robust_score_col = '稳健性评分' if '稳健性评分' in row.index else 'robust_score'
                avg_annual_col = '平均年化收益率' if '平均年化收益率' in row.index else 'avg_annual_return'
                avg_drawdown_col = '平均最大回撤' if '平均最大回撤' in row.index else 'avg_max_drawdown'
                positive_ratio_col = '盈利品种占比' if '盈利品种占比' in row.index else 'positive_return_ratio'
                capital_efficiency_col = '平均资金利用率' if '平均资金利用率' in row.index else 'avg_capital_efficiency'
                
                # 修复：年化收益率需要转换为百分比显示，回撤已经是百分比
                annual_return_value = row[avg_annual_col] * 100  # 转换为百分比
                drawdown_value = row[avg_drawdown_col]  # 已经是百分比
                positive_ratio_value = row[positive_ratio_col]  # 比率，需要转换为百分比
                capital_efficiency_value = row[capital_efficiency_col] if not pd.isna(row[capital_efficiency_col]) else 0
                
                print(f"{row[param_set_col]:20} | 稳健评分: {row[robust_score_col]:6.1f} | "
                      f"年化收益: {annual_return_value:6.1f}% | "
                      f"平均回撤: {drawdown_value:6.1f}% | "
                      f"资金利用率: {capital_efficiency_value:6.3f} | "
                      f"盈利占比: {positive_ratio_value:6.1%}")
            
            # 返回中文版本的DataFrame
            return detailed_df_chinese, summary_df_chinese
            
        finally:
            # 清理所有内存和缓存
            if self.memory_manager:
                self.memory_manager.clear_all()
            gc.collect()
    
    def detect_data_leakage(self) -> Dict[str, Any]:
        """检测数据泄露风险
        
        返回:
            Dict: 泄露检测报告
        """
        report = {
            'timestamp': datetime.now().isoformat(),
            'backtest_count': self.backtest_count,
            'memory_usage_mb': 0,
            'potential_leaks': [],
            'recommendations': []
        }
        
        try:
            # 检测内存使用量
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            report['memory_usage_mb'] = memory_info.rss / (1024 * 1024)
            
            # 检测可能的数据泄露迹象
            if report['memory_usage_mb'] > 2000:  # 超过2GB
                report['potential_leaks'].append('内存使用量过高，可能存在内存泄露')
                report['recommendations'].append('建议重启程序或减少批处理大小')
            
            # 检测vnpy模块是否有残留状态
            import sys
            vnpy_modules = [name for name in sys.modules.keys() if 'vnpy' in name.lower()]
            if len(vnpy_modules) > 50:
                report['potential_leaks'].append(f'vnpy相关模块过多({len(vnpy_modules)})，可能存在模块缓存')
                report['recommendations'].append('建议清理模块缓存')
            
            # 检测垃圾回收效果
            import gc
            gc_stats = gc.get_stats()
            if gc_stats:
                gen0_count = gc_stats[0]['collections']
                if gen0_count > 1000:
                    report['potential_leaks'].append('垃圾回收次数过多，可能存在循环引用')
                    report['recommendations'].append('建议手动断开对象引用')
            
            # 检测临时文件
            import tempfile
            temp_dir = tempfile.gettempdir()
            try:
                temp_files = [f for f in os.listdir(temp_dir) 
                             if any(keyword in f.lower() for keyword in ['vnpy', 'backtest', 'strategy'])]
                if len(temp_files) > 20:
                    report['potential_leaks'].append(f'临时文件过多({len(temp_files)})，可能存在文件泄露')
                    report['recommendations'].append('建议清理临时文件')
            except:
                pass
                
        except ImportError:
            report['potential_leaks'].append('无法导入psutil，无法监控内存使用')
        except Exception as e:
            report['potential_leaks'].append(f'泄露检测失败: {str(e)}')
        
        return report
    
    def perform_deep_cleanup(self):
        """执行深度清理，防止数据积累"""
        print("🧹 执行深度清理...")
        
        try:
            # 1. 强制垃圾回收
            import gc
            before_count = len(gc.get_objects())
            gc.collect()
            after_count = len(gc.get_objects())
            print(f"   垃圾回收: {before_count} -> {after_count} 对象")
            
            # 2. 清理内存管理器
            if self.memory_manager:
                self.memory_manager.clear_all()
                print("   内存管理器已清理")
            
            # 3. 清理结果累积
            if len(self.all_results) > 1000:
                print(f"   清理累积结果: {len(self.all_results)} 条")
                self.all_results = self.all_results[-500:]  # 只保留最近500条
            
            # 4. 强制清理导入的模块缓存
            import sys
            strategy_modules = [name for name in list(sys.modules.keys()) 
                              if 'strategy' in name.lower() and 'vnpy' in name.lower()]
            for module_name in strategy_modules[:10]:  # 限制清理数量
                try:
                    if hasattr(sys.modules[module_name], '__dict__'):
                        module_dict = sys.modules[module_name].__dict__
                        cache_attrs = [k for k in module_dict.keys() 
                                     if any(keyword in k.lower() for keyword in ['cache', 'pool', 'buffer'])]
                        for attr in cache_attrs:
                            try:
                                if hasattr(module_dict[attr], 'clear'):
                                    module_dict[attr].clear()
                            except:
                                pass
                except:
                    pass
            
            print("✅ 深度清理完成")
            
        except Exception as e:
            print(f"⚠️  深度清理失败: {str(e)}")
    
    def periodic_leak_detection(self):
        """定期泄露检测"""
        self.backtest_count += 1
        
        if self.backtest_count % self.leak_detection_interval == 0:
            print(f"\n🔍 第{self.backtest_count}次回测，执行泄露检测...")
            
            # 执行泄露检测
            report = self.detect_data_leakage()
            
            print(f"   内存使用: {report['memory_usage_mb']:.1f} MB")
            
            if report['potential_leaks']:
                print(f"⚠️  发现{len(report['potential_leaks'])}个潜在泄露:")
                for leak in report['potential_leaks']:
                    print(f"   - {leak}")
                
                print("💡 建议:")
                for rec in report['recommendations']:
                    print(f"   - {rec}")
                
                # 自动执行深度清理
                self.perform_deep_cleanup()
            else:
                print("✅ 未发现数据泄露风险")
            
            print()  # 空行分隔
    
    def run_single_symbol_backtest_isolated(self, strategy_class, symbol, params, start, end):
        """运行单个品种的回测 - 完全隔离版本，避免数据泄露
        
        参数:
            strategy_class: 策略类
            symbol: 合约代码
            params: 策略参数
            start: 开始时间
            end: 结束时间
            
        返回:
            Dict: 回测结果字典，包含资金利用率
        """
        # 获取合约参数
        contract_info = ContractInfo("期货全品种手续费保证金.xls")
        contract_params = contract_info.get_contract_params()
        
        rate = contract_params['rate'].get(symbol, 0.0002)
        size = contract_params['size'].get(symbol, 10)
        pricetick = contract_params['pricetick'].get(symbol, 1)
        margin = contract_params['margin'].get(symbol, 0)  # 获取保证金
        
        # 创建独立的回测器
        backtester = MultiSymbolBacktest()
        
        # 处理参数 - 每次都创建全新的参数副本
        modified_params = {}
        
        # 需要乘以pricetick的参数列表
        price_related_params = ["ping_zy", "zy"]
        # 需要乘以size的参数列表  
        size_related_params = ["contract_multiplier"]
        
        for param_name, param_value in params.items():
            # 对价格相关参数乘以pricetick
            if param_name in price_related_params:
                modified_params[param_name] = param_value * pricetick
            # 对合约乘数相关参数乘以size
            elif param_name in size_related_params:
                modified_params[param_name] = size
            # 其他参数保持不变
            else:
                modified_params[param_name] = param_value
        
        # 创建完全独立的参数集
        isolated_params = {k: v for k, v in modified_params.items()}
        
        # 抑制输出的回测
        if self.suppress_output:
            with redirect_stdout(StringIO()), redirect_stderr(StringIO()):
                df, stats = backtester.run_single_backtest(
                    strategy_class=strategy_class,
                    symbol=symbol,
                    interval="1m",
                    start=start,
                    end=end,
                    rate=rate,
                    slippage=0,
                    size=size,
                    pricetick=pricetick,
                    capital=50000,
                    setting=isolated_params
                )
        else:
            df, stats = backtester.run_single_backtest(
                strategy_class=strategy_class,
                symbol=symbol,
                interval="1m",
                start=start,
                end=end,
                rate=rate,
                slippage=0,
                size=size,
                pricetick=pricetick,
                capital=50000,
                setting=isolated_params
            )
        
        # 构建结果字典
        result_stats = stats.copy()
        result_stats['symbol'] = symbol
        result_stats['start_date'] = start.strftime('%Y-%m-%d')
        result_stats['end_date'] = end.strftime('%Y-%m-%d')
        
        # 添加保证金信息
        result_stats['margin'] = margin
        
        # 计算资金利用率 = 总净盈亏/品种保证金
        if margin and margin > 0:
            total_net_pnl = result_stats.get('total_net_pnl', 0)
            result_stats['capital_efficiency'] = total_net_pnl / margin
        else:
            result_stats['capital_efficiency'] = 0
        
        # 添加原始参数详情
        for param_name, param_value in params.items():
            result_stats[f'param_{param_name}'] = param_value
        
        # 添加调整后的实际参数值
        for param_name, param_value in modified_params.items():
            if param_name in price_related_params:
                result_stats[f'actual_{param_name}'] = param_value
        
        return result_stats


if __name__ == "__main__":
    # 🧹 程序启动时第一件事：全面清空所有缓存，防止数据泄露
    print("🧹 正在清空所有缓存，确保数据干净...")
    
    # 1. 强制垃圾回收，清理Python对象缓存
    import gc
    gc.collect()
    gc.disable()  # 暂时禁用自动垃圾回收，避免运行中的干扰
    
    # 2. 清理pandas缓存
    try:
        import pandas as pd
        # 清理pandas内部缓存
        pd._config.reset_option("all")
    except:
        pass
    
    # 3. 清理numpy缓存
    try:
        import numpy as np
        # 清理numpy错误状态
        np.seterr(all='raise')
        np.seterr(all='warn')  # 重置为默认
    except:
        pass
    
    # 4. 清理vnpy相关的全局缓存
    try:
        # 清理可能的策略模块缓存
        import sys
        modules_to_clear = [name for name in sys.modules.keys() 
                           if 'vnpy' in name or 'strategy' in name.lower()]
        # 注意：不完全删除模块，只清理可能的缓存属性
        for module_name in modules_to_clear:
            module = sys.modules.get(module_name)
            if module and hasattr(module, '__dict__'):
                # 清理可能的缓存属性（以cache、pool、buffer等命名）
                cache_attrs = [attr for attr in dir(module) 
                              if any(keyword in attr.lower() 
                                   for keyword in ['cache', 'pool', 'buffer', 'history', 'data'])]
                for attr in cache_attrs:
                    try:
                        obj = getattr(module, attr)
                        if hasattr(obj, 'clear'):
                            obj.clear()
                        elif isinstance(obj, (list, dict, set)):
                            obj.clear()
                    except:
                        pass
    except:
        pass
    
    # 5. 清理系统临时文件（如果存在）
    import tempfile
    import os
    try:
        temp_dir = tempfile.gettempdir()
        vnpy_temp_files = [f for f in os.listdir(temp_dir) 
                          if 'vnpy' in f.lower() or 'backtest' in f.lower()]
        for temp_file in vnpy_temp_files[:10]:  # 限制清理数量，避免误删
            try:
                os.remove(os.path.join(temp_dir, temp_file))
            except:
                pass
    except:
        pass
    
    # 6. 重新启用垃圾回收
    gc.enable()
    gc.collect()
    
    print("✅ 缓存清空完成，开始正式运行...")
    print("=" * 80)
    
    # 创建评估器实例，启用输出抑制但禁用内存优化避免数据污染
    evaluator = ParameterSetEvaluator(
        result_dir=r"C:\Users\<USER>\Desktop\result\parameter_evaluation",
        suppress_output=True,  # 抑制策略回测过程中的输出
        enable_memory_optimization=False,  # 禁用内存优化，避免数据污染
        enable_parallel=True,  # 启用批量优化处理，提高效率
        max_workers=None  # 批处理大小，默认为CPU核心数
    )
    
    # 设置回测时间区间
    start_date = datetime(2025, 5, 1)
    end_date = datetime(2025, 7, 30)
    
    # 快速测试模式：只测试几个品种和几个参数组合来验证修复
    test_mode = False  # 设为False进行完整测试
    
    if test_mode:
        print("🧪 测试模式：使用少量品种和参数组合验证修复效果")
        # 只测试3个品种
        test_symbols =  [
    "ao888.SHFE", "FG888.CZCE", "hc888.SHFE", "SA888.CZCE", "jm888.DCE",
    "al888.SHFE", "CY888.CZCE", "SF888.CZCE", "ps888.GFEX", "OI888.CZCE",
    "CJ888.CZCE", "lh888.DCE", "rb888.SHFE", "IC888.CFFEX", "si888.GFEX",
    "j888.DCE", "i888.DCE", "lc888.GFEX", "p888.DCE", "ru888.SHFE",
    "ni888.SHFE", "RM888.CZCE", "CF888.CZCE", "IM888.CFFEX", "v888.DCE",
    "ss888.SHFE", "cu888.SHFE", "IH888.CFFEX", "pb888.SHFE", "nr888.INE",
    "ag888.SHFE", "bu888.SHFE", "SM888.CZCE", "UR888.CZCE", "SR888.CZCE",
    "b888.DCE", "SH888.CZCE"
]
        
        # 只测试3个参数组合
        original_design = evaluator.design_parameter_sets
        def limited_design():
            all_sets = original_design()
            return {
                "conservative_1_3_5": all_sets["conservative_1_3_5"],
                "aggressive_1_3_5": all_sets["aggressive_1_3_5"],
                "balanced_1_3_5": all_sets["balanced_1_3_5"],
            }
        evaluator.design_parameter_sets = limited_design
        
        print(f"📅 回测时间: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
        print(f"📊 参数组合数量: {len(evaluator.design_parameter_sets())}")
        print(f"🎯 测试品种: {test_symbols}")
        print(f"🔇 策略输出: {'已抑制' if evaluator.suppress_output else '显示'}")
        print(f"🧠 内存优化: {'启用' if evaluator.enable_memory_optimization else '禁用'}")
        print(f"⚡ 批量优化: {'启用' if evaluator.enable_parallel else '禁用'}")
        
        # 运行测试
        detailed_results, summary_results = evaluator.run_comprehensive_evaluation(
            start=start_date,
            end=end_date,
            blacklist=["IF", "IH", "IC", "IM", "T", "TF", "TL"],
            specific_symbols=test_symbols,
            interval="1m",
            slippage=0,
            capital=50000
        )
        
        print(f"\n🧪 测试完成！请检查结果文件，验证不同参数组合的结果是否不同")
        
    else:
        # 完整测试模式
        # 示例：指定部分品种进行测试 (可以设为None来测试所有品种)
        # test_symbols = [
        #     "rb888.SHFE", "cu888.SHFE", "au888.SHFE", "ag888.SHFE", "al888.SHFE",
        #     "i888.DCE", "j888.DCE", "jm888.DCE", "a888.DCE", "m888.DCE",
        #     "TA888.CZCE", "CF888.CZCE", "SR888.CZCE", "MA888.CZCE", "RM888.CZCE",
        #     "sc888.INE", "lu888.INE", "nr888.INE"
        # ]
        test_symbols = [
            "ao888.SHFE", "FG888.CZCE", "hc888.SHFE", "SA888.CZCE", "jm888.DCE",
            "al888.SHFE", "CY888.CZCE", "SF888.CZCE", "ps888.GFEX", "OI888.CZCE",
            "CJ888.CZCE", "lh888.DCE", "rb888.SHFE", "IC888.CFFEX", "si888.GFEX",
            "j888.DCE", "i888.DCE", "lc888.GFEX", "p888.DCE", "ru888.SHFE",
            "ni888.SHFE", "RM888.CZCE", "CF888.CZCE", "IM888.CFFEX", "v888.DCE",
            "ss888.SHFE", "cu888.SHFE", "IH888.CFFEX", "pb888.SHFE", "nr888.INE",
            "ag888.SHFE", "bu888.SHFE", "SM888.CZCE", "UR888.CZCE", "SR888.CZCE",
            "b888.DCE", "SH888.CZCE"
        ]

        # test_symbols = None
        
        print("🚀 开始参数组合批量回测评估...")
        print(f"📅 回测时间: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
        print(f"📊 参数组合数量: {len(evaluator.design_parameter_sets())}")
        print(f"🎯 品种范围: {'指定品种' if test_symbols else '全部品种'}")
        print(f"🔇 策略输出: {'已抑制' if evaluator.suppress_output else '显示'}")
        print(f"🧠 内存优化: {'启用' if evaluator.enable_memory_optimization else '禁用'}")
        print(f"⚡ 批量优化: {'启用' if evaluator.enable_parallel else '禁用'}")
        
        # 运行综合评估
        detailed_results, summary_results = evaluator.run_comprehensive_evaluation(
            start=start_date,
            end=end_date,
            blacklist=["IF", "IH", "IC", "IM", "T", "TF", "TL"],
            specific_symbols=test_symbols,  # 设为None可测试所有品种
            interval="1m",
            slippage=0,
            capital=50000
        )
        
        print(f"\n🎉 评估完成！共测试了 {len(evaluator.design_parameter_sets())} 组参数，{len(test_symbols) if test_symbols else '全部'} 个品种") 