"""
系统配置管理模块
统一管理系统运行参数、数据库配置、日志配置等
"""

import os
import json
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "localhost"
    port: int = 3306
    database: str = "vnpy"
    username: str = "root"
    password: str = ""
    driver: str = "mysql"


@dataclass
class LogConfig:
    """日志配置"""
    level: str = "INFO"
    console: bool = True
    file: bool = True
    file_path: str = "logs"
    max_file_size: int = 10  # MB
    backup_count: int = 5
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"


@dataclass
class SchedulerConfig:
    """调度器配置"""
    # 每日定时任务时间
    screening_time: str = "08:00"      # 品种筛选时间
    allocation_time: str = "10:15"     # 资金分配时间
    adjustment_time: str = "11:30"     # 策略调整时间
    switching_time: str = "15:25"      # 合约切换时间
    night_prep_time: str = "20:00"     # 夜盘准备时间
    
    # 交易时间段
    day_start: str = "08:57"
    day_end: str = "15:16"
    night_start: str = "20:57"
    night_end: str = "02:35"
    
    # 调度间隔
    monitor_interval: int = 5          # 监控间隔(秒)
    heartbeat_interval: int = 30       # 心跳间隔(秒)


@dataclass
class SystemConfig:
    """系统配置管理类"""
    
    # 基础配置
    system_name: str = "智能量化交易系统"
    version: str = "1.0.0"
    debug: bool = False
    
    # 数据库配置
    database: DatabaseConfig = DatabaseConfig()
    
    # 日志配置
    logging: LogConfig = LogConfig()
    
    # 调度器配置
    scheduler: SchedulerConfig = SchedulerConfig()
    
    # 系统路径配置
    base_path: str = ""
    config_path: str = "config"
    data_path: str = "data"
    log_path: str = "logs"
    temp_path: str = "temp"
    
    # 性能配置
    max_workers: int = 8               # 最大工作线程数
    request_timeout: int = 30          # 请求超时时间(秒)
    retry_times: int = 3               # 重试次数
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.base_path:
            self.base_path = str(Path(__file__).parent.parent)
        
        # 确保路径存在
        self._ensure_paths()
    
    def _ensure_paths(self):
        """确保必要的路径存在"""
        paths = [
            self.get_config_path(),
            self.get_data_path(),
            self.get_log_path(),
            self.get_temp_path()
        ]
        
        for path in paths:
            Path(path).mkdir(parents=True, exist_ok=True)
    
    def get_config_path(self) -> str:
        """获取配置文件路径"""
        return os.path.join(self.base_path, self.config_path)
    
    def get_data_path(self) -> str:
        """获取数据文件路径"""
        return os.path.join(self.base_path, self.data_path)
    
    def get_log_path(self) -> str:
        """获取日志文件路径"""
        return os.path.join(self.base_path, self.log_path)
    
    def get_temp_path(self) -> str:
        """获取临时文件路径"""
        return os.path.join(self.base_path, self.temp_path)
    
    @classmethod
    def load_from_file(cls, config_file: str) -> 'SystemConfig':
        """从配置文件加载配置"""
        if not os.path.exists(config_file):
            # 如果配置文件不存在，创建默认配置
            config = cls()
            config.save_to_file(config_file)
            return config
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 递归创建配置对象
            config = cls()
            config._update_from_dict(data)
            return config
            
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return cls()
    
    def save_to_file(self, config_file: str):
        """保存配置到文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(self), f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def _update_from_dict(self, data: Dict[str, Any]):
        """从字典更新配置"""
        for key, value in data.items():
            if hasattr(self, key):
                attr = getattr(self, key)
                if isinstance(attr, (DatabaseConfig, LogConfig, SchedulerConfig)):
                    # 更新嵌套配置对象
                    if isinstance(value, dict):
                        for sub_key, sub_value in value.items():
                            if hasattr(attr, sub_key):
                                setattr(attr, sub_key, sub_value)
                else:
                    setattr(self, key, value)
    
    def get_database_url(self) -> str:
        """获取数据库连接URL"""
        db = self.database
        if db.driver == "mysql":
            return f"mysql+pymysql://{db.username}:{db.password}@{db.host}:{db.port}/{db.database}"
        elif db.driver == "sqlite":
            return f"sqlite:///{self.get_data_path()}/{db.database}.db"
        else:
            raise ValueError(f"不支持的数据库驱动: {db.driver}")
    
    def is_trading_time(self, current_time: str = None) -> bool:
        """判断是否在交易时间内"""
        from datetime import datetime, time
        
        if current_time is None:
            current_time = datetime.now().time()
        elif isinstance(current_time, str):
            current_time = datetime.strptime(current_time, "%H:%M").time()
        
        day_start = datetime.strptime(self.scheduler.day_start, "%H:%M").time()
        day_end = datetime.strptime(self.scheduler.day_end, "%H:%M").time()
        night_start = datetime.strptime(self.scheduler.night_start, "%H:%M").time()
        night_end = datetime.strptime(self.scheduler.night_end, "%H:%M").time()
        
        # 检查是否在日盘或夜盘时间内
        return (
            (day_start <= current_time <= day_end) or
            (current_time >= night_start) or
            (current_time <= night_end)
        )

    def validate(self) -> bool:
        """验证配置有效性"""
        try:
            # 验证基础属性
            if not self.system_name or not self.version:
                return False

            # 验证数据库配置
            if not self.database.driver or not self.database.database:
                return False

            # 验证路径配置
            if not self.config_path or not self.data_path or not self.log_path:
                return False

            # 验证性能配置
            if self.max_workers <= 0 or self.request_timeout <= 0:
                return False

            return True
        except Exception:
            return False

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


# 全局配置实例
_system_config: Optional[SystemConfig] = None


def get_system_config() -> SystemConfig:
    """获取系统配置实例"""
    global _system_config
    if _system_config is None:
        config_file = os.path.join(
            Path(__file__).parent.parent, 
            "config", 
            "system_config.json"
        )
        _system_config = SystemConfig.load_from_file(config_file)
    return _system_config


def reload_system_config():
    """重新加载系统配置"""
    global _system_config
    _system_config = None
    return get_system_config()


if __name__ == "__main__":
    # 测试配置模块
    config = SystemConfig()
    print("系统配置:")
    print(f"系统名称: {config.system_name}")
    print(f"版本: {config.version}")
    print(f"数据库URL: {config.get_database_url()}")
    print(f"配置路径: {config.get_config_path()}")
    print(f"当前是否交易时间: {config.is_trading_time()}")
    
    # 保存配置文件
    config_file = os.path.join(config.get_config_path(), "system_config.json")
    config.save_to_file(config_file)
    print(f"配置已保存到: {config_file}")
