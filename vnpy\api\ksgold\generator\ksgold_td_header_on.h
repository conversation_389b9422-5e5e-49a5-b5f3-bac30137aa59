virtual void nResult() {};

virtual void nReason() {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void pfldMktStatus(const dict &data) {};

virtual void pInstrumentStatus(const dict &data) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void pOrder(const dict &data) {};

virtual void pLogout(const dict &data) {};

virtual void (const dict &data) {};

virtual void pEtfTradeDetail(const dict &data) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &error, int reqid, bool last) {};

virtual void pTrade(const dict &data) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

