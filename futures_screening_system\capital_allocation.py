#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货资金配置程序
根据设定资金和保证金要求，配置适合实盘交易的品种
考虑策略开仓2倍手数和固定+浮动止盈逻辑
"""

import sys
import os
# 添加父目录到Python路径，以便导入vnpy模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime
from contract_info import ContractInfo
from futures_minute_scanner import FuturesMinuteScanner
from system_config import get_capital_config, get_symbol_risk_level, CAPITAL_PRESETS, is_blacklisted, get_blacklist_reason
import warnings

warnings.filterwarnings('ignore')

class CapitalAllocation:
    """资金配置管理器"""
    
    def __init__(self, preset_name: str = None, contract_info_path: str = "../期货全品种手续费保证金.xls"):
        """初始化资金配置器

        Args:
            preset_name: 预设配置名称，如'10万元'，None则使用默认配置
            contract_info_path: 合约信息文件路径
        """
        # 获取配置参数
        self.config = get_capital_config(preset_name)
        self.total_capital = self.config['total_capital']

        self.contract_info = ContractInfo(contract_info_path)
        self.scanner = FuturesMinuteScanner(contract_info_path)

        # 从配置文件加载参数
        self.max_risk_per_symbol = self.config['max_risk_per_symbol']
        self.max_margin_ratio = self.config['max_margin_ratio']
        self.reserve_ratio = self.config.get('reserve_ratio', 0.2)
        self.max_symbols = self.config['max_symbols']

        # 策略参数（基于fenzhouqiplus_strategy.py）
        self.strategy_lots = self.config['strategy_lots']
        self.opening_multiplier = self.config['opening_multiplier']

        # 激进模式参数
        self.aggressive_mode = self.config.get('aggressive_mode', False)
        self.total_margin_limit = self.config.get('total_margin_limit', 1.0)
        self.margin_warning_threshold = self.config.get('margin_warning_threshold', 0.3)
        self.fixed_profit_ratio = self.config['fixed_profit_ratio']
        self.trailing_profit_ratio = self.config['trailing_profit_ratio']
        self.estimated_risk_ratio = self.config['estimated_risk_ratio']
        self.max_lots_per_symbol = self.config['max_lots_per_symbol']
        
        print(f"资金配置器初始化完成")
        if preset_name:
            print(f"使用预设配置: {preset_name}")
        print(f"总资金: {self.total_capital:,.0f}元")
        print(f"最大品种数: {self.max_symbols}个")
        print(f"可用保证金: {self.total_capital * self.max_margin_ratio:,.0f}元")
        print(f"储备资金: {self.total_capital * self.reserve_ratio:,.0f}元")
        
    def get_margin_data(self) -> Dict:
        """获取保证金数据"""
        print("正在获取保证金数据...")

        # 获取合约参数
        params = self.contract_info.get_contract_params()
        margin_data = {}

        for symbol, margin in params['margin'].items():
            if margin >= 0:  # 包括保证金为0的品种，但会在后续处理中标记
                # 获取合约乘数和价格信息
                size = params['size'].get(symbol, 10)  # 默认合约乘数10

                # 从品种代码中提取品种名称
                product = ''.join([c for c in symbol if c.isalpha()]).upper()

                margin_data[symbol] = {
                    'product': product,
                    'margin_per_lot': margin,  # 每手保证金
                    'contract_size': size,     # 合约乘数
                    'symbol': symbol
                }

        print(f"获取到 {len(margin_data)} 个品种的保证金数据")
        return margin_data
    
    def calculate_position_size(self, symbol: str, margin_per_lot: float) -> Dict:
        """计算品种的仓位配置

        Args:
            symbol: 品种代码
            margin_per_lot: 每手保证金

        Returns:
            仓位配置信息
        """
        # 获取品种风险等级
        risk_info = get_symbol_risk_level(symbol)
        risk_multiplier = risk_info['risk_multiplier']
        max_position_ratio = risk_info['max_position_ratio']

        # 获取品种真实保证金比例和杠杆倍数
        margin_info = self.contract_info.get_margin_ratio(symbol)
        real_margin_ratio = margin_info['margin_ratio']
        real_leverage = margin_info['leverage']

        # 单品种最大风险资金（考虑风险等级和真实杠杆）
        base_risk_capital = self.total_capital * self.max_risk_per_symbol
        # 根据真实杠杆调整风险系数：杠杆越高，风险系数越大
        leverage_risk_factor = min(real_leverage / 10.0, 2.0)  # 限制最大2倍风险调整
        adjusted_risk_multiplier = risk_multiplier * leverage_risk_factor
        max_risk_capital = base_risk_capital * adjusted_risk_multiplier

        # 对于高保证金品种，适当放宽风险限制（因为它们通常是大合约，单位风险相对较小）
        if margin_per_lot > 15000:  # 保证金超过1.5万的品种
            high_margin_risk_factor = min(margin_per_lot / 10000, 3.0)  # 更激进的调整，最大3倍
            max_risk_capital = max_risk_capital * high_margin_risk_factor

        # 单品种最大仓位资金（考虑真实保证金比例）
        # 保证金比例越低，允许的仓位越大，但要有上限
        margin_position_factor = min(0.10 / real_margin_ratio, 2.0)  # 以10%为基准，限制最大2倍调整
        adjusted_max_position_ratio = min(max_position_ratio * margin_position_factor, 0.4)  # 最大不超过40%（放宽限制）
        max_position_capital = self.total_capital * adjusted_max_position_ratio

        # 对于高保证金品种，进一步放宽仓位限制
        if margin_per_lot > 10000:  # 保证金超过1万的品种
            high_margin_factor = min(margin_per_lot / 5000, 5.0)  # 更激进的调整，最大5倍
            adjusted_max_position_ratio = min(adjusted_max_position_ratio * high_margin_factor, 0.8)  # 最大80%
            max_position_capital = self.total_capital * adjusted_max_position_ratio

        # 考虑策略开仓2倍手数的保证金需求
        opening_lots = self.strategy_lots * self.opening_multiplier  # 开仓手数
        opening_margin = margin_per_lot * opening_lots  # 开仓保证金需求

        # 计算最大可开仓手数（基于保证金限制）
        # 激进模式允许更高的保证金使用率
        if self.aggressive_mode:
            available_margin = self.total_capital * self.total_margin_limit
        else:
            available_margin = self.total_capital * self.max_margin_ratio
        max_lots_by_margin = int(available_margin / (margin_per_lot + 0.000000001) / (self.opening_multiplier + 0.000000001))

        # 计算最大可开仓手数（基于风险限制）
        estimated_risk_per_lot = margin_per_lot * self.estimated_risk_ratio * risk_multiplier
        max_lots_by_risk = int(max_risk_capital / (estimated_risk_per_lot + 0.000000001) / (self.opening_multiplier + 0.000000001))

        # 计算最大可开仓手数（基于仓位限制）
        max_lots_by_position = int(max_position_capital / margin_per_lot / self.opening_multiplier)

        # 取较小值作为最终配置
        recommended_lots = min(
            max_lots_by_margin,
            max_lots_by_risk,
            max_lots_by_position,
            self.max_lots_per_symbol
        )

        # 记录限制原因（用于调试输出）
        limiting_factors = []
        if max_lots_by_margin == 0:
            limiting_factors.append(f"保证金限制: 每手{margin_per_lot:,.0f}元×{self.opening_multiplier} > 可用保证金{available_margin:,.0f}元")
        if max_lots_by_risk == 0:
            limiting_factors.append(f"风险限制: 每手风险{estimated_risk_per_lot:,.0f}元×{self.opening_multiplier} > 最大风险资金{max_risk_capital:,.0f}元")
        if max_lots_by_position == 0:
            limiting_factors.append(f"仓位限制: 每手{margin_per_lot:,.0f}元×{self.opening_multiplier} > 最大仓位资金{max_position_capital:,.0f}元")

        if recommended_lots < 1:
            recommended_lots = 0  # 不适合交易
        
        # 创建结果字典，包含中文键名用于显示和英文键名用于内部处理
        result = {
            '品种': symbol,
            '推荐手数': recommended_lots,
            '开仓手数': recommended_lots * self.opening_multiplier if recommended_lots > 0 else 0,
            '每手保证金': margin_per_lot,
            '所需保证金': margin_per_lot * recommended_lots * self.opening_multiplier if recommended_lots > 0 else 0,
            '预估风险': estimated_risk_per_lot * recommended_lots * self.opening_multiplier if recommended_lots > 0 else 0,
            '风险比例(%)': (estimated_risk_per_lot * recommended_lots * self.opening_multiplier / self.total_capital * 100) if recommended_lots > 0 else 0,
            '资金使用比例(%)': (margin_per_lot * recommended_lots * self.opening_multiplier / self.total_capital * 100) if recommended_lots > 0 else 0,
            '风险等级': risk_info['level'],
            '综合风险系数': adjusted_risk_multiplier,  # 综合考虑品种类型和杠杆的风险系数
            '交易所保证金比例(%)': real_margin_ratio * 100,  # 交易所规定的保证金比例
            '杠杆倍数': real_leverage,  # 基于交易所保证金的杠杆倍数2
            '最大仓位比例(%)': adjusted_max_position_ratio * 100  # 根据风险调整后的最大仓位比例
        }

        # 添加英文键名用于内部处理（不会出现在CSV中）
        result.update({
            'symbol': symbol,
            'recommended_lots': recommended_lots,
            'opening_lots': recommended_lots * self.opening_multiplier if recommended_lots > 0 else 0,
            'margin_per_lot': margin_per_lot,
            'total_margin_needed': margin_per_lot * recommended_lots * self.opening_multiplier if recommended_lots > 0 else 0,
            'estimated_risk': estimated_risk_per_lot * recommended_lots * self.opening_multiplier if recommended_lots > 0 else 0,
            'risk_ratio': (estimated_risk_per_lot * recommended_lots * self.opening_multiplier / self.total_capital * 100) if recommended_lots > 0 else 0,
            'margin_ratio': (margin_per_lot * recommended_lots * self.opening_multiplier / self.total_capital * 100) if recommended_lots > 0 else 0,
            'limiting_factors': limiting_factors,  # 限制因素
            'calculation_details': {  # 计算详情
                'max_lots_by_margin': max_lots_by_margin,
                'max_lots_by_risk': max_lots_by_risk,
                'max_lots_by_position': max_lots_by_position,
                'available_margin': available_margin,
                'max_risk_capital': max_risk_capital,
                'max_position_capital': max_position_capital,
                'estimated_risk_per_lot': estimated_risk_per_lot
            }
        })

        return result
    
    def screen_and_allocate(self, max_symbols: int = None) -> tuple:
        """筛选品种并进行资金配置
 
        Args:
            max_symbols: 最大选择品种数，None则使用配置文件中的值

        Returns:
            tuple: (配置结果DataFrame, 筛选结果DataFrame)
        """
        if max_symbols is None:
            max_symbols = self.max_symbols
        print("=" * 80)
        print("开始品种筛选和资金配置...")
        print("=" * 80)

        # 1. 获取保证金数据
        margin_data = self.get_margin_data()

        # 2. 运行品种筛选 - 添加超时处理
        print("正在运行品种筛选...")
        try:
            # 检查是否有现有的筛选结果文件
            import glob
            csv_files = glob.glob("期货品种筛选结果_*.csv")
            if csv_files:
                # 使用最新的筛选结果文件
                latest_file = max(csv_files, key=os.path.getctime)
                print(f"发现现有筛选结果文件: {latest_file}")
                # 自动使用现有结果，避免交互式输入
                try:
                    screening_results = pd.read_csv(latest_file, encoding='utf-8-sig')
                    print(f"✓ 自动使用现有筛选结果，共 {len(screening_results)} 个品种")
                except Exception as e:
                    print(f"读取现有结果失败: {e}")
                    # 如果读取失败，重新筛选
                    screening_results = pd.read_csv(latest_file, encoding='utf-8-sig')
                    print(f"✓ 使用现有筛选结果，共 {len(screening_results)} 个品种")
            else:
                # 没有现有结果，执行新的筛选
                screening_results = self.scanner.run_scan()
        except Exception as e:
            print(f"筛选过程出错: {e}")
            print("尝试使用现有筛选结果...")
            import glob
            csv_files = glob.glob("期货品种筛选结果_*.csv")
            if csv_files:
                latest_file = max(csv_files, key=os.path.getctime)
                screening_results = pd.read_csv(latest_file, encoding='utf-8-sig')
                print(f"✓ 使用现有筛选结果: {latest_file}")
            else:
                print("❌ 无法获取筛选结果")
                return pd.DataFrame(), pd.DataFrame()

        # 3. 筛选出合格品种
        qualified_symbols = screening_results[
            (screening_results['状态'].isin(self.config['preferred_status'])) &
            (screening_results['综合评分'] >= self.config['min_score'])
        ].copy()

        # 4. 应用黑名单过滤
        before_blacklist = len(qualified_symbols)
        blacklisted_symbols = []

        # 过滤黑名单品种
        filtered_symbols = []
        for _, row in qualified_symbols.iterrows():
            symbol = row['品种']
            if is_blacklisted(symbol):
                blacklisted_symbols.append({
                    'symbol': symbol,
                    'reason': get_blacklist_reason(symbol),
                    'score': row['综合评分']
                })
            else:
                filtered_symbols.append(row)

        qualified_symbols = pd.DataFrame(filtered_symbols) if filtered_symbols else pd.DataFrame()

        # 显示黑名单信息
        if blacklisted_symbols:
            print(f"\n⚠️ 黑名单品种排除:")
            for item in blacklisted_symbols:
                print(f"  - {item['symbol']} (评分{item['score']:.1f}): {item['reason']}")

        print(f"黑名单过滤前: {before_blacklist} 个品种")
        print(f"黑名单过滤后: {len(qualified_symbols)} 个合格品种")

        # 显示筛选评分情况
        self.display_screening_results(screening_results, qualified_symbols)

        # 5. 为合格品种计算资金配置
        allocation_results = []

        print(f"\n🔍 开始为 {len(qualified_symbols)} 个合格品种计算资金配置...")

        for _, row in qualified_symbols.iterrows():
            symbol = row['品种']

            # 查找对应的保证金数据
            margin_info = None
            for margin_symbol, info in margin_data.items():
                # 提取品种代码进行匹配（去掉数字和交易所后缀）
                symbol_code = symbol.split('.')[0].replace('888', '').upper()  # ao888.SHFE -> AO
                product_code = info['product'].replace('SHFE', '').replace('DCE', '').replace('CZCE', '').replace('CFFEX', '').replace('INE', '').replace('GFEX', '')  # AOSHFE -> AO

                # 多种匹配方式
                if (info['product'] in symbol.upper() or
                    symbol.upper() in margin_symbol.upper() or
                    symbol_code == product_code or
                    margin_symbol.upper() == symbol.upper()):
                    margin_info = info
                    break

            if margin_info:
                # 计算仓位配置
                allocation = self.calculate_position_size(symbol, margin_info['margin_per_lot'])
                allocation.update({
                    '综合评分': row['综合评分'],
                    '状态': row['状态'],
                    '趋势方向': row.get('趋势方向', ''),
                    '最佳周期': row.get('最佳周期', ''),
                    'product': margin_info['product']
                })
                allocation_results.append(allocation)
                if allocation['recommended_lots'] > 0:
                    print(f"  ✅ {symbol}: 推荐{allocation['recommended_lots']}手, 保证金{allocation['所需保证金']:.0f}元")
                else:
                    print(f"  ❌ {symbol}: 推荐0手, 保证金0元")
                    details = allocation['calculation_details']
                    print(f"     计算过程: 保证金限制{details['max_lots_by_margin']}手 | 风险限制{details['max_lots_by_risk']}手 | 仓位限制{details['max_lots_by_position']}手")
                    if allocation['limiting_factors']:
                        print(f"     限制原因: {'; '.join(allocation['limiting_factors'])}")
            else:
                print(f"  ❌ {symbol}: 未找到保证金数据")

        print(f"\n📊 配置结果: {len(allocation_results)} 个品种成功配置")

        # 6. 转换为DataFrame并排序
        if allocation_results:
            df_allocation = pd.DataFrame(allocation_results)
            df_allocation = df_allocation[df_allocation['recommended_lots'] > 0]  # 只保留可交易的品种
            df_allocation = df_allocation.sort_values('综合评分', ascending=False)

            # 7. 激进保证金管理 - 检查总保证金是否超限
            if self.aggressive_mode:
                df_allocation = self.adjust_positions_for_margin(df_allocation)

            df_allocation = df_allocation.sort_values('综合评分', ascending=False)
            df_allocation = df_allocation.head(max_symbols)  # 限制品种数量
        else:
            df_allocation = pd.DataFrame()

        return df_allocation, screening_results

    def display_screening_results(self, screening_results: pd.DataFrame, qualified_symbols: pd.DataFrame):
        """显示筛选评分情况"""
        print("\n" + "=" * 100)
        print("📊 品种筛选评分情况".center(100))
        print("=" * 100)

        # 统计信息
        total_count = len(screening_results)
        qualified_count = len(qualified_symbols)
        focus_count = len(screening_results[screening_results['状态'] == '重点关注'])
        ready_count = len(screening_results[screening_results['状态'] == '准备入场'])
        observe_count = len(screening_results[screening_results['状态'] == '观察'])
        excluded_count = len(screening_results[screening_results['状态'] == '排除'])

        print(f"📈 评分统计:")
        print(f"   总品种数: {total_count}")
        print(f"   合格品种: {qualified_count} ({qualified_count/total_count:.1%})")
        print(f"   重点关注: {focus_count} (评分≥60)")
        print(f"   准备入场: {ready_count} (评分40-59)")
        print(f"   观察品种: {observe_count} (评分25-39)")
        print(f"   排除品种: {excluded_count} (评分<25)")

        if not screening_results.empty:
            avg_score = screening_results['综合评分'].mean()
            max_score = screening_results['综合评分'].max()
            min_score = screening_results['综合评分'].min()
            print(f"   平均评分: {avg_score:.1f}")
            print(f"   最高评分: {max_score:.1f}")
            print(f"   最低评分: {min_score:.1f}")

        print("-" * 100)

        # 显示合格品种详情
        if not qualified_symbols.empty:
            print("🎯 合格品种详情 (将参与资金配置):")
            print(f"{'品种':<12} {'评分':<6} {'状态':<8} {'趋势':<6} {'周期':<8} {'突破':<6} {'波动':<6}")
            print("-" * 70)

            for _, row in qualified_symbols.sort_values('综合评分', ascending=False).iterrows():
                status_icon = {
                    '重点关注': '🔥',
                    '准备入场': '⚡',
                    '观察': '👀'
                }.get(row['状态'], '  ')

                print(f"{status_icon} {row['品种']:<10} {row['综合评分']:<6.1f} {row['状态']:<8} "
                      f"{row.get('趋势方向', ''):<6} {row.get('最佳周期', ''):<8} "
                      f"{row.get('突破强度', 0):<6.1f} {row.get('波动率评分', 0):<6.1f}")

        print("=" * 100)

    def save_screening_results_csv(self, screening_results: pd.DataFrame):
        """保存筛选评分结果到CSV"""
        if screening_results.empty:
            return None

        # 创建增强版筛选结果
        enhanced_df = screening_results.copy()
        enhanced_df = enhanced_df.sort_values('综合评分', ascending=False)

        # 添加评分等级
        def get_score_grade(score):
            if score >= 80:
                return 'A级-优秀'
            elif score >= 60:
                return 'B级-良好'
            elif score >= 40:
                return 'C级-一般'
            elif score >= 25:
                return 'D级-较差'
            else:
                return 'E级-很差'

        enhanced_df['评分等级'] = enhanced_df['综合评分'].apply(get_score_grade)

        # 添加操作建议
        def get_recommendation(row):
            if row['状态'] == '重点关注':
                return '立即关注'
            elif row['状态'] == '准备入场':
                return '准备建仓'
            elif row['状态'] == '观察':
                return '持续观察'
            else:
                return '暂不操作'

        enhanced_df['操作建议'] = enhanced_df.apply(get_recommendation, axis=1)

        # 重新排列列顺序
        column_order = [
            '品种', '综合评分', '评分等级', '状态', '操作建议',
            '趋势方向', '最佳周期', '突破强度', '波动率评分', '排除原因'
        ]

        available_columns = [col for col in column_order if col in enhanced_df.columns]
        enhanced_df = enhanced_df[available_columns]

        # 保存文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        current_dir = os.path.dirname(os.path.abspath(__file__))
        filename = f"筛选评分结果_{timestamp}.csv"
        filepath = os.path.join(current_dir, filename)

        enhanced_df.to_csv(filepath, index=False, encoding='utf-8-sig')

        print(f"\n💾 筛选评分结果已保存到: {filepath}")
        print(f"   包含 {len(enhanced_df)} 个品种的详细评分信息")

        qualified_count = len(enhanced_df[enhanced_df['状态'] != '排除'])
        focus_count = len(enhanced_df[enhanced_df['状态'] == '重点关注'])
        ready_count = len(enhanced_df[enhanced_df['状态'] == '准备入场'])

        print(f"   其中: 重点关注 {focus_count}个, 准备入场 {ready_count}个, 合格品种 {qualified_count}个")

        return filepath

    def adjust_positions_for_margin(self, df_allocation: pd.DataFrame) -> pd.DataFrame:
        """保证金超限时降仓处理"""
        if df_allocation.empty:
            return df_allocation

        # 计算总保证金需求
        total_margin = df_allocation['total_margin_needed'].sum()
        margin_limit = self.total_capital * self.total_margin_limit

        print(f"\n💰 保证金检查:")
        print(f"   总保证金需求: {total_margin:,.0f}元")
        print(f"   保证金上限: {margin_limit:,.0f}元 ({self.total_margin_limit*100:.0f}%)")
        print(f"   保证金占用率: {total_margin/self.total_capital*100:.1f}%")

        # 如果总保证金超限，需要调整仓位
        if total_margin > margin_limit:
            print(f"   ⚠️ 保证金超限，开始调整仓位...")

            # 按评分排序，优先保留高分品种
            df_sorted = df_allocation.sort_values('综合评分', ascending=False).copy()

            # 逐步减少仓位直到满足保证金限制
            while total_margin > margin_limit and len(df_sorted) > 0:
                # 找到最后一个品种（评分最低）
                last_idx = df_sorted.index[-1]
                last_row = df_sorted.loc[last_idx]

                if last_row['recommended_lots'] > 1:
                    # 减少1手
                    new_lots = last_row['recommended_lots'] - 1
                    new_opening_lots = new_lots * self.opening_multiplier
                    new_margin = last_row['margin_per_lot'] * new_opening_lots
                    new_risk = last_row['estimated_risk'] / last_row['opening_lots'] * new_opening_lots

                    df_sorted.loc[last_idx, 'recommended_lots'] = new_lots
                    df_sorted.loc[last_idx, 'opening_lots'] = new_opening_lots
                    df_sorted.loc[last_idx, 'total_margin_needed'] = new_margin
                    df_sorted.loc[last_idx, 'estimated_risk'] = new_risk
                    df_sorted.loc[last_idx, 'risk_ratio'] = new_risk / self.total_capital * 100
                    df_sorted.loc[last_idx, 'margin_ratio'] = new_margin / self.total_capital * 100

                    print(f"   调整 {last_row['symbol']}: {last_row['recommended_lots']}手 -> {new_lots}手")
                else:
                    # 移除该品种
                    print(f"   移除 {last_row['symbol']}: 无法满足保证金要求")
                    df_sorted = df_sorted.drop(last_idx)

                # 重新计算总保证金
                total_margin = df_sorted['total_margin_needed'].sum()

            print(f"   调整后总保证金: {total_margin:,.0f}元 ({total_margin/self.total_capital*100:.1f}%)")
            return df_sorted
        else:
            print(f"   ✅ 保证金使用正常")
            return df_allocation

    def generate_allocation_report(self, df_allocation: pd.DataFrame) -> None:
        """生成资金配置报告"""
        if df_allocation.empty:
            print("❌ 没有找到适合的交易品种")
            return
        
        print("=" * 80)
        print("📊 资金配置报告")
        print("=" * 80)
        
        # 总体统计
        total_margin_needed = df_allocation['total_margin_needed'].sum()
        total_risk = df_allocation['estimated_risk'].sum()
        margin_utilization = total_margin_needed / self.total_capital * 100
        risk_utilization = total_risk / self.total_capital * 100
        
        print(f"总资金: {self.total_capital:,.0f}元")
        print(f"需要保证金: {total_margin_needed:,.0f}元 ({margin_utilization:.1f}%)")
        print(f"预估总风险: {total_risk:,.0f}元 ({risk_utilization:.1f}%)")
        print(f"剩余资金: {self.total_capital - total_margin_needed:,.0f}元")
        print()
        
        # 详细配置表
        print("📋 详细配置方案:")
        print("-" * 120)
        print(f"{'品种':<12} {'评分':<6} {'状态':<8} {'建议手数':<8} {'开仓手数':<8} {'保证金':<12} {'预估风险':<12} {'风险比例':<8}")
        print("-" * 120)
        
        for _, row in df_allocation.iterrows():
            print(f"{row['symbol']:<12} {row['综合评分']:<6.1f} {row['状态']:<8} "
                  f"{row['recommended_lots']:<8} {row['opening_lots']:<8} "
                  f"{row['total_margin_needed']:<12,.0f} {row['estimated_risk']:<12,.0f} "
                  f"{row['risk_ratio']:<8.1f}%")
        
        print("-" * 120)
        print(f"{'合计':<12} {'':<6} {'':<8} {df_allocation['recommended_lots'].sum():<8} "
              f"{df_allocation['opening_lots'].sum():<8} {total_margin_needed:<12,.0f} "
              f"{total_risk:<12,.0f} {risk_utilization:<8.1f}%")
        
        # 风险提示和策略说明
        print()
        if self.aggressive_mode:
            print("⚡ 激进模式执行策略:")
            print("1. 全仓入场: 一次性建立全部仓位")
            print("2. 固定止盈: 盈利达2倍ATR时，平仓50%仓位")
            print("3. 浮动止盈: 剩余仓位使用1.5倍ATR移动止损")
            print("4. 保证金释放: 固定止盈后保证金减少40-50%")
            print("5. 隔夜控制: 波动率>70品种在14:55前平仓")
            print()

        print("⚠️  风险提示:")
        print("1. 策略开仓为2倍手数，固定止盈一半，浮动止盈一半")
        print("2. 建议手数为策略参数lots的建议值")
        print("3. 开仓手数为实际开仓时的手数（建议手数×2）")
        print("4. 预估风险基于历史波动率，实际风险可能不同")
        print("5. 请根据实际市场情况调整仓位")
        if self.aggressive_mode:
            print("6. 激进模式风险较高，请严格执行止损策略")
        
        return df_allocation

    def allocate_from_existing_results(self):
        """直接使用现有筛选结果进行资金配置"""
        print("正在获取保证金数据...")
        margin_data = self.get_margin_data()
        print(f"获取到 {len(margin_data)} 个品种的保证金数据")

        # 查找最新的筛选结果文件
        import glob
        current_dir = os.path.dirname(os.path.abspath(__file__))
        pattern = os.path.join(current_dir, "筛选评分结果_*.csv")
        result_files = glob.glob(pattern)

        if not result_files:
            print("❌ 未找到现有筛选结果文件，请先运行完整筛选")
            return pd.DataFrame(), []

        # 使用最新的文件
        latest_file = max(result_files, key=os.path.getmtime)
        print(f"发现现有筛选结果文件: {os.path.basename(latest_file)}")

        try:
            screening_df = pd.read_csv(latest_file, encoding='utf-8-sig')
            print(f"✓ 自动使用现有筛选结果，共 {len(screening_df)} 个品种")
        except Exception as e:
            print(f"❌ 读取筛选结果文件失败: {e}")
            return pd.DataFrame(), []

        # 转换为筛选结果格式
        screening_results = []
        for _, row in screening_df.iterrows():
            screening_results.append({
                'symbol': row['品种'],
                'score': row['综合评分'],
                'status': row['状态'],
                'trend': row.get('趋势方向', ''),
                'timeframe': row.get('最佳周期', ''),
                'breakout_score': 0.0,
                'volatility_score': 0.0
            })

        # 应用黑名单过滤
        original_count = len(screening_results)
        from system_config import is_blacklisted
        screening_results = [r for r in screening_results if not is_blacklisted(r['symbol'])]
        filtered_count = len(screening_results)

        print(f"黑名单过滤前: {original_count} 个品种")
        print(f"黑名单过滤后: {filtered_count} 个合格品种")

        if not screening_results:
            print("❌ 过滤后没有合格品种")
            return pd.DataFrame(), []

        # 显示筛选评分情况
        print("\n" + "=" * 100)
        print("📊 品种筛选评分情况".center(100))
        print("=" * 100)
        print(f"📈 评分统计:")
        print(f"   总品种数: {len(screening_results)}")
        print(f"   合格品种: {len(screening_results)} (100.0%)")

        # 按评分分类统计
        high_score = [r for r in screening_results if r['score'] >= 60]
        ready_score = [r for r in screening_results if 40 <= r['score'] < 60]
        watch_score = [r for r in screening_results if 25 <= r['score'] < 40]
        low_score = [r for r in screening_results if r['score'] < 25]

        print(f"   重点关注: {len(high_score)} (评分≥60)")
        print(f"   准备入场: {len(ready_score)} (评分40-59)")
        print(f"   观察品种: {len(watch_score)} (评分25-39)")
        print(f"   排除品种: {len(low_score)} (评分<25)")

        if screening_results:
            avg_score = sum(r['score'] for r in screening_results) / len(screening_results)
            max_score = max(r['score'] for r in screening_results)
            min_score = min(r['score'] for r in screening_results)
            print(f"   平均评分: {avg_score:.1f}")
            print(f"   最高评分: {max_score:.1f}")
            print(f"   最低评分: {min_score:.1f}")

        print("-" * 100)
        print("🎯 合格品种详情 (将参与资金配置):")
        print("品种           评分     状态       趋势     周期       突破     波动")
        print("-" * 70)

        for result in screening_results:
            status_icon = "⚡" if result['score'] >= 40 else "👀"
            print(f"{status_icon} {result['symbol']:<10} {result['score']:<6.1f} {result['status']:<8} {result['trend']:<8} {result['timeframe']:<8} {0.0:<8} {0.0:<6}")

        print("=" * 100)

        # 执行资金配置
        print(f"\n🔍 开始为 {len(screening_results)} 个合格品种计算资金配置...")
        allocation_results = []

        for result in screening_results:
            symbol = result['symbol']
            # 获取该品种的保证金数据
            margin_per_lot = None

            # 直接匹配
            if symbol in margin_data:
                margin_per_lot = margin_data[symbol]['margin_per_lot']
            else:
                # 尝试不同的格式匹配
                # 1. 尝试小写格式
                symbol_lower = symbol.lower()
                if symbol_lower in margin_data:
                    margin_per_lot = margin_data[symbol_lower]['margin_per_lot']
                else:
                    # 2. 尝试去掉交易所后缀
                    base_symbol = symbol.split('.')[0] if '.' in symbol else symbol
                    for key in margin_data.keys():
                        if key.split('.')[0].lower() == base_symbol.lower():
                            margin_per_lot = margin_data[key]['margin_per_lot']
                            break

            if margin_per_lot is not None:
                if margin_per_lot > 0:
                    allocation = self.calculate_position_size(symbol, margin_per_lot)
                else:
                    # 保证金为0的情况
                    allocation = {
                        'symbol': symbol,
                        'recommended_lots': 0,
                        '所需保证金': 0,
                        'calculation_details': {
                            'max_lots_by_margin': 0,
                            'max_lots_by_risk': 0,
                            'max_lots_by_position': 0
                        },
                        'limiting_factors': [f"保证金数据为0元，无法交易"]
                    }
            else:
                allocation = None

            if allocation:
                # 添加筛选信息
                allocation.update({
                    '综合评分': result['score'],
                    '状态': result['status'],
                    '趋势方向': result['trend'],
                    '最佳周期': result['timeframe']
                })
                allocation_results.append(allocation)
                if allocation['recommended_lots'] > 0:
                    print(f"  ✅ {symbol}: 推荐{allocation['recommended_lots']}手, 保证金{allocation['所需保证金']:.0f}元")
                else:
                    print(f"  ❌ {symbol}: 推荐0手, 保证金0元")
                    details = allocation['calculation_details']
                    print(f"     计算过程: 保证金限制{details['max_lots_by_margin']}手 | 风险限制{details['max_lots_by_risk']}手 | 仓位限制{details['max_lots_by_position']}手")
                    if allocation['limiting_factors']:
                        print(f"     限制原因: {'; '.join(allocation['limiting_factors'])}")
            else:
                print(f"  ❌ {symbol}: 未找到保证金数据")

        if not allocation_results:
            print("❌ 没有品种可以进行资金配置")
            return pd.DataFrame(), screening_results

        print(f"\n📊 配置结果: {len([r for r in allocation_results if r['recommended_lots'] > 0])} 个品种成功配置")

        # 转换为DataFrame
        df_allocation = pd.DataFrame(allocation_results)

        # 保证金检查和调整
        df_allocation = self.adjust_positions_for_margin(df_allocation)

        return df_allocation, screening_results

def main():
    """主函数"""
    print("期货资金配置程序")
    print("=" * 60)

    # 显示可用的预设配置
    print("可用的预设配置:")
    for i, (name, config) in enumerate(CAPITAL_PRESETS.items(), 1):
        print(f"  {i}. {name}: 资金{config['total_capital']:,}元, 最多{config['max_symbols']}个品种")
    print(f"  {len(CAPITAL_PRESETS)+1}. 自定义配置")
    print()

    # 用户选择配置
    try:
        choice = input("请选择配置 (直接回车使用默认10万元配置): ").strip()
        if not choice:
            preset_name = "10万元"
        elif choice.isdigit():
            choice_num = int(choice)
            if 1 <= choice_num <= len(CAPITAL_PRESETS):
                preset_name = list(CAPITAL_PRESETS.keys())[choice_num - 1]
            elif choice_num == len(CAPITAL_PRESETS) + 1:
                preset_name = None  # 自定义配置
            else:
                print("无效选择，使用默认配置")
                preset_name = "10万元"
        else:
            print("无效选择，使用默认配置")
            preset_name = "10万元"
    except:
        preset_name = "10万元"

    print(f"使用配置: {preset_name if preset_name else '自定义'}")
    print("=" * 60)

    # 创建资金配置器
    allocator = CapitalAllocation(preset_name)

    # 询问是否跳过筛选
    skip_screening = input("是否跳过筛选直接使用现有结果? (y/N): ").strip().lower()

    if skip_screening == 'y':
        # 直接使用现有筛选结果进行资金配置
        allocation_df, screening_results = allocator.allocate_from_existing_results()
    else:
        # 执行完整的筛选和配置
        allocation_df, screening_results = allocator.screen_and_allocate()

    # 保存筛选评分结果CSV
    try:
        allocator.save_screening_results_csv(screening_results)
    except Exception as e:
        print(f"保存筛选评分结果时出错: {e}")

    # 生成报告
    allocator.generate_allocation_report(allocation_df)

    # 保存结果
    if not allocation_df.empty:
        
        # 创建用于CSV保存的DataFrame，只包含中文列名，避免重复
        chinese_columns = [
            '品种', '推荐手数', '开仓手数', '每手保证金', '所需保证金',
            '预估风险', '风险比例(%)', '资金使用比例(%)', '风险等级',
            '综合风险系数', '交易所保证金比例(%)', '杠杆倍数', '最大仓位比例(%)',
            '综合评分', '状态', '趋势方向', '最佳周期', 'product'
        ]

        # 选择存在的中文列
        available_columns = [col for col in chinese_columns if col in allocation_df.columns]
        csv_df = allocation_df[available_columns].copy()

        # 重命名product列为中文
        if 'product' in csv_df.columns:
            csv_df = csv_df.rename(columns={'product': '品种类别'})

        # 确保保存到 futures_screening_system 目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        capital_str = f"{allocator.total_capital//10000}万" if allocator.total_capital >= 10000 else f"{allocator.total_capital}"

        # 获取当前脚本所在目录（futures_screening_system）
        current_dir = os.path.dirname(os.path.abspath(__file__))
        filename = f"资金配置方案_{capital_str}元_{timestamp}.csv"
        filepath = os.path.join(current_dir, filename)

        csv_df.to_csv(filepath, index=False, encoding='utf-8-sig')
        print(f"\n💾 配置方案已保存至: {filepath}")

        # 生成策略配置建议
        print(f"\n🎯 策略配置建议:")
        print(f"   将以下参数应用到fenzhouqiplus_strategy.py:")
        for _, row in allocation_df.iterrows():
            print(f"   {row['symbol']}: lots = {row['recommended_lots']}")

    return allocation_df

if __name__ == "__main__":
    main()
