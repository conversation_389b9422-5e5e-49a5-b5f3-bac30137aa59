.def("reqSubscribeTopic", &StockTdApi::reqSubscribeTopic)
.def("reqUserLogin", &StockTdApi::reqUserLogin)
.def("reqUserLogout", &StockTdApi::reqUserLogout)
.def("reqUserPasswordUpdate", &StockTdApi::reqUserPasswordUpdate)
.def("reqStockInsert", &StockTdApi::reqStockInsert)
.def("reqStockCancel", &StockTdApi::reqStockCancel)
.def("reqStockLock", &StockTdApi::reqStockLock)
.def("reqOptionsInsert", &StockTdApi::reqOptionsInsert)
.def("reqOptionsCancel", &StockTdApi::reqOptionsCancel)
.def("reqQuoteInsert", &StockTdApi::reqQuoteInsert)
.def("reqQuoteCancel", &StockTdApi::reqQuoteCancel)
.def("reqForQuote", &StockTdApi::reqForQuote)
.def("reqExercise", &StockTdApi::reqExercise)
.def("reqExerciseCancel", &StockTdApi::reqExerciseCancel)
.def("reqQryPartAccount", &StockTdApi::reqQryPartAccount)
.def("reqQryStockOrder", &StockTdApi::reqQryStockOrder)
.def("reqQryOptionsOrder", &StockTdApi::reqQryOptionsOrder)
.def("reqQryQuoteOrder", &StockTdApi::reqQryQuoteOrder)
.def("reqQryStockTrade", &StockTdApi::reqQryStockTrade)
.def("reqQryOptionsTrade", &StockTdApi::reqQryOptionsTrade)
.def("reqQryPosition", &StockTdApi::reqQryPosition)
.def("reqQryTopic", &StockTdApi::reqQryTopic)
.def("reqQryStock", &StockTdApi::reqQryStock)
.def("reqQryOptions", &StockTdApi::reqQryOptions)
.def("reqQryRate", &StockTdApi::reqQryRate)
.def("reqQryClient", &StockTdApi::reqQryClient)
.def("reqQryClientMargin", &StockTdApi::reqQryClientMargin)
.def("reqQryExercise", &StockTdApi::reqQryExercise)
.def("reqMarginCombAction", &StockTdApi::reqMarginCombAction)
.def("reqQrySseCombPosition", &StockTdApi::reqQrySseCombPosition)
.def("reqCombExercise", &StockTdApi::reqCombExercise)

.def("onFrontConnected", &StockTdApi::onFrontConnected)
.def("onFrontDisconnected", &StockTdApi::onFrontDisconnected)
.def("onHeartBeatWarning", &StockTdApi::onHeartBeatWarning)
.def("onPackageStart", &StockTdApi::onPackageStart)
.def("onPackageEnd", &StockTdApi::onPackageEnd)
.def("onRspSubscribeTopic", &StockTdApi::onRspSubscribeTopic)
.def("onRspUserLogin", &StockTdApi::onRspUserLogin)
.def("onRspUserLogout", &StockTdApi::onRspUserLogout)
.def("onRspUserPasswordUpdate", &StockTdApi::onRspUserPasswordUpdate)
.def("onRspStockInsert", &StockTdApi::onRspStockInsert)
.def("onRspStockCancel", &StockTdApi::onRspStockCancel)
.def("onRspOptionsInsert", &StockTdApi::onRspOptionsInsert)
.def("onRspOptionsCancel", &StockTdApi::onRspOptionsCancel)
.def("onRspQuoteInsert", &StockTdApi::onRspQuoteInsert)
.def("onRspForQuote", &StockTdApi::onRspForQuote)
.def("onRspQuoteCancel", &StockTdApi::onRspQuoteCancel)
.def("onRspStockLock", &StockTdApi::onRspStockLock)
.def("onRspExercise", &StockTdApi::onRspExercise)
.def("onRspExerciseCancel", &StockTdApi::onRspExerciseCancel)
.def("onRspQryPartAccount", &StockTdApi::onRspQryPartAccount)
.def("onRspQryStockOrder", &StockTdApi::onRspQryStockOrder)
.def("onRspQryOptionsOrder", &StockTdApi::onRspQryOptionsOrder)
.def("onRspQryQuoteOrder", &StockTdApi::onRspQryQuoteOrder)
.def("onRspQryStockTrade", &StockTdApi::onRspQryStockTrade)
.def("onRspQryOptionsTrade", &StockTdApi::onRspQryOptionsTrade)
.def("onRspQryPosition", &StockTdApi::onRspQryPosition)
.def("onRspQryTopic", &StockTdApi::onRspQryTopic)
.def("onRspQryStock", &StockTdApi::onRspQryStock)
.def("onRspQryOptions", &StockTdApi::onRspQryOptions)
.def("onRtnOptionsOrder", &StockTdApi::onRtnOptionsOrder)
.def("onRtnStockOrder", &StockTdApi::onRtnStockOrder)
.def("onRtnQuoteOrder", &StockTdApi::onRtnQuoteOrder)
.def("onRtnOptionsTrade", &StockTdApi::onRtnOptionsTrade)
.def("onRtnStockTrade", &StockTdApi::onRtnStockTrade)
.def("onRtnExercise", &StockTdApi::onRtnExercise)
.def("onRspQryRate", &StockTdApi::onRspQryRate)
.def("onRspQryClient", &StockTdApi::onRspQryClient)
.def("onRspQryClientMargin", &StockTdApi::onRspQryClientMargin)
.def("onRspQryExercise", &StockTdApi::onRspQryExercise)
.def("onRtnWithdrawDeposit", &StockTdApi::onRtnWithdrawDeposit)
.def("onRspMarginCombAction", &StockTdApi::onRspMarginCombAction)
.def("onRtnMarginCombAction", &StockTdApi::onRtnMarginCombAction)
.def("onRspQrySseCombPosition", &StockTdApi::onRspQrySseCombPosition)
.def("onRspCombExercise", &StockTdApi::onRspCombExercise)
;
