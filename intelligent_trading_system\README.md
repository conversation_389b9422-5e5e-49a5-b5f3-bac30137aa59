# 智能量化交易系统

一个基于vnpy框架的全自动化期货量化交易系统，提供品种筛选、资金分配、策略管理、风险控制等完整功能。

## 🚀 系统特性

### 核心功能
- **🔍 智能品种筛选**: 基于技术指标的多维度品种评估
- **💰 动态资金分配**: 智能资金分配和仓位管理
- **🔄 主力合约切换**: 自动检测和切换主力合约
- **📈 策略动态管理**: CTA策略的全生命周期管理
- **⏰ 定时任务调度**: 精确的交易时间调度系统
- **📊 实时系统监控**: 全方位的系统性能和交易监控

### 技术特点
- **模块化架构**: 松耦合的模块设计，易于维护和扩展
- **事件驱动**: 高效的事件驱动通信机制
- **多进程架构**: 基于zdrun.py的稳定多进程模型
- **配置驱动**: 统一的配置管理系统
- **类型安全**: 完整的类型注解和数据验证
- **全面测试**: 单元测试、集成测试、性能测试

## 📋 系统要求

### 最低配置
- **操作系统**: Windows 10/11, Ubuntu 18.04+, CentOS 7+
- **Python**: 3.8+
- **CPU**: 4核心 2.0GHz
- **内存**: 8GB RAM
- **存储**: 100GB 可用空间

### 推荐配置
- **操作系统**: Ubuntu 20.04 LTS
- **Python**: 3.9+
- **CPU**: 8核心 3.0GHz
- **内存**: 16GB RAM
- **存储**: 500GB SSD

## 🛠️ 快速安装

### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Linux/Mac:
source venv/bin/activate
# Windows:
venv\Scripts\activate
```

### 2. 安装依赖

```bash
# 安装核心依赖
pip install vnpy>=3.0.0
pip install ta-lib
pip install psutil
pip install schedule
pip install dataclasses-json
pip install numpy pandas matplotlib

# 或使用requirements.txt
pip install -r requirements.txt
```

### 3. TA-Lib安装

**Windows:**
```bash
pip install TA-Lib
```

**Linux:**
```bash
# 安装系统依赖
sudo apt-get install build-essential
wget http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-src.tar.gz
tar -xzf ta-lib-0.4.0-src.tar.gz
cd ta-lib/
./configure --prefix=/usr
make && sudo make install

# 安装Python包
pip install TA-Lib
```

## 🚀 快速开始

### 1. 基础配置

```bash
# 复制配置模板
cp config/system_config.py.template config/system_config.py
cp config/trading_config.py.template config/trading_config.py
cp config/risk_config.py.template config/risk_config.py

# 编辑CTP配置
nano config/ctp_config.json
```

### 2. 配置CTP接口

编辑 `config/ctp_config.json`:

```json
{
    "用户名": "your_username",
    "密码": "your_password",
    "经纪商代码": "9999",
    "交易服务器": "***************:10130",
    "行情服务器": "***************:10131",
    "产品名称": "simnow_client_test",
    "授权编码": "0000000000000000"
}
```

### 3. 运行系统

```bash
# 测试模式运行
python start_system.py --mode test

# 正常模式运行
python start_system.py

# 查看系统状态
python start_system.py --mode status

# 验证配置
python start_system.py --mode config
```

## 📁 项目结构

```
intelligent_trading_system/
├── main_controller.py          # 主控程序
├── start_system.py            # 系统启动脚本
├── test_main.py              # 系统测试程序
├── run_tests.py              # 测试运行脚本
├── config/                   # 配置模块
│   ├── system_config.py      # 系统配置
│   ├── trading_config.py     # 交易配置
│   └── risk_config.py        # 风险配置
├── core/                     # 核心模块
│   └── account_manager.py    # 账户管理
├── modules/                  # 功能模块
│   ├── screening/            # 筛选模块
│   │   ├── futures_scanner.py
│   │   └── evaluation_engine.py
│   ├── allocation/           # 分配模块
│   │   ├── capital_allocator.py
│   │   └── position_manager.py
│   ├── switching/            # 切换模块
│   │   ├── main_contract_detector.py
│   │   └── contract_switcher.py
│   ├── strategy/             # 策略模块
│   │   ├── strategy_manager.py
│   │   └── parameter_optimizer.py
│   ├── scheduler/            # 调度模块
│   │   ├── task_scheduler.py
│   │   └── event_manager.py
│   └── monitoring/           # 监控模块
│       ├── system_monitor.py
│       └── alert_manager.py
├── utils/                    # 工具模块
│   ├── logger.py            # 日志工具
│   └── helpers.py           # 辅助函数
├── tests/                    # 测试模块
├── docs/                     # 文档
└── logs/                     # 日志目录
```

## ⚙️ 系统配置

### 主要配置文件

1. **系统配置** (`config/system_config.py`)
   - 数据库配置
   - 日志配置
   - 调度器配置
   - 监控配置

2. **交易配置** (`config/trading_config.py`)
   - 筛选参数
   - 策略参数
   - 优化配置
   - 黑名单设置

3. **风险配置** (`config/risk_config.py`)
   - 仓位限制
   - 风险指标
   - 资金分配预设
   - 紧急停止设置

### 配置示例

```python
# 保守型配置
screening = ScreeningConfig(
    trend_threshold=0.7,      # 更高的趋势要求
    volatility_min=1.5,       # 更低的波动率要求
    volatility_max=2.5,
    max_positions=5           # 更少的持仓数
)

# 激进型配置
screening = ScreeningConfig(
    trend_threshold=0.6,      # 更宽松的趋势要求
    volatility_min=2.0,       # 更高的波动率要求
    volatility_max=4.0,
    max_positions=15          # 更多的持仓数
)
```

## 🕐 运行时间表

| 时间  | 任务                | 说明                |
|-------|--------------------|--------------------|
| 08:00 | 晨间品种筛选        | 分析昨日数据，筛选品种 |
| 10:15 | 资金分配           | 基于筛选结果分配资金  |
| 11:30 | 仓位调整           | 检查并调整当前仓位   |
| 15:25 | 主力合约切换        | 检测并切换主力合约   |
| 20:00 | 夜盘准备           | 系统检查，准备夜盘   |

## 🧪 测试系统

### 运行测试

```bash
# 运行所有测试
python run_tests.py --mode all

# 运行单元测试
python run_tests.py --mode unit

# 运行集成测试
python run_tests.py --mode integration

# 运行性能测试
python run_tests.py --mode performance

# 快速测试
python run_tests.py --mode quick

# 生成测试报告
python run_tests.py --mode report
```

### 测试覆盖率

```bash
# 运行覆盖率测试
python run_tests.py --mode coverage

# 查看覆盖率报告
# 报告将生成在 tests/coverage_report/ 目录
```

## 📊 监控和告警

### 系统监控指标

- **系统性能**: CPU、内存、磁盘、网络使用率
- **交易指标**: 持仓、盈亏、成交量、胜率
- **风险指标**: 最大回撤、夏普比率、风险敞口
- **运行状态**: 模块状态、连接状态、任务执行状态

### 告警设置

```python
# 告警配置示例
monitoring = MonitoringConfig(
    cpu_threshold=80.0,       # CPU使用率告警阈值
    memory_threshold=80.0,    # 内存使用率告警阈值
    disk_threshold=90.0,      # 磁盘使用率告警阈值
    alert_enabled=True        # 启用告警
)
```

## 🔧 故障排除

### 常见问题

1. **CTP连接失败**
   ```bash
   # 检查网络连接
   ping ***************

   # 验证账户信息
   python start_system.py --mode config
   ```

2. **数据库连接错误**
   ```bash
   # 检查数据库文件权限
   ls -la data/vnpy_data.db

   # 重新初始化数据库
   python -c "from vnpy.trader.database import database_manager; database_manager.init()"
   ```

3. **模块初始化失败**
   ```bash
   # 查看详细日志
   tail -f logs/system.log

   # 运行系统诊断
   python start_system.py --mode test
   ```

### 日志分析

```bash
# 查看系统日志
tail -f logs/system.log

# 查看错误日志
grep "ERROR" logs/system.log

# 查看特定模块日志
grep "FuturesScanner" logs/system.log
```

## 📚 文档

- [用户指南](docs/USER_GUIDE.md) - 详细的使用说明
- [开发者指南](docs/DEVELOPER_GUIDE.md) - 开发和扩展指南
- [API参考](docs/API_REFERENCE.md) - 完整的API文档
- [配置参考](docs/CONFIG_REFERENCE.md) - 配置参数详解
- [部署指南](docs/DEPLOYMENT_GUIDE.md) - 生产环境部署
- [系统架构](docs/ARCHITECTURE.md) - 系统架构设计

## 🤝 贡献

欢迎贡献代码和建议！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## ⚠️ 免责声明

本系统仅供学习和研究使用。使用本系统进行实盘交易的风险由用户自行承担。作者不对任何交易损失负责。

在使用本系统进行实盘交易前，请：
1. 充分理解系统逻辑和风险
2. 在模拟环境中充分测试
3. 设置合理的风险控制参数
4. 密切监控系统运行状态

## 📞 技术支持

如有问题或建议，请：
1. 查看 [FAQ](docs/USER_GUIDE.md#常见问题)
2. 搜索已有的 [Issues](../../issues)
3. 创建新的 [Issue](../../issues/new)

---

**⭐ 如果这个项目对您有帮助，请给个星标支持！**
