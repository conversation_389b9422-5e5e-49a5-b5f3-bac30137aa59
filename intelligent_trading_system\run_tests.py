"""
智能交易系统测试运行脚本
提供多种测试运行模式和详细的测试报告
"""

import os
import sys
import argparse
import unittest
import time
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(__file__))

# 导入测试模块
from tests import run_all_tests, run_unit_tests, run_integration_tests, run_performance_tests


def show_test_banner():
    """显示测试横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════════════════════╗
    ║                        智能交易系统测试套件 v1.0                              ║
    ║                   Intelligent Trading System Test Suite                      ║
    ║                                                                              ║
    ║  完整的单元测试、集成测试和性能测试                                            ║
    ║  确保系统稳定性、可靠性和性能                                                  ║
    ╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)


def run_specific_test_module(module_name):
    """运行特定测试模块"""
    print(f"运行测试模块: {module_name}")
    print("-" * 80)
    
    try:
        # 动态导入测试模块
        test_module = __import__(f'tests.{module_name}', fromlist=[module_name])
        
        # 创建测试套件
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(test_module)
        
        # 运行测试
        runner = unittest.TextTestRunner(
            verbosity=2,
            stream=sys.stdout,
            descriptions=True
        )
        
        result = runner.run(suite)
        
        # 返回测试结果
        return len(result.failures) == 0 and len(result.errors) == 0
    
    except ImportError as e:
        print(f"无法导入测试模块 {module_name}: {e}")
        return False
    except Exception as e:
        print(f"运行测试模块 {module_name} 时出错: {e}")
        return False


def run_quick_test():
    """运行快速测试（只测试基本功能）"""
    print("运行快速测试模式")
    print("只测试系统基本功能，跳过性能和集成测试")
    print("-" * 80)
    
    quick_test_modules = [
        'test_config',
        'test_account_manager',
        'test_screening',
        'test_allocation',
        'test_switching',
        'test_strategy',
        'test_scheduler',
        'test_monitoring'
    ]
    
    passed_tests = 0
    total_tests = len(quick_test_modules)
    
    for module in quick_test_modules:
        print(f"\n{'='*60}")
        print(f"测试模块: {module}")
        print('='*60)
        
        success = run_specific_test_module(module)
        if success:
            passed_tests += 1
            print(f"✓ {module} 测试通过")
        else:
            print(f"✗ {module} 测试失败")
    
    # 显示快速测试结果
    print(f"\n{'='*80}")
    print("快速测试结果摘要")
    print('='*80)
    print(f"总测试模块: {total_tests}")
    print(f"通过模块: {passed_tests}")
    print(f"失败模块: {total_tests - passed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    return passed_tests == total_tests


def run_coverage_test():
    """运行代码覆盖率测试"""
    print("运行代码覆盖率测试")
    print("需要安装 coverage 包: pip install coverage")
    print("-" * 80)
    
    try:
        import coverage
        
        # 创建覆盖率对象
        cov = coverage.Coverage()
        cov.start()
        
        # 运行所有测试
        success = run_all_tests()
        
        # 停止覆盖率收集
        cov.stop()
        cov.save()
        
        # 生成覆盖率报告
        print("\n" + "="*80)
        print("代码覆盖率报告")
        print("="*80)
        cov.report()
        
        # 生成HTML报告
        html_dir = os.path.join(os.path.dirname(__file__), 'htmlcov')
        cov.html_report(directory=html_dir)
        print(f"\nHTML覆盖率报告已生成: {html_dir}/index.html")
        
        return success
    
    except ImportError:
        print("未安装 coverage 包，无法运行覆盖率测试")
        print("请运行: pip install coverage")
        return False
    except Exception as e:
        print(f"覆盖率测试运行失败: {e}")
        return False


def run_stress_test():
    """运行压力测试"""
    print("运行压力测试")
    print("测试系统在高负载下的表现")
    print("-" * 80)
    
    # 这里可以添加专门的压力测试逻辑
    # 目前使用性能测试作为压力测试的基础
    
    print("执行性能测试作为压力测试基础...")
    result = run_performance_tests()
    
    print("\n压力测试完成")
    return result.wasSuccessful() if result else False


def generate_test_report():
    """生成测试报告"""
    print("生成详细测试报告")
    print("-" * 80)
    
    report_file = os.path.join(os.path.dirname(__file__), 'test_report.txt')
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("智能交易系统测试报告\n")
            f.write("="*80 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Python版本: {sys.version}\n")
            f.write(f"工作目录: {os.getcwd()}\n\n")
            
            # 运行测试并捕获输出
            original_stdout = sys.stdout
            
            class TestReportCapture:
                def __init__(self, file_handle):
                    self.file = file_handle
                    self.terminal = original_stdout
                
                def write(self, message):
                    self.file.write(message)
                    self.terminal.write(message)
                
                def flush(self):
                    self.file.flush()
                    self.terminal.flush()
            
            sys.stdout = TestReportCapture(f)
            
            try:
                # 运行所有测试
                success = run_all_tests()
                
                f.write(f"\n\n测试总结:\n")
                f.write(f"测试结果: {'通过' if success else '失败'}\n")
                f.write(f"报告文件: {report_file}\n")
                
            finally:
                sys.stdout = original_stdout
        
        print(f"测试报告已生成: {report_file}")
        return True
    
    except Exception as e:
        print(f"生成测试报告失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="智能交易系统测试运行脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
测试模式说明:
  all         - 运行所有测试（默认）
  unit        - 只运行单元测试
  integration - 只运行集成测试
  performance - 只运行性能测试
  quick       - 快速测试（基本功能）
  coverage    - 代码覆盖率测试
  stress      - 压力测试
  report      - 生成详细测试报告
  module      - 运行特定测试模块

示例:
  python run_tests.py                    # 运行所有测试
  python run_tests.py --mode unit        # 只运行单元测试
  python run_tests.py --mode quick       # 快速测试
  python run_tests.py --mode coverage    # 覆盖率测试
  python run_tests.py --mode module --module test_config  # 运行特定模块
        """
    )
    
    parser.add_argument(
        '--mode',
        choices=['all', 'unit', 'integration', 'performance', 'quick', 'coverage', 'stress', 'report', 'module'],
        default='all',
        help='测试模式 (默认: all)'
    )
    
    parser.add_argument(
        '--module',
        help='指定要运行的测试模块名称（当mode=module时使用）'
    )
    
    parser.add_argument(
        '--no-banner',
        action='store_true',
        help='不显示测试横幅'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='详细输出模式'
    )
    
    args = parser.parse_args()
    
    # 显示横幅
    if not args.no_banner:
        show_test_banner()
    
    # 显示测试信息
    print(f"测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试模式: {args.mode}")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print()
    
    # 记录开始时间
    start_time = time.time()
    success = False
    
    try:
        # 根据模式运行测试
        if args.mode == 'all':
            success = run_all_tests()
        elif args.mode == 'unit':
            result = run_unit_tests()
            success = result.wasSuccessful() if result else False
        elif args.mode == 'integration':
            result = run_integration_tests()
            success = result.wasSuccessful() if result else False
        elif args.mode == 'performance':
            result = run_performance_tests()
            success = result.wasSuccessful() if result else False
        elif args.mode == 'quick':
            success = run_quick_test()
        elif args.mode == 'coverage':
            success = run_coverage_test()
        elif args.mode == 'stress':
            success = run_stress_test()
        elif args.mode == 'report':
            success = generate_test_report()
        elif args.mode == 'module':
            if not args.module:
                print("错误: 使用 --mode module 时必须指定 --module 参数")
                return 1
            success = run_specific_test_module(args.module)
        else:
            print(f"未知的测试模式: {args.mode}")
            return 1
    
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return 0
    except Exception as e:
        print(f"测试运行异常: {e}")
        return 1
    
    # 显示测试总结
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n" + "="*80)
    print("测试总结")
    print("="*80)
    print(f"测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试持续时间: {duration:.2f}秒")
    print(f"测试结果: {'通过' if success else '失败'}")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
