"""
日志管理模块
提供统一的日志配置和管理功能
"""

import os
import logging
import logging.handlers
from typing import Optional
from pathlib import Path

from ..config import get_system_config


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = (
                f"{self.COLORS[record.levelname]}"
                f"{record.levelname}"
                f"{self.COLORS['RESET']}"
            )
        
        return super().format(record)


def setup_logging():
    """设置日志系统"""
    config = get_system_config()
    log_config = config.logging
    
    # 创建日志目录
    log_dir = Path(config.get_log_path())
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_config.level.upper()))
    
    # 清除现有处理器
    root_logger.handlers.clear()
    
    # 创建格式化器
    formatter = logging.Formatter(log_config.format)
    colored_formatter = ColoredFormatter(log_config.format)
    
    # 控制台处理器
    if log_config.console:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, log_config.level.upper()))
        console_handler.setFormatter(colored_formatter)
        root_logger.addHandler(console_handler)
    
    # 文件处理器
    if log_config.file:
        # 主日志文件
        main_log_file = log_dir / "trading_system.log"
        file_handler = logging.handlers.RotatingFileHandler(
            main_log_file,
            maxBytes=log_config.max_file_size * 1024 * 1024,  # 转换为字节
            backupCount=log_config.backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, log_config.level.upper()))
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
        
        # 错误日志文件
        error_log_file = log_dir / "error.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=log_config.max_file_size * 1024 * 1024,
            backupCount=log_config.backup_count,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        root_logger.addHandler(error_handler)
    
    # 设置第三方库日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('matplotlib').setLevel(logging.WARNING)
    
    return root_logger


def get_logger(name: str) -> logging.Logger:
    """获取指定名称的日志器"""
    # 确保日志系统已初始化
    if not logging.getLogger().handlers:
        setup_logging()
    
    return logging.getLogger(name)


class TradingLogger:
    """交易专用日志器"""
    
    def __init__(self, name: str):
        self.logger = get_logger(name)
        self.name = name
    
    def debug(self, msg: str, **kwargs):
        """调试日志"""
        self.logger.debug(f"[{self.name}] {msg}", **kwargs)
    
    def info(self, msg: str, **kwargs):
        """信息日志"""
        self.logger.info(f"[{self.name}] {msg}", **kwargs)
    
    def warning(self, msg: str, **kwargs):
        """警告日志"""
        self.logger.warning(f"[{self.name}] {msg}", **kwargs)
    
    def error(self, msg: str, **kwargs):
        """错误日志"""
        self.logger.error(f"[{self.name}] {msg}", **kwargs)
    
    def critical(self, msg: str, **kwargs):
        """严重错误日志"""
        self.logger.critical(f"[{self.name}] {msg}", **kwargs)
    
    def trade(self, msg: str, **kwargs):
        """交易日志"""
        self.logger.info(f"[TRADE-{self.name}] {msg}", **kwargs)
    
    def risk(self, msg: str, **kwargs):
        """风险日志"""
        self.logger.warning(f"[RISK-{self.name}] {msg}", **kwargs)
    
    def strategy(self, msg: str, **kwargs):
        """策略日志"""
        self.logger.info(f"[STRATEGY-{self.name}] {msg}", **kwargs)
    
    def system(self, msg: str, **kwargs):
        """系统日志"""
        self.logger.info(f"[SYSTEM-{self.name}] {msg}", **kwargs)


class LogContext:
    """日志上下文管理器"""
    
    def __init__(self, logger: logging.Logger, context: str):
        self.logger = logger
        self.context = context
        self.start_time = None
    
    def __enter__(self):
        from datetime import datetime
        self.start_time = datetime.now()
        self.logger.info(f"开始执行: {self.context}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        from datetime import datetime
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        if exc_type is None:
            self.logger.info(f"完成执行: {self.context} (耗时: {duration:.2f}秒)")
        else:
            self.logger.error(f"执行失败: {self.context} (耗时: {duration:.2f}秒) - {exc_val}")
        
        return False  # 不抑制异常


def log_execution_time(func):
    """装饰器：记录函数执行时间"""
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        with LogContext(logger, f"{func.__name__}"):
            return func(*args, **kwargs)
    return wrapper


def log_errors(func):
    """装饰器：记录函数异常"""
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行异常: {e}", exc_info=True)
            raise
    return wrapper


class PerformanceLogger:
    """性能日志器"""
    
    def __init__(self, name: str):
        self.logger = get_logger(f"Performance.{name}")
        self.metrics = {}
    
    def record_metric(self, name: str, value: float, unit: str = ""):
        """记录性能指标"""
        self.metrics[name] = {'value': value, 'unit': unit}
        self.logger.info(f"指标 {name}: {value} {unit}")
    
    def record_timing(self, name: str, duration: float):
        """记录时间指标"""
        self.record_metric(name, duration, "秒")
    
    def record_count(self, name: str, count: int):
        """记录计数指标"""
        self.record_metric(name, count, "次")
    
    def get_metrics(self) -> dict:
        """获取所有指标"""
        return self.metrics.copy()
    
    def clear_metrics(self):
        """清除所有指标"""
        self.metrics.clear()


# 全局性能日志器
_performance_loggers = {}


def get_performance_logger(name: str) -> PerformanceLogger:
    """获取性能日志器"""
    if name not in _performance_loggers:
        _performance_loggers[name] = PerformanceLogger(name)
    return _performance_loggers[name]


if __name__ == "__main__":
    # 测试日志系统
    setup_logging()
    
    # 测试基础日志
    logger = get_logger("TestModule")
    logger.debug("这是调试信息")
    logger.info("这是信息日志")
    logger.warning("这是警告日志")
    logger.error("这是错误日志")
    
    # 测试交易日志器
    trading_logger = TradingLogger("TestStrategy")
    trading_logger.trade("开仓买入 RB888 2手")
    trading_logger.risk("风险警告：保证金使用率过高")
    trading_logger.strategy("策略信号：多头信号确认")
    
    # 测试日志上下文
    with LogContext(logger, "测试任务"):
        import time
        time.sleep(0.1)
    
    # 测试性能日志器
    perf_logger = get_performance_logger("TestPerf")
    perf_logger.record_timing("数据处理", 0.5)
    perf_logger.record_count("处理品种数", 10)
    print(f"性能指标: {perf_logger.get_metrics()}")
    
    print("日志系统测试完成")
