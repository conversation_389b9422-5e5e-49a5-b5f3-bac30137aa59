ISTONEVersionType = "string"
ISTONE_USERS_TYPE = "enum"
ISTONE_USERS_EXTERNAL = "int"
ISTONE_USERS_OPERATOER = "int"
ISTONE_USERS_AUDITOR = "int"
ISTONE_LOG_LEVEL = "enum"
ISTONE_LOG_LEVEL_FATAL = "int"
ISTONE_LOG_LEVEL_ERROR = "int"
ISTONE_LOG_LEVEL_WARNING = "int"
ISTONE_LOG_LEVEL_INFO = "int"
ISTONE_LOG_LEVEL_DEBUG = "int"
ISTONE_LOG_LEVEL_TRACE = "int"
ISTONE_PROTOCOL_TYPE = "enum"
ISTONE_PROTOCOL_TCP = "int"
ISTONE_PROTOCOL_UDP = "int"
ISTONE_EXCHANGE_TYPE = "enum"
ISTONE_EXCHANGE_SH = "int"
ISTONE_EXCHANGE_SZ = "int"
ISTONE_EXCHANGE_UNKNOWN = "int"
ISTONE_MARKET_TYPE = "enum"
ISTONE_MKT_INIT = "int"
ISTONE_MKT_SZ_A = "int"
ISTONE_MKT_SH_A = "int"
ISTONE_MKT_UNKNOWN = "int"
ISTONE_TICKER_STATUS_TYPE = "enum"
ISTONE_PRICE_TYPE = "enum"
ISTONE_PRICE_LIMIT = "int"
ISTONE_PRICE_BEST_OR_CANCEL = "int"
ISTONE_PRICE_BEST5_OR_LIMIT = "int"
ISTONE_PRICE_BEST5_OR_CANCEL = "int"
ISTONE_PRICE_ALL_OR_CANCEL = "int"
ISTONE_PRICE_FORWARD_BEST = "int"
ISTONE_PRICE_REVERSE_BEST_LIMIT = "int"
ISTONE_PRICE_LIMIT_OR_CANCEL = "int"
ISTONE_PRICE_TYPE_UNKNOWN = "int"
ISTONE_SIDE_TYPE = "int"
ISTONE_POSITION_EFFECT_TYPE = "int"
ISTONE_ORDER_ACTION_STATUS_TYPE = "enum"
ISTONE_ORDER_ACTION_STATUS_SUBMITTED = "int"
ISTONE_ORDER_ACTION_STATUS_ACCEPTED = "int"
ISTONE_ORDER_ACTION_STATUS_REJECTED = "int"
ISTONE_ORDER_STATUS_TYPE = "enum"
ISTONE_ORDER_STATUS_INIT = "int"
ISTONE_ORDER_STATUS_ALLTRADED = "int"
ISTONE_ORDER_STATUS_PARTTRADEDQUEUEING = "int"
ISTONE_ORDER_STATUS_PARTTRADEDNOTQUEUEING = "int"
ISTONE_ORDER_STATUS_NOTRADEQUEUEING = "int"
ISTONE_ORDER_STATUS_CANCELED = "int"
ISTONE_ORDER_STATUS_REJECTED = "int"
ISTONE_ORDER_STATUS_UNKNOWN = "int"
ISTONE_ORDER_SUBMIT_STATUS_TYPE = "enum"
ISTONE_ORDER_SUBMIT_STATUS_INSERT_SUBMITTED = "int"
ISTONE_ORDER_SUBMIT_STATUS_INSERT_ACCEPTED = "int"
ISTONE_ORDER_SUBMIT_STATUS_INSERT_REJECTED = "int"
ISTONE_ORDER_SUBMIT_STATUS_CANCEL_SUBMITTED = "int"
ISTONE_ORDER_SUBMIT_STATUS_CANCEL_REJECTED = "int"
ISTONE_ORDER_SUBMIT_STATUS_CANCEL_ACCEPTED = "int"
ISTONE_TE_RESUME_TYPE = "enum"
ISTONE_TERT_RESTART = "int"
ISTONE_TERT_RESUME = "int"
ISTONE_TERT_QUICK = "int"
ETF_REPLACE_TYPE = "enum"
ERT_CASH_FORBIDDEN = "int"
ERT_CASH_OPTIONAL = "int"
ERT_CASH_MUST = "int"
ERT_CASH_RECOMPUTE_INTER_SZ = "int"
ERT_CASH_MUST_INTER_SZ = "int"
ERT_CASH_RECOMPUTE_INTER_OTHER = "int"
ERT_CASH_MUST_INTER_OTHER = "int"
EPT_INVALID = "int"
ISTONE_TICKER_TYPE = "enum"
ISTONE_TICKER_TYPE_SPOT = "int"
ISTONE_TICKER_TYPE_INDEX = "int"
ISTONE_TICKER_TYPE_OPTION = "int"
ISTONE_TICKER_TYPE_UNKNOWN = "int"
ISTONE_SECURITY_TYPE = "enum"
ISTONE_SECURITY_TYPE_STOCK = "int"
ISTONE_SECURITY_TYPE_FUND = "int"
ISTONE_SECURITY_TYPE_BOND = "int"
ISTONE_SECURITY_TYPE_INDEX = "int"
ISTONE_SECURITY_TYPE_OPTION = "int"
ISTONE_SECURITY_TYPE_UNKNOWN = "int"
ISTONE_BUSINESS_TYPE = "enum"
ISTONE_BUSINESS_TYPE_CASH = "int"
ISTONE_BUSINESS_TYPE_IPOS = "int"
ISTONE_BUSINESS_TYPE_REPO = "int"
ISTONE_BUSINESS_TYPE_ETF = "int"
ISTONE_BUSINESS_TYPE_MARGIN = "int"
ISTONE_BUSINESS_TYPE_DESIGNATION = "int"
ISTONE_BUSINESS_TYPE_ALLOTMENT = "int"
ISTONE_BUSINESS_TYPE_STRUCTURED_FUND_PURCHASE_REDEMPTION = "int"
ISTONE_BUSINESS_TYPE_STRUCTURED_FUND_SPLIT_MERGE = "int"
ISTONE_BUSINESS_TYPE_MONEY_FUND = "int"
ISTONE_BUSINESS_TYPE_OPTION = "int"
ISTONE_BUSINESS_TYPE_EXECUTE = "int"
ISTONE_BUSINESS_TYPE_FREEZE = "int"
ISTONE_BUSINESS_TYPE_UNKNOWN = "int"
ISTONE_ACCOUNT_TYPE = "enum"
ISTONE_ACCOUNT_NORMAL = "int"
ISTONE_ACCOUNT_CREDIT = "int"
ISTONE_ACCOUNT_DERIVE = "int"
ISTONE_ACCOUNT_UNKNOWN = "int"
ISTONE_FUND_TRANSFER_TYPE = "enum"
ISTONE_FUND_TRANSFER_OUT = "int"
ISTONE_FUND_TRANSFER_IN = "int"
ISTONE_FUND_INTER_TRANSFER_OUT = "int"
ISTONE_FUND_INTER_TRANSFER_IN = "int"
ISTONE_FUND_TRANSFER_UNKNOWN = "int"
ISTONE_FUND_OPER_STATUS = "enum"
ISTONE_FUND_OPER_PROCESSING = "int"
ISTONE_FUND_OPER_SUCCESS = "int"
ISTONE_FUND_OPER_FAILED = "int"
ISTONE_FUND_OPER_SUBMITTED = "int"
ISTONE_FUND_OPER_UNKNOWN = "int"
ISTONE_SPLIT_MERGE_STATUS = "enum"
ISTONE_SPLIT_MERGE_STATUS_ALLOW = "int"
ISTONE_SPLIT_MERGE_STATUS_ONLY_SPLIT = "int"
ISTONE_SPLIT_MERGE_STATUS_ONLY_MERGE = "int"
ISTONE_SPLIT_MERGE_STATUS_FORBIDDEN = "int"
ISTONE_TBT_TYPE = "enum"
ISTONE_TBT_ENTRUST = "int"
ISTONE_TBT_TRADE = "int"
ISTONE_OPT_CALL_OR_PUT_TYPE = "enum"
ISTONE_OPT_CALL = "int"
ISTONE_OPT_PUT = "int"
ISTONE_OPT_EXERCISE_TYPE_TYPE = "enum"
ISTONE_OPT_EXERCISE_TYPE_EUR = "int"
ISTONE_OPT_EXERCISE_TYPE_AME = "int"
ISTONE_POSITION_DIRECTION_TYPE = "enum"
ISTONE_POSITION_DIRECTION_NET = "int"
ISTONE_POSITION_DIRECTION_LONG = "int"
ISTONE_POSITION_DIRECTION_SHORT = "int"
ISTONE_POSITION_DIRECTION_COVERED = "int"
ISTONE_CRD_CR_STATUS = "enum"
ISTONE_CRD_CR_INIT = "int"
ISTONE_CRD_CR_SUCCESS = "int"
ISTONE_CRD_CR_FAILED = "int"
TXTPTradeTypeType = "char"
TXTPOrderTypeType = "char"
TIStoneOrderSysIDType = "string"
TIStoneTradeIDType = "string"
TIStoneUserIDType = "string"
TIStoneParticipantIDType = "string"
TIStoneIPAddressType = "string"
TIStoneIPAddressListType = "string"
TIStoneMacAddressType = "string"
TIStoneInstrumentNameType = "string"
TIStoneBranchIDType = "string"
TIStoneInstrumentIDType = "string"
TIStoneExchangeInstIDType = "string"
TIStoneIdentifiedCardNoType = "string"
TIStoneIdentifiedCardTypeType = "string"
TIStoneExchangeIDType = "string"
TIStoneExchangeNameType = "string"
TIStoneDateType = "string"
TIStoneTimeType = "string"
TIStoneClientTypeType = "char"
TIStoneClientNameType = "string"
TIStoneClientIDType = "string"
TIStoneIdCardTypeType = "string"
TIStoneAccountIDType = "string"
TIStoneSeatIDType = "string"
TIStoneProductNameType = "string"
TIStoneOrderLocalIDType = "string"
TIStoneInvestorIDType = "string"
TIStoneInvestUnitIDType = "string"
TIStoneUserNameType = "string"
TIStonePasswordType = "string"
TIStoneAbstractType = "string"
TIStoneComeFromType = "string"
TIStoneContentType = "string"
TIStoneURLLinkType = "string"
TIStoneProductInfoType = "string"
TIStoneProtocolInfoType = "string"
TIStoneBusinessUnitType = "string"
TIStoneTradingSystemNameType = "string"
TIStoneBrokerIDType = "string"
TIStoneCustomType = "string"
TIStoneTradingDayType = "string"
TIStoneDepartmentType = "string"
TIStoneGrantFuncSetType = "string"
TIStoneProductIDType = "string"
TIStoneAccountSeqNoType = "string"
TIStoneSettlementGroupIDType = "string"
TIStoneBankIDType = "string"
TIStoneBankBrchIDType = "string"
TIStoneBankAccountType = "string"
TIStoneNameType = "string"
TIStoneCommentType = "string"
TIStoneTradeCodeType = "string"
TIStoneSerialType = "string"
TIStoneDeviceIDType = "string"
TIStoneBankCodingForFutureType = "string"
TIStoneErrorMsgType = "string"
TIStoneMarketIDType = "string"
TIStoneNewsTypeType = "string"
TIStoneNewsUrgencyType = "char"
TIStoneInitPasswordType = "string"
TIStoneAuthSerialNoType = "string"
TIStoneAuthCodeType = "string"
