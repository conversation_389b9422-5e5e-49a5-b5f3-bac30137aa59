KS_SPOT = "00"
KS_DEFER = "10"
KS_FUTURES = "11"
KS_FORWARD = "01"
KS_DELIVERY = "AP"
KS_MIDDLE = "MD"
KS_I_INITING = '0'
KS_I_INIT = '1'
KS_I_BEGIN = '2'
KS_I_GRP_ORDER = '3'
KS_I_GRP_MATCH = '4'
KS_I_NORMAL = '5'
KS_I_PAUSE = '6'
KS_I_DERY_APP = '7'
KS_I_DERY_MATCH = '8'
KS_I_MID_APP = '9'
KS_I_MID_MATCH = 'A'
KS_I_END = 'B'
KS_M_INITING = '0'
KS_M_INIT = '1'
KS_M_OPEN = '2'
KS_M_TRADE = '3'
KS_M_PAUSE = '4'
KS_M_CLOSE = '5'
KS_Entrust_Sending = '1'
KS_Entrust_Waiting = '2'
KS_Entrust_In = '3'
KS_Entrust_All_Cancel = '4'
KS_Entrust_All_Done = '5'
KS_Entrust_Part_Done = '6'
KS_Entrust_Part_Done_Cancel = '7'
KS_Entrust_Wait_Cancel = '8'
KS_Entrust_Error = '9'
KS_Entrust_By_Exch = 'A'
KS_Entrust_By_Emergency = 'B'
KS_Entrust_By_Auto_Cancel = 'C'
KS_CONDITION_MARKET_PRICE = '0'
KS_CONDITION_BUYFIVE_PRICE = '1'
KS_CONDITION_SELLFIVE_PRICE = '2'
KS_CONDITION_COMMON_PRICE = '3'
KS_CONDITION_BUYONE_PRICE = '4'
KS_CONDITION_SELLONE_PRICE = '5'
KS_CONDITION_BUYTWO_PRICE = '6'
KS_CONDITION_SELLTWO_PRICE = '7'
KS_CONDITION_BUYTHREE_PRICE = '8'
KS_CONDITION_SELLTHREE_PRICE = '9'
KS_CONDITION_BUYFOUR_PRICE = 'a'
KS_CONDITION_SELLFOUR_PRICE = 'b'
KS_CONDITION_PROFIT_ORDER = '0'
KS_CONDITION_LOSS_ORDER = '1'
KS_CONDITION_ORDER_NOTTRIGGER = '0'
KS_CONDITION_ORDER_SUCCESS = '1'
KS_CONDITION_ORDER_STATUS = '2'
KS_CONDITION_ORDER_NOTUSE = '3'
KS_CONDITION_ORDER_EXPIRED = '4'
KS_CONDITION_ORDER_PROCESSING = '5'
KS_P_OPEN = '0'
KS_P_OFFSET = '1'
KS_BUY = '0'
KS_SELL = '1'
KS_SPOT_ENTRUST = '0'
KS_TN_ENTRUST = '4'
KS_DEFER_ENTRUST = '1'
KS_DELIVERY_ENTRUST = '2'
KS_MIDDLE_ENTRUST = '3'
KS_COUNTER_CHANNEL = '1'
KS_TEL_CHANNEL = '2'
KS_NET_CHANNEL = '3'
KS_TRADER_CHANNEL = '4'
KS_SELF_CHANNEL = '5'
KS_PHONE_CHANNEL = '6'
KS_TRADEAPI_CHANNEL = '7'
KS_CLIENT_SPOT = '0'
KS_CLIENT_FUTURE = '1'
KS_CLIENT_GENERAL = '2'
KS_BANKACC_TYPE = "1"
KS_TRADECODE_TYPE = "2"
KS_ACTIVITY_ON = '1'
KS_VARIETY_CODE_GOLD = 0
KS_VARIETY_CODE_AG = 1
KS_VARIETY_CODE_PT = 2
KS_LOG_CLEAR_DAY = 3
KS_FRONTCONNECTED = 1
KS_DISCONNECTED = 2
KS_RECONNECTING = 3
KS_ONSTATUSCHANGE = 4
KS_STATE_INIT = '0'
KS_STATE_READY = '1'
KS_STATE_ONLINE = '2'
KS_STATE_LOGON = '3'
KS_COMM_INSTRUCTION = '0'
KS_FOK_INSTRUCTION = '1'
KS_FAK_INSTRUCTION = '2'
KS_MP2FP_INSTUCTION = '3'
KS_MPFOK_INSTUCTION = '4'
KS_MPFAK_INSTUCTION = '5'
ETF_SCRIPTION = "020"
ETF_PURCHASE = "022"
ETF_REDEEM = "024"
ETF_PURCHASE_REQ = "101"
ETF_PURCHASE_RETURN_SUCCESS = "102"
ETF_PURCHASE_RETURN_FAIL = "103"
ETF_PURCHASE_CONFIRM_SUCCESS = "104"
ETF_PURCHASE_CONFIRM_FAIL = "105"
ETF_SCRIPTION_REQ = "201"
ETF_SCRIPTION_RETURN_SUCCESS = "202"
ETF_SCRIPTION_RETURN_FAIL = "203"
ETF_SCRIPTION_SUCCESS = "204"
ETF_SCRIPTION_FAIL = "205"
ETF_SCRIPTION_CONFIRM_SUCCESS = "206"
ETF_SCRIPTION_CONFIRM_FAIL = "207"
ETF_TRANSFER_REQ = "208"
ETF_REDEEM_REQ = "301"
ETF_REDEEM_CONFIRM_SUCCESS = "304"
ETF_REDEEM_TRANSFER_FAIL = "305"
ETF_FROZEN_REQ = "401"
ETF_FROZEN_SUCCESS = "402"
ETF_FROZEN_FAIL = "403"
ETF_TRANSFER_SUCCESS = "404"
ETF_TRANSFER_FAIL = "405"
ETF_UNFROZEN_SUCCESS = "406"
ETF_UNFROZEN_FAIL = "407"
ETF_FUNDUNIT_DECREASE_REQ = "501"
ETF_FUNDUNIT_DECREASE_SUCCESS = "502"
ETF_FUNDUNIT_DECREASE_FAIL = "503"
ETF_FUNDUNIT_INCREASE_REQ = "601"
ETF_FUNDUNIT_INCREASE_SUCCESS = "602"
ETF_FUNDUNIT_INCREASE_FAIL = "603"
ETF_ERR_ENTRUST = "901"
ETF_ACCOUNT_UNBINDING = "000"
ETF_ACCOUNT_BINDING_WAITIN_GCONFIRM = "701"
ETF_ACCOUNT_BINDING_FINISH = "702"
ETF_ACCOUNT_BINDING_FAIL = "703"
ETF_ACCOUNT_BINDING_SEND = "704"
ETF_ACCOUNT_UNBINDING_WAITIN_GCONFIRM = "801"
ETF_ACCOUNT_UNBINDING_FINISH = "802"
ETF_ACCOUNT_UNBINDING_FAIL = "803"
ETF_ACCOUNT_UNBINDING_SEND = "804"
KS_CONDITION_FIRSTLOGIN = 1
KSGOLDERROR_INPUTNULL = 1000
KSGOLDERROR_NOTLOGIN = 1001
KSGOLDERROR_PARAMETER_EXCEPTION = 1002
KSGOLDERROR_OVERLOAD = 1003
KSGOLDERROR_NOTCONNECTGATEWAY = 1004
KSGOLDERROR_ALREADLOGIN = 1005
KSGOLDERROR_PRICEILLEGAL = 1006
KSGOLDERROR_ORDERREFTOOLOW = 1007
KSGOLDERROR_GATAWAYINITFAIL = 1008
KSGOLDERROR_ORDERREFSESSIONIDCHECKFAIL = 1009
KSGOLDERROR_FLOWCONTROLCHECKFAIL = 1010
KSGOLDERROR_FIRSTLOGINNEEDMODPWD = 1099
KSGOLDERROR_VERSIONCHECKFAIL = -1011
KSGOLDERROR_TRADERACCOUNTFUND = -1012
KSGOLDERROR_USERLOGINEFAIL = -1013
KSGOLDERROR_USERLOGOUTFAIL = -1014
KSGOLDERROR_SPOTINSTQUERYFAIL = -1015
KSGOLDERROR_TNINSTQUERYFAIL = -1016
KSGOLDERROR_DEFERINSTQUERYFAIL = -1017
KSGOLDERROR_DELIVERYCODEQUERYFAIL = -1018
KSGOLDERROR_INSTCACHNOTEXIST = -1019
KSGOLDERROR_INSTNOTFOUND = -1020
KSGOLDERROR_DELIVERYCODECACHNOTEXIST = -1021
KSGOLDERROR_DELIVERYCODENOTFOUND = -1022
KSGOLDERROR_INITORDERQUERYERROR = -1023
KSGOLDERROR_INITDONEQUERYERROR = -1024
KSGOLDERROR_INITDONEQUERYGETNEXTERROR = -1025
KSGOLDERROR_INITORDERQUERYGETNEXTERROR = -1026
KSGOLDERROR_QUERYCONNECTIONLOGINFAIL = -1027
KSGOLDERROR_ORDERACTIONFAIL = -1028
KSGOLDERROR_CONDITIONORDERACTIONFAIL = -1029
KSGOLDERROR_SUBMARKETDATAOPERATORFAIL = -1030
KSGOLDERROR_QUERYRECORDNOTFOUND = -1031
