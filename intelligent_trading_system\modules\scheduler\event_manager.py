"""
事件管理器
提供事件驱动机制，支持系统内部组件间的异步通信
"""

import threading
import queue
from typing import Dict, List, Callable, Any, Optional
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
import weakref

from ...utils.logger import get_logger


class EventType(Enum):
    """事件类型"""
    # 系统事件
    SYSTEM_START = "system_start"
    SYSTEM_STOP = "system_stop"
    SYSTEM_ERROR = "system_error"
    
    # 账户事件
    ACCOUNT_LOGIN = "account_login"
    ACCOUNT_LOGOUT = "account_logout"
    ACCOUNT_ERROR = "account_error"
    
    # 交易事件
    ORDER_SENT = "order_sent"
    ORDER_FILLED = "order_filled"
    ORDER_CANCELLED = "order_cancelled"
    POSITION_CHANGED = "position_changed"
    
    # 策略事件
    STRATEGY_START = "strategy_start"
    STRATEGY_STOP = "strategy_stop"
    STRATEGY_ERROR = "strategy_error"
    
    # 数据事件
    TICK_DATA = "tick_data"
    BAR_DATA = "bar_data"
    
    # 调度事件
    TASK_START = "task_start"
    TASK_COMPLETE = "task_complete"
    TASK_FAILED = "task_failed"
    
    # 风险事件
    RISK_WARNING = "risk_warning"
    RISK_LIMIT_EXCEEDED = "risk_limit_exceeded"
    
    # 自定义事件
    CUSTOM = "custom"


@dataclass
class Event:
    """事件对象"""
    type: EventType
    data: Any = None
    timestamp: datetime = None
    source: str = ""
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class EventManager:
    """事件管理器"""
    
    def __init__(self, max_queue_size: int = 1000):
        """初始化事件管理器"""
        self.logger = get_logger("EventManager")
        
        # 事件队列
        self.event_queue = queue.Queue(maxsize=max_queue_size)
        
        # 事件处理器注册表
        self.handlers: Dict[EventType, List[Callable]] = {}
        
        # 弱引用处理器（避免内存泄漏）
        self.weak_handlers: Dict[EventType, List[weakref.ref]] = {}
        
        # 事件处理线程
        self.running = False
        self.event_thread: Optional[threading.Thread] = None
        
        # 事件统计
        self.event_stats: Dict[EventType, int] = {}
        self.total_events = 0
        
        # 线程锁
        self.lock = threading.RLock()
        
        self.logger.info("事件管理器初始化完成")
    
    def start(self) -> bool:
        """启动事件管理器"""
        try:
            if self.running:
                self.logger.warning("事件管理器已在运行中")
                return True
            
            self.running = True
            self.event_thread = threading.Thread(target=self._process_events, daemon=True)
            self.event_thread.start()
            
            self.logger.info("事件管理器启动成功")
            return True
        
        except Exception as e:
            self.logger.error(f"启动事件管理器失败: {e}")
            return False
    
    def stop(self) -> bool:
        """停止事件管理器"""
        try:
            if not self.running:
                self.logger.warning("事件管理器未在运行")
                return True
            
            self.running = False
            
            # 发送停止事件
            self.put_event(Event(EventType.SYSTEM_STOP, source="EventManager"))
            
            # 等待事件处理线程结束
            if self.event_thread and self.event_thread.is_alive():
                self.event_thread.join(timeout=5)
            
            self.logger.info("事件管理器停止成功")
            return True
        
        except Exception as e:
            self.logger.error(f"停止事件管理器失败: {e}")
            return False
    
    def put_event(self, event: Event) -> bool:
        """放入事件到队列"""
        try:
            if not self.running:
                return False
            
            # 非阻塞放入
            self.event_queue.put_nowait(event)
            
            with self.lock:
                self.total_events += 1
                self.event_stats[event.type] = self.event_stats.get(event.type, 0) + 1
            
            return True
        
        except queue.Full:
            self.logger.warning("事件队列已满，丢弃事件")
            return False
        except Exception as e:
            self.logger.error(f"放入事件失败: {e}")
            return False
    
    def register_handler(self, event_type: EventType, handler: Callable[[Event], None]) -> bool:
        """注册事件处理器"""
        try:
            with self.lock:
                if event_type not in self.handlers:
                    self.handlers[event_type] = []
                
                self.handlers[event_type].append(handler)
            
            self.logger.debug(f"注册事件处理器: {event_type.value}")
            return True
        
        except Exception as e:
            self.logger.error(f"注册事件处理器失败: {e}")
            return False
    
    def register_weak_handler(self, event_type: EventType, handler: Callable[[Event], None]) -> bool:
        """注册弱引用事件处理器"""
        try:
            with self.lock:
                if event_type not in self.weak_handlers:
                    self.weak_handlers[event_type] = []
                
                weak_ref = weakref.ref(handler)
                self.weak_handlers[event_type].append(weak_ref)
            
            self.logger.debug(f"注册弱引用事件处理器: {event_type.value}")
            return True
        
        except Exception as e:
            self.logger.error(f"注册弱引用事件处理器失败: {e}")
            return False
    
    def unregister_handler(self, event_type: EventType, handler: Callable[[Event], None]) -> bool:
        """注销事件处理器"""
        try:
            with self.lock:
                if event_type in self.handlers and handler in self.handlers[event_type]:
                    self.handlers[event_type].remove(handler)
                    self.logger.debug(f"注销事件处理器: {event_type.value}")
                    return True
            
            return False
        
        except Exception as e:
            self.logger.error(f"注销事件处理器失败: {e}")
            return False
    
    def emit_event(self, event_type: EventType, data: Any = None, source: str = "") -> bool:
        """发射事件（便捷方法）"""
        event = Event(type=event_type, data=data, source=source)
        return self.put_event(event)
    
    def get_event_stats(self) -> Dict[str, Any]:
        """获取事件统计"""
        with self.lock:
            stats = {
                'total_events': self.total_events,
                'queue_size': self.event_queue.qsize(),
                'running': self.running,
                'event_types': {}
            }
            
            for event_type, count in self.event_stats.items():
                stats['event_types'][event_type.value] = count
            
            return stats
    
    def clear_stats(self):
        """清除统计信息"""
        with self.lock:
            self.event_stats.clear()
            self.total_events = 0
        
        self.logger.info("事件统计信息已清除")
    
    def _process_events(self):
        """处理事件主循环"""
        self.logger.info("事件处理线程启动")
        
        while self.running:
            try:
                # 获取事件（阻塞等待，超时1秒）
                event = self.event_queue.get(timeout=1)
                
                # 处理事件
                self._handle_event(event)
                
                # 标记任务完成
                self.event_queue.task_done()
            
            except queue.Empty:
                # 超时，继续循环
                continue
            except Exception as e:
                self.logger.error(f"处理事件异常: {e}")
        
        self.logger.info("事件处理线程结束")
    
    def _handle_event(self, event: Event):
        """处理单个事件"""
        try:
            # 处理强引用处理器
            with self.lock:
                handlers = self.handlers.get(event.type, []).copy()
            
            for handler in handlers:
                try:
                    handler(event)
                except Exception as e:
                    self.logger.error(f"事件处理器执行失败: {e}")
            
            # 处理弱引用处理器
            with self.lock:
                weak_handlers = self.weak_handlers.get(event.type, []).copy()
            
            # 清理失效的弱引用
            valid_weak_handlers = []
            for weak_ref in weak_handlers:
                handler = weak_ref()
                if handler is not None:
                    valid_weak_handlers.append(weak_ref)
                    try:
                        handler(event)
                    except Exception as e:
                        self.logger.error(f"弱引用事件处理器执行失败: {e}")
            
            # 更新弱引用列表
            if len(valid_weak_handlers) != len(weak_handlers):
                with self.lock:
                    self.weak_handlers[event.type] = valid_weak_handlers
        
        except Exception as e:
            self.logger.error(f"处理事件失败: {e}")
    
    def display_event_summary(self):
        """显示事件摘要"""
        stats = self.get_event_stats()
        
        self.logger.info("=" * 80)
        self.logger.info("事件管理摘要")
        self.logger.info("=" * 80)
        self.logger.info(f"运行状态: {'运行中' if stats['running'] else '已停止'}")
        self.logger.info(f"总事件数: {stats['total_events']}")
        self.logger.info(f"队列大小: {stats['queue_size']}")
        
        if stats['event_types']:
            self.logger.info("\n事件类型统计:")
            for event_type, count in sorted(stats['event_types'].items()):
                self.logger.info(f"{event_type:25}: {count:8}")
        
        # 显示处理器统计
        with self.lock:
            handler_count = sum(len(handlers) for handlers in self.handlers.values())
            weak_handler_count = sum(len(handlers) for handlers in self.weak_handlers.values())
        
        self.logger.info(f"\n注册的处理器数量:")
        self.logger.info(f"强引用处理器: {handler_count}")
        self.logger.info(f"弱引用处理器: {weak_handler_count}")


# 全局事件管理器实例
_global_event_manager: Optional[EventManager] = None


def get_event_manager() -> EventManager:
    """获取全局事件管理器"""
    global _global_event_manager
    if _global_event_manager is None:
        _global_event_manager = EventManager()
    return _global_event_manager


def emit_event(event_type: EventType, data: Any = None, source: str = "") -> bool:
    """发射事件（全局便捷函数）"""
    return get_event_manager().emit_event(event_type, data, source)


if __name__ == "__main__":
    # 测试事件管理器
    event_manager = EventManager()
    
    # 注册事件处理器
    def test_handler(event: Event):
        print(f"处理事件: {event.type.value} - {event.data} - {event.timestamp}")
    
    event_manager.register_handler(EventType.SYSTEM_START, test_handler)
    event_manager.register_handler(EventType.CUSTOM, test_handler)
    
    # 启动事件管理器
    event_manager.start()
    
    # 发射测试事件
    event_manager.emit_event(EventType.SYSTEM_START, "系统启动", "测试")
    event_manager.emit_event(EventType.CUSTOM, "自定义事件", "测试")
    
    # 等待事件处理
    import time
    time.sleep(1)
    
    # 显示事件摘要
    event_manager.display_event_summary()
    
    # 停止事件管理器
    event_manager.stop()
    
    print("事件管理器测试完成")
