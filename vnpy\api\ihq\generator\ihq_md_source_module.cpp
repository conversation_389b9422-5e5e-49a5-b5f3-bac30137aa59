.def("reqUserLogin", &MdApi::reqUserLogin)
.def("reqUserLogout", &MdApi::reqUserLogout)
.def("SubAllMarketData", &MdApi::SubAllMarketData)
.def("UnSubAllMarketData", &MdApi::UnSubAllMarketData)
.def("SubAllTickByTick", &MdApi::SubAllTickByTick)
.def("UnSubAllTickByTick", &MdApi::UnSubAllTickByTick)
.def("SubMarketData", &MdApi::SubMarketData)
.def("UnSubMarketData", &MdApi::UnSubMarketData)
.def("SubTickByTick", &MdApi::SubTickByTick)
.def("UnSubTickByTick", &MdApi::UnSubTickByTick)
.def("SubAllOrderBook", &MdApi::SubAllOrderBook)
.def("UnSubAllOrderBook", &MdApi::UnSubAllOrderBook)
.def("SubOrderBook", &MdApi::SubOrderBook)
.def("UnSubOrderBook", &MdApi::UnSubOrderBook)

.def("onFrontConnected", &MdApi::onFrontConnected)
.def("onFrontDisconnected", &MdApi::onFrontDisconnected)
.def("onHeartBeatWarning", &MdApi::onHeartBeatWarning)
.def("onRspError", &MdApi::onRspError)
.def("onRspUserLogin", &MdApi::onRspUserLogin)
.def("onRspUserLogout", &MdApi::onRspUserLogout)
.def("onRspSubAllMarketData", &MdApi::onRspSubAllMarketData)
.def("onRspUnSubAllMarketData", &MdApi::onRspUnSubAllMarketData)
.def("onRspSubAllTickByTick", &MdApi::onRspSubAllTickByTick)
.def("onRspUnSubAllTickByTick", &MdApi::onRspUnSubAllTickByTick)
.def("onRspSubMarketData", &MdApi::onRspSubMarketData)
.def("onRspUnSubMarketData", &MdApi::onRspUnSubMarketData)
.def("onRspSubTickByTick", &MdApi::onRspSubTickByTick)
.def("onRspUnSubTickByTick", &MdApi::onRspUnSubTickByTick)
.def("onRtnDepthMarketData", &MdApi::onRtnDepthMarketData)
.def("onRtnTickByTick", &MdApi::onRtnTickByTick)
.def("onRtnOrderBook", &MdApi::onRtnOrderBook)
.def("onRspSubOrderBook", &MdApi::onRspSubOrderBook)
.def("onRspUnSubOrderBook", &MdApi::onRspUnSubOrderBook)
;
