"""
任务调度器
提供定时任务调度功能，支持每日自动化操作
"""

import schedule
import threading
import time
from typing import Dict, List, Callable, Optional, Any
from datetime import datetime, time as dt_time
from dataclasses import dataclass
from enum import Enum

from ...config import get_system_config
from ...utils.logger import get_logger


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "待执行"
    RUNNING = "执行中"
    COMPLETED = "已完成"
    FAILED = "执行失败"
    CANCELLED = "已取消"


@dataclass
class ScheduledTask:
    """调度任务"""
    name: str
    func: Callable
    schedule_time: str  # 格式: "HH:MM"
    description: str = ""
    enabled: bool = True
    last_run: Optional[datetime] = None
    status: TaskStatus = TaskStatus.PENDING
    error_msg: str = ""


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self):
        """初始化任务调度器"""
        self.logger = get_logger("TaskScheduler")
        self.system_config = get_system_config()
        
        # 任务管理
        self.tasks: Dict[str, ScheduledTask] = {}
        self.running = False
        self.scheduler_thread: Optional[threading.Thread] = None
        
        # 默认任务配置
        self.default_tasks = {
            "08:00": "品种筛选",
            "10:15": "资金分配", 
            "11:30": "仓位调整",
            "15:25": "主力合约切换",
            "20:00": "夜盘准备"
        }
        
        self.logger.info("任务调度器初始化完成")
    
    def add_task(self, name: str, func: Callable, schedule_time: str, 
                description: str = "", enabled: bool = True) -> bool:
        """添加定时任务"""
        try:
            if name in self.tasks:
                self.logger.warning(f"任务 {name} 已存在，将覆盖原任务")
            
            task = ScheduledTask(
                name=name,
                func=func,
                schedule_time=schedule_time,
                description=description,
                enabled=enabled
            )
            
            self.tasks[name] = task
            
            # 如果调度器正在运行，立即注册任务
            if self.running:
                self._register_task(task)
            
            self.logger.info(f"添加定时任务: {name} - {schedule_time} - {description}")
            return True
        
        except Exception as e:
            self.logger.error(f"添加任务失败: {e}")
            return False
    
    def remove_task(self, name: str) -> bool:
        """移除定时任务"""
        try:
            if name not in self.tasks:
                self.logger.warning(f"任务 {name} 不存在")
                return False
            
            # 从schedule中移除
            schedule.clear(name)
            
            # 从任务列表中移除
            del self.tasks[name]
            
            self.logger.info(f"移除定时任务: {name}")
            return True
        
        except Exception as e:
            self.logger.error(f"移除任务失败: {e}")
            return False
    
    def enable_task(self, name: str) -> bool:
        """启用任务"""
        try:
            if name not in self.tasks:
                self.logger.warning(f"任务 {name} 不存在")
                return False
            
            task = self.tasks[name]
            if not task.enabled:
                task.enabled = True
                if self.running:
                    self._register_task(task)
                self.logger.info(f"启用任务: {name}")
            
            return True
        
        except Exception as e:
            self.logger.error(f"启用任务失败: {e}")
            return False
    
    def disable_task(self, name: str) -> bool:
        """禁用任务"""
        try:
            if name not in self.tasks:
                self.logger.warning(f"任务 {name} 不存在")
                return False
            
            task = self.tasks[name]
            if task.enabled:
                task.enabled = False
                schedule.clear(name)
                self.logger.info(f"禁用任务: {name}")
            
            return True
        
        except Exception as e:
            self.logger.error(f"禁用任务失败: {e}")
            return False
    
    def start_scheduler(self) -> bool:
        """启动调度器"""
        try:
            if self.running:
                self.logger.warning("调度器已在运行中")
                return True
            
            # 注册所有启用的任务
            for task in self.tasks.values():
                if task.enabled:
                    self._register_task(task)
            
            # 启动调度线程
            self.running = True
            self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.scheduler_thread.start()
            
            self.logger.info("任务调度器启动成功")
            return True
        
        except Exception as e:
            self.logger.error(f"启动调度器失败: {e}")
            return False
    
    def stop_scheduler(self) -> bool:
        """停止调度器"""
        try:
            if not self.running:
                self.logger.warning("调度器未在运行")
                return True
            
            self.running = False
            
            # 清除所有调度任务
            schedule.clear()
            
            # 等待调度线程结束
            if self.scheduler_thread and self.scheduler_thread.is_alive():
                self.scheduler_thread.join(timeout=5)
            
            self.logger.info("任务调度器停止成功")
            return True
        
        except Exception as e:
            self.logger.error(f"停止调度器失败: {e}")
            return False
    
    def run_task_now(self, name: str) -> bool:
        """立即执行任务"""
        try:
            if name not in self.tasks:
                self.logger.warning(f"任务 {name} 不存在")
                return False
            
            task = self.tasks[name]
            self.logger.info(f"立即执行任务: {name}")
            
            return self._execute_task(task)
        
        except Exception as e:
            self.logger.error(f"立即执行任务失败: {e}")
            return False
    
    def get_task_status(self, name: str) -> Optional[ScheduledTask]:
        """获取任务状态"""
        return self.tasks.get(name)
    
    def get_all_tasks(self) -> Dict[str, ScheduledTask]:
        """获取所有任务"""
        return self.tasks.copy()
    
    def get_next_run_time(self, name: str) -> Optional[datetime]:
        """获取任务下次执行时间"""
        try:
            if name not in self.tasks:
                return None
            
            # 从schedule中获取下次执行时间
            for job in schedule.jobs:
                if hasattr(job, 'tags') and name in job.tags:
                    return job.next_run
            
            return None
        
        except Exception as e:
            self.logger.error(f"获取任务执行时间失败: {e}")
            return None
    
    def _register_task(self, task: ScheduledTask):
        """注册任务到schedule"""
        try:
            # 解析时间
            hour, minute = map(int, task.schedule_time.split(':'))
            
            # 创建包装函数
            def task_wrapper():
                return self._execute_task(task)
            
            # 注册到schedule
            schedule.every().day.at(task.schedule_time).do(task_wrapper).tag(task.name)
            
            self.logger.debug(f"注册任务到调度器: {task.name} - {task.schedule_time}")
        
        except Exception as e:
            self.logger.error(f"注册任务失败: {e}")
    
    def _execute_task(self, task: ScheduledTask) -> bool:
        """执行任务"""
        try:
            self.logger.info(f"开始执行任务: {task.name}")
            
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.last_run = datetime.now()
            task.error_msg = ""
            
            # 执行任务函数
            result = task.func()
            
            # 更新任务状态
            if result is not False:  # 允许返回None或True
                task.status = TaskStatus.COMPLETED
                self.logger.info(f"任务执行成功: {task.name}")
                return True
            else:
                task.status = TaskStatus.FAILED
                task.error_msg = "任务函数返回False"
                self.logger.error(f"任务执行失败: {task.name}")
                return False
        
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error_msg = str(e)
            self.logger.error(f"任务执行异常: {task.name} - {e}")
            return False
    
    def _run_scheduler(self):
        """运行调度器主循环"""
        self.logger.info("调度器主循环启动")
        
        while self.running:
            try:
                # 运行待执行的任务
                schedule.run_pending()
                
                # 短暂休眠
                time.sleep(1)
            
            except Exception as e:
                self.logger.error(f"调度器运行异常: {e}")
                time.sleep(5)  # 异常时稍长休眠
        
        self.logger.info("调度器主循环结束")
    
    def display_schedule_summary(self):
        """显示调度摘要"""
        if not self.tasks:
            self.logger.info("暂无调度任务")
            return
        
        self.logger.info("=" * 80)
        self.logger.info("任务调度摘要")
        self.logger.info("=" * 80)
        
        enabled_count = sum(1 for task in self.tasks.values() if task.enabled)
        disabled_count = len(self.tasks) - enabled_count
        
        self.logger.info(f"总任务数: {len(self.tasks)}")
        self.logger.info(f"启用: {enabled_count}, 禁用: {disabled_count}")
        self.logger.info(f"调度器状态: {'运行中' if self.running else '已停止'}")
        
        self.logger.info("\n任务详情:")
        self.logger.info(f"{'任务名称':20} | {'执行时间':8} | {'状态':8} | {'启用':6} | {'描述':30}")
        self.logger.info("-" * 80)
        
        # 按执行时间排序
        sorted_tasks = sorted(self.tasks.values(), key=lambda x: x.schedule_time)
        
        for task in sorted_tasks:
            enabled_str = "是" if task.enabled else "否"
            self.logger.info(f"{task.name:20} | {task.schedule_time:8} | {task.status.value:8} | "
                           f"{enabled_str:6} | {task.description:30}")
        
        # 显示下次执行时间
        if self.running:
            self.logger.info("\n下次执行时间:")
            for task in sorted_tasks:
                if task.enabled:
                    next_run = self.get_next_run_time(task.name)
                    if next_run:
                        self.logger.info(f"{task.name}: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
    
    def is_trading_time(self) -> bool:
        """检查是否为交易时间"""
        now = datetime.now().time()
        
        # 日盘时间: 09:00-15:00
        day_start = dt_time(9, 0)
        day_end = dt_time(15, 0)
        
        # 夜盘时间: 21:00-02:30
        night_start = dt_time(21, 0)
        night_end = dt_time(2, 30)
        
        # 检查是否在交易时间内
        if day_start <= now <= day_end:
            return True
        elif now >= night_start or now <= night_end:
            return True
        else:
            return False


if __name__ == "__main__":
    # 测试任务调度器
    scheduler = TaskScheduler()
    
    # 添加测试任务
    def test_task():
        print(f"测试任务执行: {datetime.now()}")
        return True
    
    scheduler.add_task("test_task", test_task, "10:00", "测试任务")
    
    # 显示调度摘要
    scheduler.display_schedule_summary()
    
    # 立即执行任务
    scheduler.run_task_now("test_task")
    
    print("任务调度器测试完成")
