#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货筛选与资金配置系统 - 统一配置文件
集中管理所有系统参数，包括筛选器参数、资金配置参数、黑名单等
"""

# ==================== 筛选器配置参数 ====================
SCANNER_CONFIG = {
    # 趋势过滤器参数 - 用于识别趋势强度和方向
    'trend_filters': {
        'adx_threshold': 20,           # 日线ADX强趋势阈值，用于判断是否有明确趋势(calculate_trend_indicators)
        'adx_min_threshold': 8,        # 30分钟ADX最小阈值，低于此值排除品种(_check_exclusion_conditions)
        'ema_periods': [12, 36, 72],   # 30分钟EMA周期组合，用于判断多空排列(calculate_trend_indicators)
        'di_period': 14,               # DI计算周期，用于判断趋势方向(calculate_trend_indicators)
    },

    # 波动扩张检测参数 - 用于识别波动率突然增加的品种
    'volatility_filters': {
        'atr_multiplier': 1.8,         # 15分钟ATR扩张倍数阈值，当前ATR>历史均值*此倍数时认为扩张(calculate_volatility_indicators)
        'atr_period': 14,              # ATR计算周期，用于计算真实波动幅度(calculate_volatility_indicators)
        'atr_lookback': 10,            # ATR均值回看周期，用于计算历史ATR均值作为基准(calculate_volatility_indicators)
        'bb_expansion_threshold': 25,   # 30分钟布林带扩张阈值(%)，布林带宽度增加超过此比例认为扩张(calculate_volatility_indicators)
        'bb_period': 20,               # 布林带计算周期，用于计算布林带上下轨(calculate_volatility_indicators)
        'bb_std_dev': 2,               # 布林带标准差倍数，决定布林带宽度(calculate_volatility_indicators)
        'bb_lookback': 10,             # 布林带宽度均值回看周期，用于计算历史宽度均值(calculate_volatility_indicators)
        'vix_threshold': 20,           # 5分钟波动率指数阈值，超过此值认为波动率较高(calculate_volatility_indicators)
        'vix_period': 20,              # 波动率计算周期，用于计算价格波动率(calculate_volatility_indicators)
    },
    
    # 突破信号确认参数 - 用于识别价格突破和技术信号
    'breakout_signals': {
        'donchian_period': 20,         # 5分钟Donchian通道周期，用于判断价格突破(calculate_breakout_signals)
        'macd_fast': 12,               # 15分钟MACD快线周期，用于计算MACD指标(calculate_breakout_signals)
        'macd_slow': 26,               # 15分钟MACD慢线周期，用于计算MACD指标(calculate_breakout_signals)
        'macd_signal': 9,              # 15分钟MACD信号线周期，用于判断金叉死叉(calculate_breakout_signals)
        'kdj_k_period': 9,             # 15分钟KDJ K值周期，用于计算KDJ指标(calculate_breakout_signals)
        'kdj_slow_k': 3,               # 15分钟KDJ 慢速K周期，用于平滑K值(calculate_breakout_signals)
        'kdj_slow_d': 3,               # 15分钟KDJ D值周期，用于计算D值(calculate_breakout_signals)
        'kdj_oversold': 20,            # KDJ超卖阈值，低于此值认为超卖(calculate_breakout_signals)
        'kdj_overbought': 80,          # KDJ超买阈值，高于此值认为超买(calculate_breakout_signals)
        'body_atr_period': 14,         # 实体强度ATR计算周期，用于评估K线实体强度(calculate_breakout_signals)
    },

    # 排除条件参数 - 用于过滤不符合交易条件的品种
    'exclusion_criteria': {
        'min_amplitude_multiplier': 0.8,  # 最小振幅倍数(相对15分钟ATR)，日振幅小于ATR*此倍数的品种被排除(_check_exclusion_conditions)
        'min_volume': 100,               # 最小成交量(手)，30分钟K线成交量小于此值的品种被排除(_check_exclusion_conditions)
        'min_volume_test': 50,            # 测试环境最小成交量，用于测试时降低成交量要求(_check_exclusion_conditions)
        'min_adx_for_trade': 8,           # 交易最小ADX要求，ADX小于此值的品种被排除(_check_exclusion_conditions)
    },
    
    # 评分权重配置 - 用于计算各项指标的综合评分
    'scoring_weights': {
        'atr_expansion_weight': 0.4,       # ATR扩张权重，在波动率评分中的占比(calculate_volatility_indicators)
        'bb_expansion_weight': 0.3,        # 布林带扩张权重，在波动率评分中的占比(calculate_volatility_indicators)
        'volume_change_weight': 0.3,       # 成交量变化权重，在波动率评分中的占比(calculate_volatility_indicators)
        'trend_score_weight': 0.3,         # 趋势评分权重，在综合评分中的占比(evaluate_symbol)
        'volatility_score_weight': 0.4,    # 波动率评分权重，在综合评分中的占比(evaluate_symbol)
        'breakout_score_weight': 0.3,      # 突破信号评分权重，在综合评分中的占比(evaluate_symbol)
        'donchian_weight': 40,             # Donchian突破权重，在突破信号评分中的占比(calculate_breakout_signals)
        'macd_weight': 30,                 # MACD信号权重，在突破信号评分中的占比(calculate_breakout_signals)
        'kdj_weight': 20,                  # KDJ信号权重，在突破信号评分中的占比(calculate_breakout_signals)
        'body_strength_weight': 0.1,       # 实体强度权重，在突破信号评分中的占比(calculate_breakout_signals)
        'adx_weight': 30,                  # ADX权重，在趋势评分中的占比(calculate_trend_indicators)
        'ema_alignment_weight': 40,        # EMA排列权重，在趋势评分中的占比(calculate_trend_indicators)
        'di_direction_weight': 30,         # DI方向权重，在趋势评分中的占比(calculate_trend_indicators)
    },

    # 评级标准 - 用于将综合评分转换为交易建议等级
    'rating_criteria': {
        'excellent_threshold': 60,         # 重点关注阈值，评分>=60分的品种标记为"重点关注"(evaluate_symbol)
        'good_threshold': 40,              # 准备入场阈值，评分>=40分的品种标记为"准备入场"(evaluate_symbol)
        'fair_threshold': 25,              # 观察阈值，评分>=25分的品种标记为"观察"(evaluate_symbol)
        'high_volatility': 40,             # 高波动率阈值，波动率评分>=40认为高波动(未直接使用)
        'strong_breakout': 50,             # 强突破信号阈值，突破评分>=50认为强突破(未直接使用)
        'strong_trend': 50,                # 强趋势阈值，趋势评分>=50认为强趋势(未直接使用)
    },
    
    # 数据获取配置 - 控制数据获取和缓存行为
    'data_config': {
        'cache_timeout': 300,              # 缓存超时时间(秒)，数据缓存有效期(fetch_data)
        'enable_cache': True,              # 是否启用缓存，提高重复查询效率(fetch_data)
        'periods': ['D', '30', '15', '5'],  # 支持的数据周期，用于数据重采样(fetch_data)
        'min_bars_required': {             # 各周期最小K线数量要求，少于此数量的品种被排除(fetch_data)
            'D': 100, '30': 72, '15': 80, '5': 144
        },
        'max_workers': 8,                  # 最大并发线程数，控制多线程处理品种数量(run_scan)
        'request_delay': 0.1,              # 请求间隔(秒)，避免频繁请求数据库(未直接使用)
    },

    # 输出配置 - 控制结果显示和保存格式
    'output_config': {
        'save_csv': True,                  # 是否保存CSV文件，控制结果文件输出(save_results)
        'csv_encoding': 'utf-8-sig',       # CSV文件编码，确保中文正确显示(save_results)
        'filename_template': '期货品种筛选结果_{timestamp}.csv',  # 文件名模板(save_results)
        'display_progress': True,          # 是否显示进度，控制实时进度输出(run_scan)
        'display_details': True,           # 是否显示详细信息，控制详细结果显示(display_results)
        'max_display_rows': 50,            # 最大显示行数，限制控制台输出行数(display_results)
        'result_columns': [
            '品种', '趋势方向', '最佳周期', '突破强度', 
            '波动率评分', '综合评分', '状态', '排除原因'
        ],
    },
    
    # 运行时配置
    'runtime_config': {
        'scan_interval_minutes': 30,       # 建议扫描间隔(分钟)
        'daily_scan_times': [              # 每日重点扫描时间
            '08:45', '10:30', '13:30', '14:30', '21:30'
        ],
        'max_symbols_per_scan': 100,       # 单次扫描最大品种数
        'error_retry_times': 3,            # 错误重试次数
        'timeout_seconds': 30,             # 单个品种超时时间
    }
}

# ==================== 资金配置参数 ====================
CAPITAL_CONFIG = {
    # 基础资金设置
    'total_capital': 100000,        # 总资金（元）
    'max_symbols': 8,               # 最大交易品种数
    
    # 风险控制参数
    'max_risk_per_symbol': 0.02,    # 单品种最大风险比例（2%）
    'max_margin_ratio': 0.99,        # 最大保证金使用比例（80%）
    'reserve_ratio': 0.01,           # 资金储备比例（20%）
    'max_total_risk': 0.15,         # 总体最大风险比例（15%）
    
    # 策略参数（基于fenzhouqiplus_strategy.py）
    'strategy_lots': 1,             # 策略默认手数
    'opening_multiplier': 2,        # 开仓倍数（策略开仓2倍手数）
    'fixed_profit_ratio': 0.5,     # 固定止盈比例（一半）
    'trailing_profit_ratio': 0.5,  # 浮动止盈比例（一半）
    
    # 风险估算参数
    'estimated_risk_ratio': 0.1,   # 预估风险比例（保证金的10%）
    'max_lots_per_symbol': 4,      # 单品种最大手数限制
    
    # 筛选条件 - 用于资金配置时过滤合格品种
    'min_score': 25,               # 最低综合评分，降低到25分以包含更多品种(screen_and_allocate)
    'preferred_status': ['重点关注', '准备入场', '观察'],  # 优选状态，包含观察级别品种(screen_and_allocate)
}

# ==================== 资金配置预设 ====================
# 不同资金规模的预设配置，用于CapitalAllocation类初始化
CAPITAL_PRESETS = {
    '5万元': {
        'total_capital': 50000,            # 总资金，用于计算仓位和风险(CapitalAllocation.__init__)
        'max_symbols': 3,                  # 最大品种数量，限制同时交易品种数(screen_and_allocate)
        'max_risk_per_symbol': 0.03,       # 单品种最大风险比例3%，用于仓位计算(calculate_position_size)
        'max_margin_ratio': 0.7,           # 最大保证金使用比例70%，防止过度杠杆(calculate_position_size)
        'max_lots_per_symbol': 2,          # 单品种最大手数限制，控制单品种仓位(calculate_position_size)
    },

    '8万元': {  # 激进模式配置
        'total_capital': 80000,            # 总资金8万元(CapitalAllocation.__init__)
        'max_symbols': 10,                 # 最大品种数量10个，允许更多品种分散风险(screen_and_allocate)
        'max_risk_per_symbol': 0.06,       # 单品种风险6%，激进模式允许更高风险(calculate_position_size)
        'risk_per_trade': 0.02,            # 单笔风险2%，用于Kelly公式计算(calculate_position_size)
        'max_portfolio_risk': 0.25,        # 总组合风险25%，控制整体风险敞口(screen_and_allocate)
        'reserve_ratio': 0.0,              # 无储备金，激进模式满仓操作(calculate_position_size)
        'strategy_lots': 1,                # 策略基础手数，用于计算建议手数(calculate_position_size)
        'opening_multiplier': 2,           # 开仓倍数2倍，实际开仓是建议手数的2倍(generate_allocation_report)
        'volatility_risk_factors': {       # 波动率风险系数，根据品种波动率调整仓位
            'low': 0.8,                    # 低波动品种系数0.8，可适当增加仓位(calculate_position_size)
            'medium': 1.2,                 # 中波动品种系数1.2，标准仓位(calculate_position_size)
            'high': 1.0                    # 高波动品种系数1.0，略微减少仓位(calculate_position_size)
        },
        'margin_warning_threshold': 0.4,   # 单品种保证金预警阈值40%，超过时提示风险(calculate_position_size)
        'total_margin_limit': 1.2,         # 总保证金上限120%，允许适度杠杆(adjust_positions_for_margin)
        'kelly_params': {                  # Kelly公式参数，用于科学仓位计算
            'win_rate': 0.45,              # 胜率45%，基于历史回测数据(calculate_position_size)
            'win_loss_ratio': 1.8          # 盈亏比1.8，平均盈利/平均亏损(calculate_position_size)
        },
        'aggressive_mode': True,           # 激进模式标志，启用激进交易策略(CapitalAllocation.__init__)
        'max_margin_ratio': 1.2,           # 最大保证金使用120%，允许杠杆交易(calculate_position_size)
        'max_lots_per_symbol': 4,          # 单品种最大手数4手，激进模式允许更大仓位(calculate_position_size)
    },
    
    '10万元': {  # 中等资金配置
        'total_capital': 100000,           # 总资金10万元(CapitalAllocation.__init__)
        'max_symbols': 5,                  # 最大品种数量5个，平衡风险和收益(screen_and_allocate)
        'max_risk_per_symbol': 0.02,       # 单品种最大风险比例2%，适中风险控制(calculate_position_size)
        'max_margin_ratio': 0.8,           # 最大保证金使用比例80%，保留安全边际(calculate_position_size)
        'max_lots_per_symbol': 3,          # 单品种最大手数3手，控制单品种仓位(calculate_position_size)
    },

    '20万元': {  # 较大资金配置
        'total_capital': 200000,           # 总资金20万元(CapitalAllocation.__init__)
        'max_symbols': 8,                  # 最大品种数量8个，增加分散化程度(screen_and_allocate)
        'max_risk_per_symbol': 0.02,       # 单品种最大风险比例2%，标准风险控制(calculate_position_size)
        'max_margin_ratio': 0.8,           # 最大保证金使用比例80%，稳健杠杆使用(calculate_position_size)
        'max_lots_per_symbol': 5,          # 单品种最大手数5手，允许更大仓位(calculate_position_size)
    },

    '50万元': {  # 大资金配置
        'total_capital': 500000,           # 总资金50万元(CapitalAllocation.__init__)
        'max_symbols': 12,                 # 最大品种数量12个，高度分散化投资(screen_and_allocate)
        'max_risk_per_symbol': 0.015,      # 单品种最大风险比例1.5%，严格风险控制(calculate_position_size)
        'max_margin_ratio': 0.85,          # 最大保证金使用比例85%，充分利用资金(calculate_position_size)
        'max_lots_per_symbol': 8,          # 单品种最大手数8手，充分利用资金规模(calculate_position_size)
    },

    '100万元': {  # 超大资金配置
        'total_capital': 1000000,          # 总资金100万元(CapitalAllocation.__init__)
        'max_symbols': 15,                 # 最大品种数量15个，最大化分散投资(screen_and_allocate)
        'max_risk_per_symbol': 0.01,       # 单品种最大风险比例1%，最严格风险控制(calculate_position_size)
        'max_margin_ratio': 0.85,          # 最大保证金使用比例85%，高效资金利用(calculate_position_size)
        'max_lots_per_symbol': 10,         # 单品种最大手数10手，大资金优势发挥(calculate_position_size)
    }
}

# ==================== 黑名单配置 ====================
BLACKLIST = {
    'symbols': ['JR', 'LR', 'RI', 'PM', 'WH', 'RS'],  # 基础品种代码
    'reasons': {  # 排除原因，用于日志输出和报告生成
        'JR': '粳稻流动性差',              # 在FuturesMinuteScanner初始化时显示排除原因
        'LR': '晚籼稻交易量小',            # 在FuturesMinuteScanner初始化时显示排除原因
        'RI': '早籼稻波动不足',            # 在FuturesMinuteScanner初始化时显示排除原因
        'PM': '普麦交易不活跃',            # 在FuturesMinuteScanner初始化时显示排除原因
        'WH': '强麦波动性低',              # 在FuturesMinuteScanner初始化时显示排除原因
        'RS': '菜籽无交易量',              # 在FuturesMinuteScanner初始化时显示排除原因
    }
}

# ==================== 品种风险等级配置 ====================
# 用于资金配置时根据品种特性调整仓位大小
SYMBOL_RISK_LEVELS = {
    # 低风险品种（农产品、金属）- 用于calculate_position_size中的风险调整
    'low_risk': {
        'symbols': ['C', 'CS', 'A', 'B', 'M', 'Y', 'P', 'CU', 'AL', 'ZN', 'PB', 'NI', 'SN'],  # 低风险品种列表
        'risk_multiplier': 0.8,                        # 风险系数，用于调整仓位大小(calculate_position_size)
        'max_position_ratio': 0.25,                    # 最大仓位比例，用于限制单品种仓位(calculate_position_size)
    },

    # 中等风险品种（化工、建材）- 用于calculate_position_size中的风险调整
    'medium_risk': {
        'symbols': ['RU', 'BU', 'FU', 'MA', 'PP', 'V', 'EG', 'TA', 'RB', 'HC', 'I', 'J', 'JM'],  # 中等风险品种列表
        'risk_multiplier': 1.0,                        # 风险系数，标准风险水平(calculate_position_size)
        'max_position_ratio': 0.2,                     # 最大仓位比例，适中仓位限制(calculate_position_size)
    },

    # 高风险品种（贵金属、能源、股指）- 用于calculate_position_size中的风险调整
    'high_risk': {
        'symbols': ['AU', 'AG', 'SC', 'LU', 'IF', 'IH', 'IC', 'IM', 'T', 'TF', 'TS'],  # 高风险品种列表
        'risk_multiplier': 1.5,                        # 风险系数，高风险需要更大风险调整(calculate_position_size)
        'max_position_ratio': 0.15,                    # 最大仓位比例，严格限制高风险品种仓位(calculate_position_size)
    }
}

# ==================== 品种分类配置 ====================
SYMBOL_CATEGORIES = {
    '金属': ['CU', 'AL', 'ZN', 'PB', 'NI', 'SN', 'AU', 'AG'],
    '黑色': ['RB', 'HC', 'I', 'J', 'JM'],
    '化工': ['RU', 'BU', 'SP', 'FU', 'L', 'V', 'PP', 'EG', 'EB', 'PG', 'TA', 'MA', 'UR', 'SA'],
    '农产品': ['C', 'CS', 'A', 'B', 'M', 'Y', 'P', 'CF', 'SR', 'OI', 'RM', 'AP', 'CJ'],
    '能源': ['SC', 'BC', 'LU', 'NR'],
    '股指': ['IF', 'IH', 'IC', 'IM'],
    '国债': ['T', 'TF', 'TS'],
}

# ==================== 交易时间配置 ====================
TRADING_SESSIONS = {
    '日盘': {
        'start': '09:00',
        'end': '15:00',
        'break_start': '11:30',
        'break_end': '13:30'
    },
    '夜盘': {
        'start': '21:00',
        'end': '02:30'  # 次日
    }
}

# ==================== 配置管理函数 ====================

def get_scanner_config(section: str = None):
    """获取筛选器配置参数

    Args:
        section: 配置节名称，如果为None则返回全部配置

    Returns:
        配置字典
    """
    if section is None:
        return SCANNER_CONFIG
    return SCANNER_CONFIG.get(section, {})

def get_capital_config(preset_name: str = None) -> dict:
    """获取资金配置参数

    Args:
        preset_name: 预设配置名称，如'10万元'

    Returns:
        配置参数字典
    """
    config = CAPITAL_CONFIG.copy()

    if preset_name and preset_name in CAPITAL_PRESETS:
        config.update(CAPITAL_PRESETS[preset_name])

    return config

def get_symbol_risk_level(symbol: str) -> dict:
    """获取品种风险等级"""
    product = ''.join([c for c in symbol if c.isalpha()]).upper()

    for level, config in SYMBOL_RISK_LEVELS.items():
        if product in config['symbols']:
            return {
                'level': level,
                'risk_multiplier': config['risk_multiplier'],
                'max_position_ratio': config['max_position_ratio']
            }

    # 默认中等风险
    return {
        'level': 'medium_risk',
        'risk_multiplier': 1.0,
        'max_position_ratio': 0.2
    }

def get_symbol_category(symbol: str) -> str:
    """获取品种分类

    Args:
        symbol: 品种代码

    Returns:
        品种分类名称
    """
    # 提取品种代码前缀
    product = symbol[:2].upper() if len(symbol) >= 2 else symbol[0].upper()

    for category, symbols in SYMBOL_CATEGORIES.items():
        if product in symbols:
            return category

    return '其他'

def is_blacklisted(full_symbol):
    """检查品种是否在黑名单中"""
    # 提取品种代码（去掉数字和交易所后缀）
    import re
    base_symbol = re.match(r'([A-Za-z]+)', full_symbol.upper())
    if base_symbol:
        base_symbol = base_symbol.group(1)
        return base_symbol in BLACKLIST['symbols']
    return False

def get_blacklist_reason(full_symbol):
    """获取黑名单原因"""
    import re
    base_symbol = re.match(r'([A-Za-z]+)', full_symbol.upper())
    if base_symbol:
        base_symbol = base_symbol.group(1)
        return BLACKLIST['reasons'].get(base_symbol, '流动性不足')
    return '未知原因'

def update_scanner_config(section: str, key: str, value):
    """更新筛选器配置参数

    Args:
        section: 配置节名称
        key: 配置键名
        value: 新值
    """
    if section in SCANNER_CONFIG:
        SCANNER_CONFIG[section][key] = value
    else:
        SCANNER_CONFIG[section] = {key: value}

def update_capital_config(key: str, value) -> None:
    """更新资金配置参数"""
    if key in CAPITAL_CONFIG:
        CAPITAL_CONFIG[key] = value
        print(f"配置已更新: {key} = {value}")
    else:
        print(f"未知配置项: {key}")

def print_all_config():
    """打印所有配置信息"""
    print("=" * 80)
    print("期货筛选与资金配置系统 - 统一配置")
    print("=" * 80)

    print("\n📊 筛选器配置:")
    for section, config in SCANNER_CONFIG.items():
        print(f"\n[{section}]")
        for key, value in config.items():
            if isinstance(value, dict):
                print(f"  {key}:")
                for k, v in value.items():
                    print(f"    {k}: {v}")
            else:
                print(f"  {key}: {value}")

    print("\n💰 资金配置:")
    print(f"  总资金: {CAPITAL_CONFIG['total_capital']:,}元")
    print(f"  最大品种数: {CAPITAL_CONFIG['max_symbols']}个")
    print(f"  单品种最大风险: {CAPITAL_CONFIG['max_risk_per_symbol']*100:.1f}%")
    print(f"  最大保证金使用: {CAPITAL_CONFIG['max_margin_ratio']*100:.1f}%")

    print("\n🎯 预设配置:")
    for name, preset in CAPITAL_PRESETS.items():
        aggressive = " (激进模式)" if preset.get('aggressive_mode', False) else ""
        print(f"  {name}: 资金{preset['total_capital']:,}元, 最多{preset['max_symbols']}个品种{aggressive}")

    print(f"\n⚠️ 黑名单品种: {', '.join(BLACKLIST['symbols'])}")

if __name__ == "__main__":
    print_all_config()
