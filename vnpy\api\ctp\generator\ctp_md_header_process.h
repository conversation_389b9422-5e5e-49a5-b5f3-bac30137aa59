void processFrontConnected(Task *task);

void processFrontDisconnected(Task *task);

void processHeartBeatWarning(Task *task);

void processRspUserLogin(Task *task);

void processRspUserLogout(Task *task);

void processRspError(Task *task);

void processRspSubMarketData(Task *task);

void processRspUnSubMarketData(Task *task);

void processRspSubForQuoteRsp(Task *task);

void processRspUnSubForQuoteRsp(Task *task);

void processRtnDepthMarketData(Task *task);

void processRtnForQuoteRsp(Task *task);

