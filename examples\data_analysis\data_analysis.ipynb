{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from vnpy.trader.constant import Exchange,Interval\n", "from data_analysis import DataAnalysis\n", "from datetime import datetime\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["herramiento = DataAnalysis()\n", "herramiento.load_history(    \n", "    symbol=\"XBTUSD\", \n", "    exchange=Exchange.BITMEX, \n", "    interval=Interval.MINUTE, \n", "    start=datetime(2019, 9, 1), \n", "    end=datetime(2019, 10, 30),\n", "    rate = 8/10000,\n", "    index_3to1 = [\"ATR\",\"CCI\"],\n", "    index_1to1 = [\"STDDEV\",\"SMA\"],\n", "    index_2to2 = [\"AROON\"],\n", "    index_2to1 = [\"AROONOSC\"],\n", "    index_4to1 = [\"BOP\"],\n", "    window_index=30,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["data = herramiento.base_analysis()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["herramiento.show_chart(data[:1500], boll_wide=2.8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["# 多时间周期分析\n", "intervals = [\"5min\",\"15min\",\"30min\",\"1h\",\"2h\",\"4h\"]\n", "herramiento.multi_time_frame_analysis(intervals=intervals)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3.0}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.1"}, "toc": {"base_numbering": 1.0, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16.0, "lenType": 16.0, "lenVar": 40.0}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 0}