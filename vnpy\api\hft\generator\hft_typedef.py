LEN_ID = "int"
LEN_SYS_ID = "int"
LEN_SYMBOL = "int"
LEN_SYMBOL_NAME = "int"
LEN_ERR_MSG = "int"
LEN_IP_ADDR = "int"
LEN_CUST_ID = "int"
LEN_PASSWORD = "int"
LEN_ACCOUNT_ID = "int"
LEN_CIF_TYPE = "int"
LEN_TERM_INFO = "int"
LEN_SECU_ID = "int"
LEN_STK_CODE = "int"
LEN_MONY_TYPE = "int"
LEN_ORG_ID = "int"
LEN_SEAT = "int"
LEN_TA_CODE = "int"
LEN_REMARK = "int"
LEN_REMARK1 = "int"
LEN_MARKET = "string"
LogLevel_Debug = "int"
LogLevel_Info = "int"
LogLevel_Warn = "int"
LogLevel_Error = "int"
LogLevel_Critical = "int"
SUCCESS = "int"
ERR_HFT_TRADER_INTERNAL = "int"
ERR_HFT_TRADER_INVALID_PARAM = "int"
ERR_HFT_TRADER_NETWORK_TIMEOUT = "int"
ERR_HFT_TRADER_NETWORK_SEND_FAIL = "int"
ERR_HFT_TRADER_INVALID_PKG = "int"
ERR_HFT_TRADER_PACK_FAIL = "int"
ERR_HFT_TRADER_UNPACK_FAIL = "int"
ERR_HFT_TRADER_NO_MEM = "int"
ERR_HFT_TRADER_INIT_ERROR = "int"
ERR_HFT_TRADER_NOT_CONNECT = "int"
ERR_HFT_TRADER_LOGIN_FAIL = "int"
ERR_HFT_TRADER_NOT_INIT = "int"
ERR_HFT_TRADER_GW_ROUTE_FAIL = "int"
ERR_HFT_TRADER_GW_LOGIC_FAIL = "int"
ERR_HFT_TRADER_GW_KEYFIELD_FAIL = "int"
ERR_HFT_TRADER_GW_DECODE_FAIL = "int"
ERR_HFT_TRADER_GW_NO_USER = "int"
ERR_HFT_TRADER_GW_NO_COUNTERID = "int"
ERR_HFT_TRADER_GW_ERROR_IP = "int"
ERR_HFT_TRADER_GW_ERROR_MAC = "int"
ERR_HFT_TRADER_NO_DATA = "int"
ERR_HFT_TRADER_NO_CERT_FILE = "int"
ERR_HFT_TRADER_GW_UNKOWN_MSG = "int"
ERR_HFT_TRADER_GW_OVERRUN = "int"
ERR_HFT_TRADER_GW_FUNDID_DISABLE = "int"
ERR_HFT_TRADER_GW_INVALID_CID = "int"
ERR_HFT_TRADER_GW_USED_CID = "int"
ERR_HFT_TRADER_EXCEED_MAX_NUM = "int"
ERR_HFT_TRADER_SEQNO_NOT_UNIQUE = "int"
ERR_HFT_TRADER_GW_REQUIRE_MIN_API_VERSION = "int"
ERR_HFT_TRADER_UNSUPPORTED_FUNC = "int"
ERR_HFT_TRADER_BUFFER_OVERFLOW = "int"
ERR_HFT_TRADER_NOT_FOUND_SECUID = "int"
ERR_HFT_TRADER_CALL_MICRO_SERVICE = "int"
ERR_HFT_TRADER_MICRO_SERVICE_BIZERROR = "int"
Market_SSE = "int"
Market_SZSE = "int"
Market_SSE_OP = "int"
Market_SZSE_OP = "int"
OrderStatus_PendingNew = "int"
OrderStatus_New = "int"
OrderStatus_PartiallyFilled = "int"
OrderStatus_Filled = "int"
OrderStatus_PendingCancel = "int"
OrderStatus_Canceling = "int"
OrderStatus_CancelFilled = "int"
OrderStatus_PartiallyCanceled = "int"
OrderStatus_Rejected = "int"
OrderStatus_CancelRejected = "int"
OrderType_LMT = "int"
OrderType_BOC = "int"
OrderType_BOP = "int"
OrderType_B5TC = "int"
OrderType_B5TL = "int"
OrderType_IOC = "int"
OrderType_AON = "int"
OrderType_ALMT = "int"
OrderType_ELMT = "int"
OrderType_OLMT = "int"
OrderSide_Bid = "int"
OrderSide_Ask = "int"
OrderSide_Buy_Open = "int"
OrderSide_Buy_Close = "int"
OrderSide_Sell_Open = "int"
OrderSide_Sell_Close = "int"
OrderSide_Margin_Bid = "int"
OrderSide_Margin_Ask = "int"
OrderSide_Covered_Open = "int"
OrderSide_Covered_Close = "int"
OrderSide_ETF_Create = "int"
OrderSide_ETF_Redeem = "int"
OrderSide_Reverse_Repurchase = "int"
OrderSide_Margin_EnBuyBack = "int"
OrderSide_Margin_EnSellBack = "int"
OrderSide_Margin_StockBack = "int"
OrderSide_Margin_PayBack = "int"
OrderSide_Margin_MortgageIn = "int"
OrderSide_Margin_MortgageOut = "int"
OrderSide_Repurchase = "int"
OrderSide_IPO_Bid = "int"
OrderSide_AHFPT_Bid = "int"
OrderSide_AHFPT_Ask = "int"
OrderSide_Margin_MoreStockTrans = "int"
OrderSide_Allotment_Shares = "int"
OrderSide_ETF_Create_OTC = "int"
OrderSide_ETF_Redeem_OTC = "int"
OrderSide_Bond_Swap = "int"
OrderSide_Bond_Sell_Back = "int"
OrderSide_Pledge_In = "int"
OrderSide_Pledge_Out = "int"
PositionSide_Long = "int"
PositionSide_Short = "int"
PositionSide_Short_Covered = "int"
TradeReportType_Normal = "int"
TradeReportType_Cancel = "int"
TradeReportType_Abolish = "int"
TradeReportType_InsideCancel = "int"
TradeReportType_CancelAbolish = "int"
AccountType_Stock = "int"
AccountType_Futures = "int"
AccountType_Options = "int"
AccountType_Margin = "int"
CurrencyType_CNY = "int"
CurrencyType_HKD = "int"
CurrencyType_USD = "int"
CurrencyType_EUR = "int"
CurrencyType_JPY = "int"
CurrencyType_KRW = "int"
CancelFlag_False = "int"
CancelFlag_True = "int"
ETFTradeState_PurchaseAndRedeem = "int"
ETFTradeState_Forbidden = "int"
ETFTradeState_OnlyPurchase = "int"
ETFTradeState_OnlyRedeem = "int"
ETFRepalceFlag_Forbidden = "int"
ETFRepalceFlag_Allow = "int"
ETFRepalceFlag_Must = "int"
ETFRepalceFlag_NotSHRefund = "int"
ETFRepalceFlag_NotSHMust = "int"
ETFRepalceFlag_NotSHSZRefund = "int"
ETFRepalceFlag_NotSHSZMust = "int"
StkType_Stock = "int"
StkType_Bond = "int"
StkType_Index = "int"
StkType_Future = "int"
StkType_Options = "int"
StkType_Fund = "int"
StkType_Repurchase = "int"
StkType_Warrant = "int"
StkType_STIB = "int"
SecurityTypeDetail_Default = "int"
SecurityTypeDetail_ZBAG = "int"
SecurityTypeDetail_ZXBG = "int"
SecurityTypeDetail_CYBG = "int"
SecurityTypeDetail_ZBBG = "int"
SecurityTypeDetail_GZ = "int"
SecurityTypeDetail_QYZ = "int"
SecurityTypeDetail_GSZ = "int"
SecurityTypeDetail_KZZ = "int"
SecurityTypeDetail_SMZ = "int"
SecurityTypeDetail_KJHSMZ = "int"
SecurityTypeDetail_ZQGSCJZ = "int"
SecurityTypeDetail_ZYSHG = "int"
SecurityTypeDetail_ZCZCZQ = "int"
SecurityTypeDetail_GPETF = "int"
SecurityTypeDetail_KSCGPETF = "int"
SecurityTypeDetail_KJETF = "int"
SecurityTypeDetail_SWZQETF = "int"
SecurityTypeDetail_XJZQETF = "int"
SecurityTypeDetail_HJETF = "int"
SecurityTypeDetail_HBETF = "int"
SecurityTypeDetail_GGETF = "int"
SecurityTypeDetail_SPQHETF = "int"
SecurityTypeDetail_BZLOF = "int"
SecurityTypeDetail_FJZJJ = "int"
SecurityTypeDetail_FBSJJ = "int"
SecurityTypeDetail_JSSJJ = "int"
SecurityTypeDetail_PSQZ = "int"
SecurityTypeDetail_QZ = "int"
SecurityTypeDetail_GGQQ = "int"
SecurityTypeDetail_ETFQQ = "int"
SecurityTypeDetail_ZGCP = "int"
SecurityTypeDetail_BJHG = "int"
SecurityTypeDetail_YXG = "int"
SecurityTypeDetail_ZQGSDQZ = "int"
SecurityTypeDetail_KJHGSZ = "int"
SecurityTypeDetail_ZQFX = "int"
SecurityTypeDetail_GZFX = "int"
SecurityTypeDetail_QTZQ = "int"
SecurityTypeDetail_KSC = "int"
SecurityTypeDetail_QTJJ = "int"
SecurityTypeDetail_GJBGP = "int"
SecurityTypeDetail_QTGP = "int"
SecurityTypeDetail_MDSZQHG = "int"
SecurityTypeDetail_CYBKZZ = "int"
SecurityTypeDetail_CTPZ = "int"
SecurityTypeDetail_HLTCDR = "int"
SecurityTypeDetail_CXQYGP = "int"
SecurityTypeDetail_ZYDM = "int"
SecurityTypeDetail_SSSSHBJJ = "int"
SecurityTypeDetail_XGXZFX = "int"
SecurityTypeDetail_KCB = "int"
SecurityTypeDetail_KCBCDR = "int"
SecurityStatus_Normal = "int"
SecurityStatus_Suspend = "int"
SecurityStatus_NearExpireDate = "int"
SecurityStatus_Adjustment = "int"
SecurityStatus_ExcludeRight = "int"
SecurityStatus_ExcludeDividend = "int"
SecurityStatus_Caution = "int"
SecurityStatus_IPOFirstDay = "int"
SecurityStatus_Delisting = "int"
SecurityStatus_TransferByAgreement = "int"
CounterType_JZJY = "int"
CounterType_APEX_MARGIN = "int"
CounterType_APEX = "int"
CounterType_DYC = "int"
CounterType_DYC_MARGIN = "int"
CounterType_DYC_JZJY = "int"
CounterType_DYC_JZJY_MARGIN = "int"
SecuidRight_Delisting = "int"
SecuidRight_Caution = "int"
SecuidRight_Gem = "int"
SecuidRight_KeChuangPlate_Normal = "int"
SecuidRight_KeChuangPlate_Credit = "int"
SecuidRight_Repo = "int"
SecuidRight_Reverse_Repo = "int"
SecuidRight_SHHK_Trade = "int"
SecuidRight_SZHK_Trade = "int"
SecuidRight_ETF_Redemption_Normal = "int"
SecuidRight_Hulun_Trade = "int"
TransferFundSide_In = "int"
TransferFundSide_Out = "int"
OrderFlag_Security_Normal = "int"
OrderFlag_Security_CashLoan = "int"
OrderFlag_Security_SecurityLoan = "int"
OrderFlag_Future_Option_Speculation = "int"
OrderFlag_Future_Option_Hedge = "int"
OrderFlag_Future_Option_Arbitrage = "int"
OrderFlag_Option_Covered = "int"
OrderQueryFlag_All = "int"
OrderQueryFlag_Cancelable = "int"
OrderQueryFlag_NonCancel = "int"
IPOQueryFlag_NewStock = "int"
IPOQueryFlag_NewBond = "int"
ExerciseType_Execute = "int"
ExerciseOperateStatus_InsertSubmitted = "int"
ExerciseOperateStatus_CancelSubmitted = "int"
ExerciseOperateStatus_Accepted = "int"
ExerciseOperateStatus_InsertRejected = "int"
ExerciseOperateStatus_CancelRejected = "int"
ExerciseOperateStatus_UnSubmitted = "int"
ExerciseStatus_SendStock = "int"
ExerciseStatus_RepealStock = "int"
ExerciseStatus_SendOffer = "int"
ExerciseStatus_Fail = "int"
ExerciseStatus_Accepted = "int"
ExerciseStatus_Cancelled = "int"
StrikeModeType_Continental = "int"
StrikeModeType_American = "int"
StrikeModeType_Bermuda = "int"
OptionsType_CallOptions = "int"
OptionsType_PutOptions = "int"
OrderUnitType_Shou = "int"
OrderUnitType_Gu = "int"
OrderUnitType_Fen = "int"
OrderUnitType_Zhang = "int"
TransferStatus_TransferHandling = "int"
TransferStatus_TransferSuccess = "int"
TransferStatus_TransferFail = "int"
TransferPositionType_All = "int"
TransferPositionType_History = "int"
TransferPositionType_TodayBS = "int"
TransferPositionType_TodayPR = "int"
TransferStockPositionSide_In = "int"
TransferStockPositionSide_Out = "int"
TransferStockPositionSide_MoveIn = "int"
TransferStockPositionSide_MoveOut = "int"
TransferStockPositionSide_Lock = "int"
TransferStockPositionSide_Unlock = "int"
TransferStockPositionSide_ExerciseFreeze = "int"
TransferStockPositionSide_ExerciseUnfreeze = "int"
TransferStockPositionSide_CoveredCloseUnfreeze = "int"
LockStockType_Lock = "int"
LockStockType_Unlock = "int"
LockStockStatus_SendStock = "int"
LockStockStatus_RepealStock = "int"
LockStockStatus_SendOffer = "int"
LockStockStatus_Fail = "int"
LockStockStatus_Success = "int"
LockStockStatus_ForceCancel = "int"
ContractStatus_Suspend = "int"
ContractStatus_LongTermSuspend = "int"
ContractStatus_CircuitBreaker = "int"
ContractStatus_RestrictLongOpen = "int"
ContractStatus_RestrictShortOpen = "int"
ContractStatus_RestrictCoveredOpen = "int"
ContractStatus_VolatilityInterrupt = "int"
ContractStatus_TempSuspend = "int"
ContractStatus_ContinuousSuspend = "int"
ContractStatus_RestrictExercise = "int"
ContractStatus_RestrictMakerQuote = "int"
OptionCombineType_Combine = "int"
OptionCombineType_Split = "int"
OptionCombineType_ForceSplit = "int"
OptionCombineStrategy_CNSJC = "int"
OptionCombineStrategy_CXSJC = "int"
OptionCombineStrategy_PXSJC = "int"
OptionCombineStrategy_PNSJC = "int"
OptionCombineStrategy_KS = "int"
OptionCombineStrategy_KKS = "int"
OptionCombineStrategy_ZBD = "int"
ApprovalStatus_NoCheck = "int"
ApprovalStatus_CheckSucc = "int"
ApprovalStatus_CheckFail = "int"
ApprovalStatus_Expired = "int"
ApprovalStatus_Cancelled = "int"
ApprovalStatus_Extended = "int"
MARKET_SH = "string"
MARKET_SZ = "string"
MARKET_CFFEX = "string"
MARKET_CZCE = "string"
MARKET_DCE = "string"
MARKET_SHFE = "string"
MARKET_HK = "string"
MARKET_SGE = "string"
MARKET_CFETS = "string"
MARKET_SHOP = "string"
MARKET_SZOP = "string"
MARKET_SZHK = "string"
MARKET_SHHK = "string"
