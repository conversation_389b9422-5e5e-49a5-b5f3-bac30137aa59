#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
删除VNPY中所有合约2025年4月21日至今的数据
作者：AI助手
日期：2025年01月20日
"""

import os
import shutil
from datetime import datetime, timedelta
from typing import List, Dict, Set
from collections import defaultdict

from vnpy.trader.constant import Exchange, Interval
from vnpy.trader.database import database_manager, convert_tz
from vnpy.trader.object import BarData


def backup_database(backup_path=None):
    """
    备份VNPY数据库
    
    Args:
        backup_path: 备份文件夹路径，如果不指定则使用默认路径
    """
    print("=" * 60)
    print("开始备份数据库...")
    
    # 获取数据库类型
    db_instance = database_manager
    db_type = type(db_instance).__name__
    
    if db_type == 'SqliteDatabase':
        # SQLite数据库备份
        from vnpy.trader.setting import SETTINGS
        
        # 获取SQLite数据库文件路径
        database_file = SETTINGS.get("database.database", "database.db")
        
        # 如果是相对路径，需要找到完整路径
        if not os.path.isabs(database_file):
            # 通常在用户目录下的.vntrader文件夹
            user_home = os.path.expanduser("~")
            vntrader_dir = os.path.join(user_home, ".vntrader")
            database_path = os.path.join(vntrader_dir, database_file)
        else:
            database_path = database_file
        
        if not os.path.exists(database_path):
            print(f"警告：数据库文件 {database_path} 不存在，跳过备份")
            return
        
        # 生成备份路径
        if backup_path is None:
            backup_dir = os.path.join(os.path.dirname(database_path), "backup")
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
                print(f"创建备份文件夹: {backup_dir}")
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"database_backup_{timestamp}.db"
            backup_path = os.path.join(backup_dir, backup_filename)
        
        try:
            print(f"源文件: {database_path}")
            print(f"备份文件: {backup_path}")
            
            # 复制数据库文件
            shutil.copy2(database_path, backup_path)
            print(f"✅ SQLite数据库备份完成: {backup_path}")
            
        except Exception as e:
            print(f"❌ SQLite数据库备份失败: {str(e)}")
            print("继续执行删除操作...")
    
    else:
        print(f"⚠️  当前数据库类型 {db_type} 不支持自动备份")
        print("建议手动备份数据库后再继续操作")
        response = input("是否继续执行删除操作？(y/N): ")
        if response.lower() != 'y':
            print("操作已取消")
            return False
    
    print("备份完成！")
    print("=" * 60)
    return True


def get_all_contracts_with_data():
    """
    获取数据库中有数据的所有合约
    
    Returns:
        List: 包含(symbol, exchange, interval)元组的列表
    """
    print("正在获取数据库中的所有合约...")
    
    try:
        bar_overview = database_manager.get_bar_overview()
        contracts = []
        
        for overview in bar_overview:
            if overview.count > 0:  # 只处理有数据的合约
                contracts.append((overview.symbol, overview.exchange, overview.interval))
        
        print(f"找到 {len(contracts)} 个有数据的合约")
        return contracts
        
    except Exception as e:
        print(f"❌ 获取合约列表失败: {str(e)}")
        return []


def delete_bar_data_by_time_range(symbol, exchange, interval, start_time, end_time):
    """
    删除指定时间范围内的K线数据
    
    Args:
        symbol: 合约代码
        exchange: 交易所
        interval: 时间周期
        start_time: 开始时间
        end_time: 结束时间
    
    Returns:
        删除的数据条数
    """
    try:
        # 先查询要删除的数据
        bars_to_delete = database_manager.load_bar_data(
            symbol=symbol,
            exchange=exchange,
            interval=interval,
            start=start_time,
            end=end_time
        )
        
        if not bars_to_delete:
            return 0
        
        # 获取底层数据库实例
        db_instance = database_manager
        db_type = type(db_instance).__name__
        
        # 根据数据库类型执行不同的删除操作
        if db_type in ['SqliteDatabase', 'MysqlDatabase', 'PostgresqlDatabase']:
            # 对于关系型数据库，使用Peewee ORM
            try:
                # 动态导入对应的DbBarData模型
                if db_type == 'SqliteDatabase':
                    from vnpy.database.sqlite.sqlite_database import DbBarData
                elif db_type == 'MysqlDatabase':
                    from vnpy.database.mysql.mysql_database import DbBarData
                elif db_type == 'PostgresqlDatabase':
                    from vnpy.database.postgresql.postgresql_database import DbBarData
                
                # 转换时间格式
                start_converted = convert_tz(start_time)
                end_converted = convert_tz(end_time)
                
                # 执行删除操作
                with db_instance.db.atomic():
                    deleted_count = DbBarData.delete().where(
                        (DbBarData.symbol == symbol) &
                        (DbBarData.exchange == exchange.value) &
                        (DbBarData.interval == interval.value) &
                        (DbBarData.datetime >= start_converted) &
                        (DbBarData.datetime <= end_converted)
                    ).execute()
                
                return deleted_count
                
            except Exception as e:
                print(f"  警告：关系型数据库删除失败: {str(e)}")
                return 0
                
        elif db_type == 'MongodbDatabase':
            # 对于MongoDB，使用MongoEngine
            try:
                from vnpy.database.mongodb.mongodb_database import DbBarData
                
                # 转换时间格式
                start_converted = convert_tz(start_time)
                end_converted = convert_tz(end_time)
                
                # 执行删除操作
                deleted_count = DbBarData.objects(
                    symbol=symbol,
                    exchange=exchange.value,
                    interval=interval.value,
                    datetime__gte=start_converted,
                    datetime__lte=end_converted
                ).delete()
                
                return deleted_count
                
            except Exception as e:
                print(f"  警告：MongoDB删除失败: {str(e)}")
                return 0
                
        else:
            # 对于其他数据库类型（如InfluxDB），提示不支持
            print(f"  警告：不支持的数据库类型 {db_type}")
            return 0
            
    except Exception as e:
        print(f"  警告：删除时间范围数据时出错: {str(e)}")
        return 0


def delete_tick_data_by_time_range(symbol, exchange, start_time, end_time):
    """
    删除指定时间范围内的Tick数据
    
    Args:
        symbol: 合约代码
        exchange: 交易所
        start_time: 开始时间
        end_time: 结束时间
    
    Returns:
        删除的数据条数
    """
    try:
        # 先查询要删除的数据
        ticks_to_delete = database_manager.load_tick_data(
            symbol=symbol,
            exchange=exchange,
            start=start_time,
            end=end_time
        )
        
        if not ticks_to_delete:
            return 0
        
        # 获取底层数据库实例
        db_instance = database_manager
        db_type = type(db_instance).__name__
        
        # 根据数据库类型执行不同的删除操作
        if db_type in ['SqliteDatabase', 'MysqlDatabase', 'PostgresqlDatabase']:
            # 对于关系型数据库，使用Peewee ORM
            try:
                # 动态导入对应的DbTickData模型
                if db_type == 'SqliteDatabase':
                    from vnpy.database.sqlite.sqlite_database import DbTickData
                elif db_type == 'MysqlDatabase':
                    from vnpy.database.mysql.mysql_database import DbTickData
                elif db_type == 'PostgresqlDatabase':
                    from vnpy.database.postgresql.postgresql_database import DbTickData
                
                # 转换时间格式
                start_converted = convert_tz(start_time)
                end_converted = convert_tz(end_time)
                
                # 执行删除操作
                with db_instance.db.atomic():
                    deleted_count = DbTickData.delete().where(
                        (DbTickData.symbol == symbol) &
                        (DbTickData.exchange == exchange.value) &
                        (DbTickData.datetime >= start_converted) &
                        (DbTickData.datetime <= end_converted)
                    ).execute()
                
                return deleted_count
                
            except Exception as e:
                print(f"  警告：关系型数据库Tick删除失败: {str(e)}")
                return 0
                
        elif db_type == 'MongodbDatabase':
            # 对于MongoDB，使用MongoEngine
            try:
                from vnpy.database.mongodb.mongodb_database import DbTickData
                
                # 转换时间格式
                start_converted = convert_tz(start_time)
                end_converted = convert_tz(end_time)
                
                # 执行删除操作
                deleted_count = DbTickData.objects(
                    symbol=symbol,
                    exchange=exchange.value,
                    datetime__gte=start_converted,
                    datetime__lte=end_converted
                ).delete()
                
                return deleted_count
                
            except Exception as e:
                print(f"  警告：MongoDB Tick删除失败: {str(e)}")
                return 0
                
        else:
            # 对于其他数据库类型（如InfluxDB），提示不支持
            print(f"  警告：不支持的数据库类型 {db_type}")
            return 0
            
    except Exception as e:
        print(f"  警告：删除Tick时间范围数据时出错: {str(e)}")
        return 0


def main():
    """
    主函数：删除所有合约2025年4月21日至今的数据
    """
    print("=" * 80)
    print("VNPY数据删除工具")
    print("功能：删除所有合约2025年4月21日至今的数据")
    print("=" * 80)
    
    # 设置删除时间范围
    start_time = datetime(2025, 4, 21, 0, 0, 0)  # 2025年4月21日
    end_time = datetime.now()  # 当前时间
    
    print(f"删除时间范围：{start_time.strftime('%Y-%m-%d %H:%M:%S')} 至 {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 确认操作
    print("⚠️  警告：此操作将删除指定时间范围内的所有合约数据，该操作不可逆！")
    print(f"📅 删除范围：{start_time.date()} 至 {end_time.date()}")
    
    response = input("请确认是否继续？(输入 'YES' 继续，其他任意键取消): ")
    if response != 'YES':
        print("操作已取消")
        return
    
    # 备份数据库
    if not backup_database():
        return
    
    # 获取所有合约
    contracts = get_all_contracts_with_data()
    if not contracts:
        print("未找到任何合约数据")
        return
    
    # 统计变量
    total_bar_deleted = 0
    total_tick_deleted = 0
    processed_contracts = 0
    failed_contracts = 0
    
    # 按合约分组统计
    contract_stats = defaultdict(int)
    
    print("\n开始删除数据...")
    print("-" * 80)
    
    # 遍历所有合约
    for i, (symbol, exchange, interval) in enumerate(contracts, 1):
        try:
            print(f"[{i}/{len(contracts)}] 处理合约: {symbol}.{exchange.value} ({interval.value})")
            
            # 删除Bar数据
            bar_deleted = delete_bar_data_by_time_range(
                symbol, exchange, interval, start_time, end_time
            )
            total_bar_deleted += bar_deleted
            contract_stats[f"{symbol}.{exchange.value}"] += bar_deleted
            
            if bar_deleted > 0:
                print(f"  📊 删除Bar数据: {bar_deleted} 条")
            
            # 对于1分钟数据，同时删除对应的Tick数据
            if interval == Interval.MINUTE:
                tick_deleted = delete_tick_data_by_time_range(
                    symbol, exchange, start_time, end_time
                )
                total_tick_deleted += tick_deleted
                
                if tick_deleted > 0:
                    print(f"  📈 删除Tick数据: {tick_deleted} 条")
            
            if bar_deleted > 0 or tick_deleted > 0:
                print(f"  ✅ 合约 {symbol}.{exchange.value} 处理完成")
            else:
                print(f"  ➖ 合约 {symbol}.{exchange.value} 无需删除")
            
            processed_contracts += 1
            
        except Exception as e:
            print(f"  ❌ 合约 {symbol}.{exchange.value} 处理失败: {str(e)}")
            failed_contracts += 1
            continue
    
    # 输出删除统计
    print("\n" + "=" * 80)
    print("删除操作完成统计")
    print("=" * 80)
    print(f"📊 总计删除Bar数据：{total_bar_deleted:,} 条")
    print(f"📈 总计删除Tick数据：{total_tick_deleted:,} 条")
    print(f"✅ 成功处理合约：{processed_contracts} 个")
    print(f"❌ 失败处理合约：{failed_contracts} 个")
    print(f"🗓️  删除时间范围：{start_time.date()} 至 {end_time.date()}")
    
    # 显示各合约删除详情
    if contract_stats:
        print("\n各合约删除详情:")
        print("-" * 50)
        for contract, count in sorted(contract_stats.items()):
            if count > 0:
                print(f"  {contract}: {count:,} 条")
    
    print("\n✅ 数据删除操作完成！")
    print("📁 数据库备份文件已保存，如需恢复数据请使用备份文件")


if __name__ == "__main__":
    main() 