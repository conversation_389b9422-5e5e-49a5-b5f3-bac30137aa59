"""
账户管理模块
负责账户登录、状态监控、断线重连等功能
"""

import time
import threading
from typing import Dict, List, Optional, Callable
from datetime import datetime, timedelta
from enum import Enum

from vnpy.event import EventEngine, Event
from vnpy.trader.engine import MainEngine
from vnpy.trader.gateway import BaseGateway
from vnpy.trader.constant import Status
from vnpy.trader.object import AccountData, ContractData
from vnpy.trader.event import EVENT_ACCOUNT, EVENT_CONTRACT, EVENT_LOG

from ..config import get_system_config, get_trading_config
from ..utils.logger import get_logger


class AccountStatus(Enum):
    """账户状态枚举"""
    DISCONNECTED = "未连接"
    CONNECTING = "连接中"
    CONNECTED = "已连接"
    ERROR = "连接错误"
    RECONNECTING = "重连中"


class AccountManager:
    """账户管理器"""
    
    def __init__(self, main_engine: MainEngine, event_engine: EventEngine):
        """初始化账户管理器"""
        self.main_engine = main_engine
        self.event_engine = event_engine
        self.logger = get_logger("AccountManager")
        
        # 配置
        self.system_config = get_system_config()
        self.trading_config = get_trading_config()
        
        # 账户状态管理
        self.account_status: Dict[str, AccountStatus] = {}
        self.account_data: Dict[str, AccountData] = {}
        self.contract_data: Dict[str, ContractData] = {}
        
        # 重连管理
        self.reconnect_threads: Dict[str, threading.Thread] = {}
        self.reconnect_counts: Dict[str, int] = {}
        self.last_connect_time: Dict[str, datetime] = {}
        
        # 回调函数
        self.status_callbacks: List[Callable] = []
        self.account_callbacks: List[Callable] = []
        
        # 注册事件监听
        self._register_events()
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_accounts, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info("账户管理器初始化完成")
    
    def _register_events(self):
        """注册事件监听"""
        self.event_engine.register(EVENT_ACCOUNT, self._on_account_event)
        self.event_engine.register(EVENT_CONTRACT, self._on_contract_event)
        self.event_engine.register(EVENT_LOG, self._on_log_event)
    
    def _on_account_event(self, event: Event):
        """处理账户事件"""
        account_data: AccountData = event.data
        self.account_data[account_data.gateway_name] = account_data
        
        # 通知回调函数
        for callback in self.account_callbacks:
            try:
                callback(account_data)
            except Exception as e:
                self.logger.error(f"账户事件回调执行失败: {e}")
    
    def _on_contract_event(self, event: Event):
        """处理合约事件"""
        contract_data: ContractData = event.data
        self.contract_data[contract_data.vt_symbol] = contract_data
    
    def _on_log_event(self, event: Event):
        """处理日志事件"""
        log_data = event.data
        if "连接成功" in log_data.msg:
            gateway_name = log_data.gateway_name
            if gateway_name:
                self._update_account_status(gateway_name, AccountStatus.CONNECTED)
        elif "连接失败" in log_data.msg or "连接断开" in log_data.msg:
            gateway_name = log_data.gateway_name
            if gateway_name:
                self._update_account_status(gateway_name, AccountStatus.ERROR)
    
    def _update_account_status(self, gateway_name: str, status: AccountStatus):
        """更新账户状态"""
        old_status = self.account_status.get(gateway_name)
        self.account_status[gateway_name] = status
        
        if old_status != status:
            self.logger.info(f"账户 {gateway_name} 状态变更: {old_status} -> {status}")
            
            # 通知状态变更回调
            for callback in self.status_callbacks:
                try:
                    callback(gateway_name, status)
                except Exception as e:
                    self.logger.error(f"状态变更回调执行失败: {e}")
            
            # 如果连接断开，启动重连
            if status == AccountStatus.ERROR:
                self._start_reconnect(gateway_name)
    
    def connect_all_accounts(self) -> bool:
        """连接所有账户"""
        self.logger.info("开始连接所有账户...")
        success_count = 0
        
        for account_config in self.trading_config.accounts:
            if account_config.enabled and account_config.auto_connect:
                if self.connect_account(account_config.name):
                    success_count += 1
        
        total_accounts = len([acc for acc in self.trading_config.accounts 
                            if acc.enabled and acc.auto_connect])
        
        self.logger.info(f"账户连接完成: {success_count}/{total_accounts}")
        return success_count == total_accounts
    
    def connect_account(self, account_name: str) -> bool:
        """连接指定账户"""
        account_config = self.trading_config.get_account_config(account_name)
        if not account_config:
            self.logger.error(f"未找到账户配置: {account_name}")
            return False
        
        if not account_config.enabled:
            self.logger.warning(f"账户 {account_name} 未启用")
            return False
        
        try:
            self.logger.info(f"正在连接账户: {account_name}")
            self._update_account_status(account_name, AccountStatus.CONNECTING)
            
            # 执行连接
            success = self.main_engine.connect(
                account_config.settings, 
                account_config.gateway_name
            )
            
            if success:
                self.last_connect_time[account_name] = datetime.now()
                self.reconnect_counts[account_name] = 0
                self.logger.info(f"账户 {account_name} 连接请求已发送")
                return True
            else:
                self._update_account_status(account_name, AccountStatus.ERROR)
                self.logger.error(f"账户 {account_name} 连接失败")
                return False
                
        except Exception as e:
            self._update_account_status(account_name, AccountStatus.ERROR)
            self.logger.error(f"连接账户 {account_name} 时发生异常: {e}")
            return False
    
    def disconnect_account(self, account_name: str) -> bool:
        """断开指定账户"""
        try:
            self.logger.info(f"正在断开账户: {account_name}")
            
            # 停止重连线程
            if account_name in self.reconnect_threads:
                # 这里应该有停止线程的逻辑，但threading.Thread没有直接的停止方法
                # 实际实现中可以使用事件或标志来控制线程退出
                pass
            
            # 执行断开
            gateway = self.main_engine.get_gateway(account_name)
            if gateway:
                gateway.close()
                self._update_account_status(account_name, AccountStatus.DISCONNECTED)
                self.logger.info(f"账户 {account_name} 已断开")
                return True
            else:
                self.logger.warning(f"未找到账户网关: {account_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"断开账户 {account_name} 时发生异常: {e}")
            return False
    
    def _start_reconnect(self, account_name: str):
        """启动重连线程"""
        account_config = self.trading_config.get_account_config(account_name)
        if not account_config or not account_config.auto_connect:
            return
        
        # 检查重连次数限制
        reconnect_count = self.reconnect_counts.get(account_name, 0)
        if reconnect_count >= account_config.max_reconnect_times:
            self.logger.error(f"账户 {account_name} 重连次数已达上限 {account_config.max_reconnect_times}")
            return
        
        # 如果已有重连线程在运行，不重复启动
        if account_name in self.reconnect_threads and self.reconnect_threads[account_name].is_alive():
            return
        
        # 启动重连线程
        reconnect_thread = threading.Thread(
            target=self._reconnect_account,
            args=(account_name,),
            daemon=True
        )
        self.reconnect_threads[account_name] = reconnect_thread
        reconnect_thread.start()
        
        self.logger.info(f"已启动账户 {account_name} 重连线程")
    
    def _reconnect_account(self, account_name: str):
        """重连账户线程函数"""
        account_config = self.trading_config.get_account_config(account_name)
        if not account_config:
            return
        
        while True:
            try:
                # 等待重连间隔
                time.sleep(account_config.reconnect_interval)
                
                # 检查是否需要重连
                current_status = self.account_status.get(account_name)
                if current_status == AccountStatus.CONNECTED:
                    self.logger.info(f"账户 {account_name} 已连接，停止重连")
                    break
                
                # 检查重连次数限制
                reconnect_count = self.reconnect_counts.get(account_name, 0)
                if reconnect_count >= account_config.max_reconnect_times:
                    self.logger.error(f"账户 {account_name} 重连次数已达上限")
                    break
                
                # 执行重连
                self.logger.info(f"正在重连账户 {account_name} (第{reconnect_count + 1}次)")
                self._update_account_status(account_name, AccountStatus.RECONNECTING)
                
                if self.connect_account(account_name):
                    self.reconnect_counts[account_name] = reconnect_count + 1
                    # 等待连接结果
                    time.sleep(10)
                    
                    if self.account_status.get(account_name) == AccountStatus.CONNECTED:
                        self.logger.info(f"账户 {account_name} 重连成功")
                        break
                else:
                    self.reconnect_counts[account_name] = reconnect_count + 1
                    
            except Exception as e:
                self.logger.error(f"重连账户 {account_name} 时发生异常: {e}")
                break
    
    def _monitor_accounts(self):
        """监控账户状态线程"""
        while True:
            try:
                time.sleep(self.system_config.scheduler.monitor_interval)
                
                # 检查账户状态
                for account_name, status in self.account_status.items():
                    if status == AccountStatus.CONNECTED:
                        # 检查账户数据是否正常更新
                        account_data = self.account_data.get(account_name)
                        if account_data:
                            # 这里可以添加更多的健康检查逻辑
                            pass
                
            except Exception as e:
                self.logger.error(f"账户监控线程异常: {e}")
    
    def get_account_status(self, account_name: str) -> AccountStatus:
        """获取账户状态"""
        return self.account_status.get(account_name, AccountStatus.DISCONNECTED)
    
    def get_account_data(self, account_name: str) -> Optional[AccountData]:
        """获取账户数据"""
        return self.account_data.get(account_name)
    
    def get_all_contracts(self) -> Dict[str, ContractData]:
        """获取所有合约数据"""
        return self.contract_data.copy()
    
    def is_account_connected(self, account_name: str) -> bool:
        """检查账户是否已连接"""
        return self.get_account_status(account_name) == AccountStatus.CONNECTED
    
    def are_all_accounts_connected(self) -> bool:
        """检查所有账户是否都已连接"""
        enabled_accounts = [acc.name for acc in self.trading_config.accounts 
                          if acc.enabled and acc.auto_connect]
        
        for account_name in enabled_accounts:
            if not self.is_account_connected(account_name):
                return False
        
        return True
    
    def add_status_callback(self, callback: Callable):
        """添加状态变更回调"""
        self.status_callbacks.append(callback)
    
    def add_account_callback(self, callback: Callable):
        """添加账户数据回调"""
        self.account_callbacks.append(callback)
    
    def get_status_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        return {
            'total_accounts': len(self.trading_config.accounts),
            'enabled_accounts': len([acc for acc in self.trading_config.accounts if acc.enabled]),
            'connected_accounts': len([name for name, status in self.account_status.items() 
                                     if status == AccountStatus.CONNECTED]),
            'account_status': {name: status.value for name, status in self.account_status.items()},
            'reconnect_counts': self.reconnect_counts.copy()
        }


if __name__ == "__main__":
    # 测试账户管理器
    from vnpy.event import EventEngine
    from vnpy.trader.engine import MainEngine
    
    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)
    
    account_manager = AccountManager(main_engine, event_engine)
    
    print("账户管理器测试:")
    print(f"状态摘要: {account_manager.get_status_summary()}")
    
    # 模拟连接所有账户
    # account_manager.connect_all_accounts()
    
    time.sleep(2)
    print(f"连接后状态: {account_manager.get_status_summary()}")
