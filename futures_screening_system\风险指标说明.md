# 期货品种风险指标说明文档

## 📊 CSV输出指标详解

### 基础信息指标
| 指标名称 | 说明 | 示例 |
|---------|------|------|
| 品种 | 期货合约代码 | SR888.CZCE |
| 趋势方向 | 当前技术分析趋势 | 多头/空头 |
| 最佳周期 | 推荐的交易周期 | 5分钟/15分钟/30分钟 |
| 综合评分 | 品种筛选综合得分 | 64.5 |
| 状态 | 交易建议状态 | 重点关注/准备入场/观察 |

### 保证金与杠杆指标
| 指标名称 | 说明 | 计算方式 | 示例 |
|---------|------|----------|------|
| **交易所保证金比例** | 交易所规定的最低保证金比例 | 从Excel文件统计最常见值 | 7.00% |
| **杠杆倍数** | 基于保证金比例的理论杠杆 | 1 ÷ 保证金比例 | 14.3x |

### 风险评估指标
| 指标名称 | 说明 | 计算方式 | 示例 |
|---------|------|----------|------|
| **综合风险系数** | 考虑品种类型和杠杆的综合风险 | 基础风险 × 杠杆调整因子 | 1.43 |
| **风险等级** | 基于风险系数的等级分类 | 低风险(<1.0)/中风险(1.0-1.5)/高风险(>1.5) | 中风险 |
| **品种类型风险** | 品种类型分类 | 商品期货/股指期货/国债期货 | 商品期货 |
| **杠杆风险调整** | 杠杆对风险的调整系数 | min(杠杆÷10, 2.0) | 1.43 |

### 资金配置指标
| 指标名称 | 说明 | 计算方式 | 示例 |
|---------|------|----------|------|
| **资金配置** | 使用的资金配置方案 | 预设方案名称 | 100万元 |
| **总资金** | 总可用资金 | 配置方案金额 | 1000000 |
| **推荐原因** | 推荐该品种的综合原因 | 评分+状态+风险等级 | 评分64.5分，重点关注，中风险 |

## 🔍 指标计算逻辑详解

### 1. 保证金比例获取
```python
# 从Excel文件中获取品种的所有合约保证金数据
product_contracts = df[df['证券代码'].str.contains(product_code)]

# 统计出现次数最多的保证金比例
margin_counter = Counter(margin_ratios.values)
most_common_margin = margin_counter.most_common(1)[0][0]

# 转换为小数形式
margin_ratio = most_common_margin / 100.0
```

### 2. 杠杆倍数计算
```python
# 杠杆倍数 = 1 ÷ 保证金比例
leverage = 1.0 / margin_ratio
```

### 3. 综合风险系数计算
```python
# 基础风险系数（根据品种类型）
base_risk = {
    '股指期货': 1.5,  # IF, IH, IC, IM
    '国债期货': 1.2,  # T, TF, TS
    '商品期货': 1.0   # 其他品种
}

# 杠杆风险调整因子
leverage_risk_factor = min(leverage / 10.0, 2.0)

# 综合风险系数
comprehensive_risk = base_risk * leverage_risk_factor
```

### 4. 风险等级分类
```python
if comprehensive_risk > 1.5:
    risk_level = '高风险'
elif comprehensive_risk > 1.0:
    risk_level = '中风险'
else:
    risk_level = '低风险'
```

## 📈 实际应用示例

### 示例1: 商品期货 - 白糖(SR)
- **交易所保证金比例**: 7.00%
- **杠杆倍数**: 14.3x
- **基础风险**: 1.0 (商品期货)
- **杠杆调整**: 14.3÷10 = 1.43
- **综合风险系数**: 1.0 × 1.43 = 1.43
- **风险等级**: 中风险

### 示例2: 股指期货 - 中证500(IC)
- **交易所保证金比例**: 12.00%
- **杠杆倍数**: 8.3x
- **基础风险**: 1.5 (股指期货)
- **杠杆调整**: 8.3÷10 = 0.83
- **综合风险系数**: 1.5 × 0.83 = 1.25
- **风险等级**: 中风险

### 示例3: 商品期货 - 菜籽(cs)
- **交易所保证金比例**: 6.00%
- **杠杆倍数**: 16.7x
- **基础风险**: 1.0 (商品期货)
- **杠杆调整**: min(16.7÷10, 2.0) = 1.67
- **综合风险系数**: 1.0 × 1.67 = 1.67
- **风险等级**: 高风险

## ⚠️ 风险控制建议

### 低风险品种 (风险系数 < 1.0)
- **特征**: 保证金比例较高，杠杆相对较低
- **建议**: 可适当增加仓位，作为组合的稳定部分
- **示例**: lu888.INE (11%保证金, 9.1x杠杆)

### 中风险品种 (风险系数 1.0-1.5)
- **特征**: 保证金和杠杆适中
- **建议**: 标准仓位配置，密切关注市场变化
- **示例**: SR888.CZCE (7%保证金, 14.3x杠杆)

### 高风险品种 (风险系数 > 1.5)
- **特征**: 杠杆较高或品种波动性大
- **建议**: 减少仓位，加强风险管理
- **示例**: cs888.DCE (6%保证金, 16.7x杠杆)

## 🎯 使用建议

1. **组合配置**: 建议低、中、高风险品种合理搭配
2. **资金管理**: 根据风险等级调整单品种资金分配比例
3. **动态调整**: 定期更新保证金数据，重新计算风险指标
4. **止损设置**: 高风险品种设置更严格的止损条件

---
*本文档基于真实交易所保证金数据，为期货交易风险管理提供参考*
