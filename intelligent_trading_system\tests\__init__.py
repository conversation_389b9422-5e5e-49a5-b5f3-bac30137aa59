"""
智能交易系统测试模块
提供完整的单元测试、集成测试和性能测试
"""

import os
import sys
import unittest
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

# 测试套件导入
from .test_config import TestConfigModule
from .test_account_manager import TestAccountManager
from .test_screening import TestScreeningModule
from .test_allocation import TestAllocationModule
from .test_switching import TestSwitchingModule
from .test_strategy import TestStrategyModule
from .test_scheduler import TestSchedulerModule
from .test_monitoring import TestMonitoringModule
from .test_integration import TestSystemIntegration
from .test_performance import TestSystemPerformance


def create_test_suite():
    """创建完整的测试套件"""
    suite = unittest.TestSuite()
    
    # 添加单元测试
    suite.addTest(unittest.makeSuite(TestConfigModule))
    suite.addTest(unittest.makeSuite(TestAccountManager))
    suite.addTest(unittest.makeSuite(TestScreeningModule))
    suite.addTest(unittest.makeSuite(TestAllocationModule))
    suite.addTest(unittest.makeSuite(TestSwitchingModule))
    suite.addTest(unittest.makeSuite(TestStrategyModule))
    suite.addTest(unittest.makeSuite(TestSchedulerModule))
    suite.addTest(unittest.makeSuite(TestMonitoringModule))
    
    # 添加集成测试
    suite.addTest(unittest.makeSuite(TestSystemIntegration))
    
    # 添加性能测试
    suite.addTest(unittest.makeSuite(TestSystemPerformance))
    
    return suite


def run_all_tests():
    """运行所有测试"""
    print("=" * 80)
    print("智能交易系统测试套件")
    print("=" * 80)
    print(f"测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 创建测试套件
    suite = create_test_suite()
    
    # 运行测试
    runner = unittest.TextTestRunner(
        verbosity=2,
        stream=sys.stdout,
        descriptions=True,
        failfast=False
    )
    
    result = runner.run(suite)
    
    # 显示测试结果摘要
    print("\n" + "=" * 80)
    print("测试结果摘要")
    print("=" * 80)
    print(f"测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"跳过: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    
    # 显示失败和错误详情
    if result.failures:
        print(f"\n失败的测试 ({len(result.failures)} 个):")
        for test, traceback in result.failures:
            print(f"  - {test}")
    
    if result.errors:
        print(f"\n错误的测试 ({len(result.errors)} 个):")
        for test, traceback in result.errors:
            print(f"  - {test}")
    
    # 返回测试是否全部通过
    return len(result.failures) == 0 and len(result.errors) == 0


def run_unit_tests():
    """只运行单元测试"""
    suite = unittest.TestSuite()
    
    # 添加单元测试
    suite.addTest(unittest.makeSuite(TestConfigModule))
    suite.addTest(unittest.makeSuite(TestAccountManager))
    suite.addTest(unittest.makeSuite(TestScreeningModule))
    suite.addTest(unittest.makeSuite(TestAllocationModule))
    suite.addTest(unittest.makeSuite(TestSwitchingModule))
    suite.addTest(unittest.makeSuite(TestStrategyModule))
    suite.addTest(unittest.makeSuite(TestSchedulerModule))
    suite.addTest(unittest.makeSuite(TestMonitoringModule))
    
    runner = unittest.TextTestRunner(verbosity=2)
    return runner.run(suite)


def run_integration_tests():
    """只运行集成测试"""
    suite = unittest.TestSuite()
    suite.addTest(unittest.makeSuite(TestSystemIntegration))
    
    runner = unittest.TextTestRunner(verbosity=2)
    return runner.run(suite)


def run_performance_tests():
    """只运行性能测试"""
    suite = unittest.TestSuite()
    suite.addTest(unittest.makeSuite(TestSystemPerformance))
    
    runner = unittest.TextTestRunner(verbosity=2)
    return runner.run(suite)


if __name__ == "__main__":
    # 运行所有测试
    success = run_all_tests()
    
    # 根据测试结果退出
    sys.exit(0 if success else 1)
