"""
智能交易系统测试主程序
用于测试系统集成和各模块功能
"""

import os
import sys
import time
from datetime import datetime

# 添加路径
sys.path.append(os.path.dirname(__file__))
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from main_controller import IntelligentTradingSystem
from utils.logger import get_logger


def test_system_initialization():
    """测试系统初始化"""
    print("=" * 80)
    print("测试系统初始化")
    print("=" * 80)
    
    try:
        # 创建智能交易系统
        trading_system = IntelligentTradingSystem()
        
        # 测试初始化（不包含vnpy组件，避免依赖问题）
        print("开始测试组件初始化...")
        
        # 测试交易组件初始化
        success = trading_system._initialize_trading_components()
        print(f"交易组件初始化: {'成功' if success else '失败'}")
        
        # 测试调度组件初始化
        success = trading_system._initialize_scheduler_components()
        print(f"调度组件初始化: {'成功' if success else '失败'}")
        
        # 测试定时任务设置
        success = trading_system._setup_scheduled_tasks()
        print(f"定时任务设置: {'成功' if success else '失败'}")
        
        # 测试事件处理器设置
        success = trading_system._setup_event_handlers()
        print(f"事件处理器设置: {'成功' if success else '失败'}")
        
        print("系统初始化测试完成")
        return trading_system
    
    except Exception as e:
        print(f"系统初始化测试失败: {e}")
        return None


def test_scheduler_functionality(trading_system):
    """测试调度器功能"""
    print("\n" + "=" * 80)
    print("测试调度器功能")
    print("=" * 80)
    
    try:
        # 启动事件管理器
        trading_system.event_manager.start()
        print("事件管理器启动成功")
        
        # 启动系统监控
        trading_system.system_monitor.start_monitoring()
        print("系统监控启动成功")
        
        # 启动任务调度器
        trading_system.task_scheduler.start_scheduler()
        print("任务调度器启动成功")
        
        # 显示调度摘要
        trading_system.task_scheduler.display_schedule_summary()
        
        # 测试立即执行任务
        print("\n测试立即执行任务...")
        success = trading_system.task_scheduler.run_task_now("morning_screening")
        print(f"早盘筛选任务执行: {'成功' if success else '失败'}")
        
        success = trading_system.task_scheduler.run_task_now("capital_allocation")
        print(f"资金分配任务执行: {'成功' if success else '失败'}")
        
        # 等待一段时间观察系统运行
        print("\n系统运行中，等待5秒...")
        time.sleep(5)
        
        # 显示系统状态
        trading_system.display_system_status()
        
        return True
    
    except Exception as e:
        print(f"调度器功能测试失败: {e}")
        return False


def test_monitoring_functionality(trading_system):
    """测试监控功能"""
    print("\n" + "=" * 80)
    print("测试监控功能")
    print("=" * 80)
    
    try:
        # 显示监控摘要
        trading_system.system_monitor.display_monitor_summary()
        
        # 显示事件摘要
        trading_system.event_manager.display_event_summary()
        
        # 显示告警摘要
        trading_system.alert_manager.display_alert_summary()
        
        # 获取系统指标
        metrics = trading_system.system_monitor.get_current_system_metrics()
        if metrics:
            print(f"\n当前系统指标:")
            print(f"CPU使用率: {metrics.cpu_percent:.1f}%")
            print(f"内存使用率: {metrics.memory_percent:.1f}%")
            print(f"磁盘使用率: {metrics.disk_percent:.1f}%")
        
        # 获取事件统计
        event_stats = trading_system.event_manager.get_event_stats()
        print(f"\n事件统计:")
        print(f"总事件数: {event_stats['total_events']}")
        print(f"队列大小: {event_stats['queue_size']}")
        
        return True
    
    except Exception as e:
        print(f"监控功能测试失败: {e}")
        return False


def test_trading_components(trading_system):
    """测试交易组件功能"""
    print("\n" + "=" * 80)
    print("测试交易组件功能")
    print("=" * 80)
    
    try:
        # 测试期货筛选器
        print("测试期货筛选器...")
        # 注意：这里只测试接口，不执行实际筛选（需要数据库连接）
        scanner = trading_system.futures_scanner
        print(f"筛选器初始化: {'成功' if scanner else '失败'}")
        
        # 测试资金分配器
        print("测试资金分配器...")
        allocator = trading_system.capital_allocator
        print(f"分配器初始化: {'成功' if allocator else '失败'}")
        
        # 测试仓位管理器
        print("测试仓位管理器...")
        position_manager = trading_system.position_manager
        print(f"仓位管理器初始化: {'成功' if position_manager else '失败'}")
        
        # 测试合约检测器
        print("测试合约检测器...")
        detector = trading_system.contract_detector
        print(f"合约检测器初始化: {'成功' if detector else '失败'}")
        
        # 测试合约切换器
        print("测试合约切换器...")
        switcher = trading_system.contract_switcher
        print(f"合约切换器初始化: {'成功' if switcher else '失败'}")
        
        return True
    
    except Exception as e:
        print(f"交易组件测试失败: {e}")
        return False


def test_system_shutdown(trading_system):
    """测试系统关闭"""
    print("\n" + "=" * 80)
    print("测试系统关闭")
    print("=" * 80)
    
    try:
        # 停止任务调度器
        if trading_system.task_scheduler:
            trading_system.task_scheduler.stop_scheduler()
            print("任务调度器停止成功")
        
        # 停止系统监控
        if trading_system.system_monitor:
            trading_system.system_monitor.stop_monitoring()
            print("系统监控停止成功")
        
        # 停止事件管理器
        if trading_system.event_manager:
            trading_system.event_manager.stop()
            print("事件管理器停止成功")
        
        print("系统关闭测试完成")
        return True
    
    except Exception as e:
        print(f"系统关闭测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("智能交易系统集成测试开始")
    print("测试时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # 测试系统初始化
    trading_system = test_system_initialization()
    if not trading_system:
        print("系统初始化失败，测试终止")
        return
    
    # 测试交易组件
    if not test_trading_components(trading_system):
        print("交易组件测试失败")
    
    # 测试调度器功能
    if not test_scheduler_functionality(trading_system):
        print("调度器功能测试失败")
    
    # 测试监控功能
    if not test_monitoring_functionality(trading_system):
        print("监控功能测试失败")
    
    # 测试系统关闭
    if not test_system_shutdown(trading_system):
        print("系统关闭测试失败")
    
    print("\n" + "=" * 80)
    print("智能交易系统集成测试完成")
    print("=" * 80)


if __name__ == "__main__":
    main()
