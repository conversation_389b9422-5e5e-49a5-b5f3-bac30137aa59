"""
评估引擎
提供品种评估的核心算法和评分机制
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json

from ...config import get_trading_config
from ...utils.logger import get_logger
from ...utils.helpers import safe_float, format_percentage


class EvaluationEngine:
    """品种评估引擎"""
    
    def __init__(self):
        """初始化评估引擎"""
        self.logger = get_logger("EvaluationEngine")
        self.trading_config = get_trading_config()
        
        # 评估权重配置
        self.weights = {
            'trend': 0.3,      # 趋势权重
            'volatility': 0.4,  # 波动率权重
            'breakout': 0.3     # 突破权重
        }
        
        # 评分阈值
        self.score_thresholds = {
            'excellent': 80,    # 重点关注
            'good': 60,        # 准备入场
            'fair': 40,        # 观察
            'poor': 0          # 排除
        }
        
        self.logger.info("评估引擎初始化完成")
    
    def calculate_comprehensive_score(self, trend_score: float, volatility_score: float, 
                                    breakout_score: float) -> float:
        """计算综合评分"""
        try:
            comprehensive_score = (
                trend_score * self.weights['trend'] +
                volatility_score * self.weights['volatility'] +
                breakout_score * self.weights['breakout']
            )
            return round(comprehensive_score, 1)
        except Exception as e:
            self.logger.error(f"计算综合评分失败: {e}")
            return 0.0
    
    def determine_rating(self, score: float) -> str:
        """根据评分确定等级"""
        if score >= self.score_thresholds['excellent']:
            return '重点关注'
        elif score >= self.score_thresholds['good']:
            return '准备入场'
        elif score >= self.score_thresholds['fair']:
            return '观察'
        else:
            return '排除'
    
    def calculate_risk_level(self, symbol: str, volatility_score: float, 
                           trend_score: float) -> Dict[str, Any]:
        """计算风险等级"""
        try:
            # 基础风险评分
            base_risk = 50
            
            # 波动率调整
            if volatility_score > 80:
                volatility_risk = 30
            elif volatility_score > 60:
                volatility_risk = 20
            elif volatility_score > 40:
                volatility_risk = 10
            else:
                volatility_risk = 0
            
            # 趋势调整
            if trend_score > 70:
                trend_risk = -10  # 强趋势降低风险
            elif trend_score < 30:
                trend_risk = 20   # 弱趋势增加风险
            else:
                trend_risk = 0
            
            # 品种特定风险
            symbol_risk = self._get_symbol_specific_risk(symbol)
            
            # 综合风险评分
            total_risk = base_risk + volatility_risk + trend_risk + symbol_risk
            total_risk = max(0, min(100, total_risk))  # 限制在0-100范围
            
            # 确定风险等级
            if total_risk >= 80:
                risk_level = '高风险'
            elif total_risk >= 60:
                risk_level = '中高风险'
            elif total_risk >= 40:
                risk_level = '中等风险'
            elif total_risk >= 20:
                risk_level = '中低风险'
            else:
                risk_level = '低风险'
            
            return {
                'risk_score': total_risk,
                'risk_level': risk_level,
                'volatility_risk': volatility_risk,
                'trend_risk': trend_risk,
                'symbol_risk': symbol_risk
            }
        
        except Exception as e:
            self.logger.error(f"计算风险等级失败: {e}")
            return {
                'risk_score': 50,
                'risk_level': '中等风险',
                'volatility_risk': 0,
                'trend_risk': 0,
                'symbol_risk': 0
            }
    
    def _get_symbol_specific_risk(self, symbol: str) -> int:
        """获取品种特定风险"""
        # 提取品种代码
        if '.' in symbol:
            product_code = symbol.split('.')[0]
            # 移除数字
            import re
            product_code = re.sub(r'\d+', '', product_code).upper()
        else:
            product_code = symbol.upper()
        
        # 品种风险映射
        high_risk_products = ['RB', 'HC', 'I', 'J', 'JM']  # 黑色系
        medium_risk_products = ['CU', 'AL', 'ZN', 'PB', 'NI']  # 有色金属
        low_risk_products = ['C', 'CS', 'A', 'M', 'Y']  # 农产品
        
        if product_code in high_risk_products:
            return 15
        elif product_code in medium_risk_products:
            return 10
        elif product_code in low_risk_products:
            return 5
        else:
            return 0
    
    def calculate_position_suggestion(self, symbol: str, score: float, 
                                    risk_level: str, account_balance: float = 100000) -> Dict[str, Any]:
        """计算建议仓位"""
        try:
            # 基础仓位比例
            if score >= 80:
                base_position_ratio = 0.8  # 80%
            elif score >= 60:
                base_position_ratio = 0.6  # 60%
            elif score >= 40:
                base_position_ratio = 0.4  # 40%
            else:
                base_position_ratio = 0.0  # 不建议开仓
            
            # 风险调整
            risk_adjustments = {
                '低风险': 1.0,
                '中低风险': 0.9,
                '中等风险': 0.8,
                '中高风险': 0.6,
                '高风险': 0.4
            }
            
            risk_adjustment = risk_adjustments.get(risk_level, 0.8)
            adjusted_ratio = base_position_ratio * risk_adjustment
            
            # 计算建议资金
            suggested_capital = account_balance * adjusted_ratio
            
            # 获取品种信息计算手数
            margin_ratio = self._get_margin_ratio(symbol)
            contract_size = self._get_contract_size(symbol)
            estimated_price = self._get_estimated_price(symbol)
            
            if margin_ratio > 0 and contract_size > 0 and estimated_price > 0:
                margin_per_lot = estimated_price * contract_size * margin_ratio
                max_lots = int(suggested_capital / margin_per_lot) if margin_per_lot > 0 else 0
            else:
                max_lots = 0
            
            return {
                'suggested_capital': suggested_capital,
                'position_ratio': adjusted_ratio,
                'max_lots': max_lots,
                'margin_per_lot': margin_per_lot if 'margin_per_lot' in locals() else 0,
                'risk_adjustment': risk_adjustment
            }
        
        except Exception as e:
            self.logger.error(f"计算建议仓位失败: {e}")
            return {
                'suggested_capital': 0,
                'position_ratio': 0,
                'max_lots': 0,
                'margin_per_lot': 0,
                'risk_adjustment': 1.0
            }
    
    def _get_margin_ratio(self, symbol: str) -> float:
        """获取保证金比例"""
        # 从配置获取或使用默认值
        default_margins = {
            'RB': 0.08, 'HC': 0.08, 'I': 0.08, 'J': 0.08, 'JM': 0.08,
            'CU': 0.07, 'AL': 0.07, 'ZN': 0.07, 'PB': 0.07, 'NI': 0.07,
            'C': 0.05, 'CS': 0.05, 'A': 0.05, 'M': 0.05, 'Y': 0.05
        }
        
        product_code = symbol.split('.')[0] if '.' in symbol else symbol
        import re
        product_code = re.sub(r'\d+', '', product_code).upper()
        
        return default_margins.get(product_code, 0.08)
    
    def _get_contract_size(self, symbol: str) -> int:
        """获取合约乘数"""
        default_sizes = {
            'RB': 10, 'HC': 10, 'I': 100, 'J': 100, 'JM': 60,
            'CU': 5, 'AL': 5, 'ZN': 5, 'PB': 5, 'NI': 1,
            'C': 10, 'CS': 10, 'A': 10, 'M': 10, 'Y': 10
        }
        
        product_code = symbol.split('.')[0] if '.' in symbol else symbol
        import re
        product_code = re.sub(r'\d+', '', product_code).upper()
        
        return default_sizes.get(product_code, 10)
    
    def _get_estimated_price(self, symbol: str) -> float:
        """获取估算价格（这里使用默认值，实际应该从市场数据获取）"""
        # 这里应该从实时数据获取，暂时使用估算值
        default_prices = {
            'RB': 3500, 'HC': 3200, 'I': 800, 'J': 2000, 'JM': 1500,
            'CU': 70000, 'AL': 18000, 'ZN': 25000, 'PB': 15000, 'NI': 130000,
            'C': 2500, 'CS': 2800, 'A': 4000, 'M': 3500, 'Y': 8500
        }
        
        product_code = symbol.split('.')[0] if '.' in symbol else symbol
        import re
        product_code = re.sub(r'\d+', '', product_code).upper()
        
        return default_prices.get(product_code, 3000)
    
    def generate_evaluation_report(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成评估报告"""
        try:
            if not results:
                return {'error': '没有评估结果'}
            
            # 统计信息
            total_count = len(results)
            status_counts = {}
            score_distribution = {'80+': 0, '60-80': 0, '40-60': 0, '0-40': 0}
            
            for result in results:
                status = result.get('状态', '未知')
                status_counts[status] = status_counts.get(status, 0) + 1
                
                score = result.get('综合评分', 0)
                if score >= 80:
                    score_distribution['80+'] += 1
                elif score >= 60:
                    score_distribution['60-80'] += 1
                elif score >= 40:
                    score_distribution['40-60'] += 1
                else:
                    score_distribution['0-40'] += 1
            
            # 生成报告
            report = {
                'timestamp': datetime.now().isoformat(),
                'total_symbols': total_count,
                'status_distribution': status_counts,
                'score_distribution': score_distribution,
                'top_symbols': results[:10],  # 前10个品种
                'summary': {
                    'excellent_count': status_counts.get('重点关注', 0),
                    'good_count': status_counts.get('准备入场', 0),
                    'watch_count': status_counts.get('观察', 0),
                    'excluded_count': status_counts.get('排除', 0),
                    'success_rate': (status_counts.get('重点关注', 0) + status_counts.get('准备入场', 0)) / total_count * 100
                }
            }
            
            return report
        
        except Exception as e:
            self.logger.error(f"生成评估报告失败: {e}")
            return {'error': str(e)}
    
    def save_evaluation_report(self, report: Dict[str, Any], filename: str = None) -> str:
        """保存评估报告"""
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"evaluation_report_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"评估报告已保存到: {filename}")
            return filename
        
        except Exception as e:
            self.logger.error(f"保存评估报告失败: {e}")
            return ""


if __name__ == "__main__":
    # 测试评估引擎
    engine = EvaluationEngine()
    
    # 测试综合评分计算
    score = engine.calculate_comprehensive_score(70, 80, 60)
    print(f"综合评分: {score}")
    
    # 测试等级确定
    rating = engine.determine_rating(score)
    print(f"评级: {rating}")
    
    # 测试风险计算
    risk_info = engine.calculate_risk_level("RB888.SHFE", 80, 70)
    print(f"风险信息: {risk_info}")
    
    # 测试仓位建议
    position_info = engine.calculate_position_suggestion("RB888.SHFE", score, risk_info['risk_level'])
    print(f"仓位建议: {position_info}")
    
    print("评估引擎测试完成")
