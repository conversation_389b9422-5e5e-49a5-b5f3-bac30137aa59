"""
辅助函数模块
提供常用的工具函数
"""

import os
import json
import time
from datetime import datetime, timedelta
from typing import Any, Optional, Union, Dict, List
from decimal import Decimal, ROUND_HALF_UP


def format_datetime(dt: datetime, fmt: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化日期时间"""
    if dt is None:
        return ""
    return dt.strftime(fmt)


def parse_datetime(dt_str: str, fmt: str = "%Y-%m-%d %H:%M:%S") -> Optional[datetime]:
    """解析日期时间字符串"""
    try:
        return datetime.strptime(dt_str, fmt)
    except (ValueError, TypeError):
        return None


def safe_float(value: Any, default: float = 0.0) -> float:
    """安全转换为浮点数"""
    try:
        if value is None or value == "":
            return default
        return float(value)
    except (ValueError, TypeError):
        return default


def safe_int(value: Any, default: int = 0) -> int:
    """安全转换为整数"""
    try:
        if value is None or value == "":
            return default
        return int(float(value))  # 先转float再转int，处理"1.0"这种情况
    except (ValueError, TypeError):
        return default


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """安全除法"""
    try:
        if denominator == 0:
            return default
        return numerator / denominator
    except (TypeError, ZeroDivisionError):
        return default


def round_to_pricetick(price: float, pricetick: float) -> float:
    """将价格舍入到最小价格变动单位"""
    if pricetick <= 0:
        return price
    
    # 使用Decimal进行精确计算
    price_decimal = Decimal(str(price))
    pricetick_decimal = Decimal(str(pricetick))
    
    # 计算倍数并四舍五入
    multiplier = (price_decimal / pricetick_decimal).quantize(
        Decimal('1'), rounding=ROUND_HALF_UP
    )
    
    # 返回舍入后的价格
    return float(multiplier * pricetick_decimal)


def calculate_commission(volume: int, price: float, commission_ratio: float, 
                        size: int = 1, min_commission: float = 5.0) -> float:
    """计算手续费"""
    commission = volume * price * size * commission_ratio
    return max(commission, min_commission)


def calculate_margin(volume: int, price: float, margin_ratio: float, size: int = 1) -> float:
    """计算保证金"""
    return volume * price * size * margin_ratio


def format_number(number: float, precision: int = 2) -> str:
    """格式化数字显示"""
    if abs(number) >= 1e8:
        return f"{number/1e8:.{precision}f}亿"
    elif abs(number) >= 1e4:
        return f"{number/1e4:.{precision}f}万"
    else:
        return f"{number:.{precision}f}"


def format_percentage(value: float, precision: int = 2) -> str:
    """格式化百分比"""
    return f"{value*100:.{precision}f}%"


def get_trading_day(dt: datetime = None) -> datetime:
    """获取交易日"""
    if dt is None:
        dt = datetime.now()
    
    # 如果是凌晨2:30之前，认为是前一个交易日的夜盘
    if dt.hour < 3:
        dt = dt - timedelta(days=1)
    
    # 跳过周末
    while dt.weekday() >= 5:  # 5=Saturday, 6=Sunday
        dt = dt - timedelta(days=1)
    
    return dt.replace(hour=0, minute=0, second=0, microsecond=0)


def is_trading_time(dt: datetime = None) -> bool:
    """判断是否在交易时间内"""
    if dt is None:
        dt = datetime.now()
    
    current_time = dt.time()
    weekday = dt.weekday()
    
    # 周末不交易
    if weekday >= 5:
        return False
    
    # 日盘时间：9:00-11:30, 13:30-15:00
    day_session1 = (current_time >= datetime.strptime("09:00", "%H:%M").time() and 
                   current_time <= datetime.strptime("11:30", "%H:%M").time())
    day_session2 = (current_time >= datetime.strptime("13:30", "%H:%M").time() and 
                   current_time <= datetime.strptime("15:00", "%H:%M").time())
    
    # 夜盘时间：21:00-02:30（次日）
    night_session = (current_time >= datetime.strptime("21:00", "%H:%M").time() or 
                    current_time <= datetime.strptime("02:30", "%H:%M").time())
    
    return day_session1 or day_session2 or night_session


def load_json_file(file_path: str, default: Any = None) -> Any:
    """加载JSON文件"""
    try:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            return default
    except Exception:
        return default


def save_json_file(data: Any, file_path: str) -> bool:
    """保存JSON文件"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception:
        return False


def retry_on_exception(max_retries: int = 3, delay: float = 1.0, 
                      exceptions: tuple = (Exception,)):
    """重试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        time.sleep(delay * (2 ** attempt))  # 指数退避
                        continue
                    else:
                        raise last_exception
            
        return wrapper
    return decorator


def validate_symbol_format(symbol: str) -> bool:
    """验证品种代码格式"""
    if not symbol:
        return False
    
    # 基本格式检查：字母开头，可包含数字和点
    import re
    pattern = r'^[A-Za-z]+\d*\.(SHFE|DCE|CZCE|CFFEX|INE)$'
    return bool(re.match(pattern, symbol))


def extract_product_code(symbol: str) -> str:
    """提取品种代码"""
    if '.' in symbol:
        symbol = symbol.split('.')[0]
    
    # 移除数字部分
    import re
    return re.sub(r'\d+', '', symbol).upper()


def generate_vt_symbol(symbol: str, exchange: str) -> str:
    """生成VT格式的品种代码"""
    return f"{symbol}.{exchange.upper()}"


def parse_vt_symbol(vt_symbol: str) -> tuple:
    """解析VT格式的品种代码"""
    if '.' in vt_symbol:
        symbol, exchange = vt_symbol.split('.', 1)
        return symbol, exchange
    else:
        return vt_symbol, ""


def calculate_position_value(volume: int, price: float, size: int = 1) -> float:
    """计算持仓价值"""
    return volume * price * size


def calculate_pnl(volume: int, entry_price: float, current_price: float, 
                 size: int = 1, direction: str = "long") -> float:
    """计算盈亏"""
    if direction.lower() == "long":
        return volume * (current_price - entry_price) * size
    else:
        return volume * (entry_price - current_price) * size


def get_file_size(file_path: str) -> int:
    """获取文件大小（字节）"""
    try:
        return os.path.getsize(file_path)
    except OSError:
        return 0


def ensure_directory(directory: str):
    """确保目录存在"""
    os.makedirs(directory, exist_ok=True)


def clean_old_files(directory: str, days: int = 30, pattern: str = "*"):
    """清理旧文件"""
    import glob
    from pathlib import Path
    
    cutoff_time = time.time() - (days * 24 * 60 * 60)
    
    for file_path in glob.glob(os.path.join(directory, pattern)):
        try:
            if os.path.getmtime(file_path) < cutoff_time:
                os.remove(file_path)
        except OSError:
            pass


def memory_usage() -> Dict[str, float]:
    """获取内存使用情况"""
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            'rss': memory_info.rss / 1024 / 1024,  # MB
            'vms': memory_info.vms / 1024 / 1024,  # MB
            'percent': process.memory_percent()
        }
    except ImportError:
        return {'error': 'psutil not available'}


def cpu_usage() -> float:
    """获取CPU使用率"""
    try:
        import psutil
        return psutil.cpu_percent(interval=1)
    except ImportError:
        return 0.0


if __name__ == "__main__":
    # 测试辅助函数
    print("辅助函数测试:")
    
    # 测试数字转换
    print(f"safe_float('123.45'): {safe_float('123.45')}")
    print(f"safe_int('123.45'): {safe_int('123.45')}")
    print(f"safe_float(''): {safe_float('')}")
    
    # 测试价格舍入
    print(f"round_to_pricetick(123.456, 0.1): {round_to_pricetick(123.456, 0.1)}")
    
    # 测试数字格式化
    print(f"format_number(12345678): {format_number(12345678)}")
    print(f"format_percentage(0.1234): {format_percentage(0.1234)}")
    
    # 测试时间相关
    now = datetime.now()
    print(f"format_datetime(now): {format_datetime(now)}")
    print(f"get_trading_day(): {get_trading_day()}")
    print(f"is_trading_time(): {is_trading_time()}")
    
    # 测试品种代码
    print(f"validate_symbol_format('RB888.SHFE'): {validate_symbol_format('RB888.SHFE')}")
    print(f"extract_product_code('RB2501.SHFE'): {extract_product_code('RB2501.SHFE')}")
    
    # 测试系统信息
    print(f"memory_usage(): {memory_usage()}")
    print(f"cpu_usage(): {cpu_usage()}")
    
    print("辅助函数测试完成")
