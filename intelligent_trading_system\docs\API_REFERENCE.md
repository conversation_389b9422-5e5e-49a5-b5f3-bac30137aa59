# 智能量化交易系统API参考

## 目录
1. [API概述](#api概述)
2. [核心模块API](#核心模块api)
3. [功能模块API](#功能模块api)
4. [配置模块API](#配置模块api)
5. [工具模块API](#工具模块api)
6. [事件系统API](#事件系统api)
7. [数据结构](#数据结构)
8. [异常处理](#异常处理)

## API概述

智能量化交易系统提供完整的Python API，支持：

- **模块化设计**: 每个功能模块独立封装
- **类型安全**: 使用类型注解和dataclass
- **异步支持**: 支持多线程和异步操作
- **事件驱动**: 基于事件的松耦合架构
- **配置管理**: 统一的配置管理接口

## 核心模块API

### AccountManager

账户管理器，负责CTP账户连接和管理。

```python
from core.account_manager import AccountManager, AccountConfig

class AccountManager:
    """账户管理器"""
    
    def __init__(self, config: AccountConfig):
        """
        初始化账户管理器
        
        Args:
            config: 账户配置
        """
    
    def initialize(self) -> bool:
        """
        初始化账户连接
        
        Returns:
            bool: 初始化是否成功
        """
    
    def login(self) -> bool:
        """
        登录CTP账户
        
        Returns:
            bool: 登录是否成功
        """
    
    def logout(self) -> bool:
        """
        登出CTP账户
        
        Returns:
            bool: 登出是否成功
        """
    
    def get_account_info(self) -> Dict[str, Any]:
        """
        获取账户信息
        
        Returns:
            Dict[str, Any]: 账户信息字典
        """
    
    def get_positions(self) -> List[PositionData]:
        """
        获取持仓信息
        
        Returns:
            List[PositionData]: 持仓数据列表
        """
    
    def get_orders(self) -> List[OrderData]:
        """
        获取委托信息
        
        Returns:
            List[OrderData]: 委托数据列表
        """
```

### IntelligentTradingSystem

主控系统类，协调所有模块。

```python
from main_controller import IntelligentTradingSystem

class IntelligentTradingSystem:
    """智能交易系统主控制器"""
    
    def __init__(self):
        """初始化系统"""
    
    def start(self) -> bool:
        """
        启动系统
        
        Returns:
            bool: 启动是否成功
        """
    
    def stop(self) -> bool:
        """
        停止系统
        
        Returns:
            bool: 停止是否成功
        """
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取系统状态
        
        Returns:
            Dict[str, Any]: 系统状态信息
        """
    
    def run_screening(self) -> Dict[str, float]:
        """
        执行品种筛选
        
        Returns:
            Dict[str, float]: 品种评分结果
        """
    
    def run_allocation(self, screening_results: Dict[str, float]) -> Dict[str, int]:
        """
        执行资金分配
        
        Args:
            screening_results: 筛选结果
            
        Returns:
            Dict[str, int]: 分配结果
        """
```

## 功能模块API

### 筛选模块

#### FuturesScanner

期货品种扫描器。

```python
from modules.screening.futures_scanner import FuturesScanner, ScanConfig

class FuturesScanner:
    """期货品种扫描器"""
    
    def __init__(self, config: ScanConfig):
        """
        初始化扫描器
        
        Args:
            config: 扫描配置
        """
    
    def scan_all_symbols(self) -> Dict[str, Dict[str, float]]:
        """
        扫描所有品种
        
        Returns:
            Dict[str, Dict[str, float]]: 品种指标数据
        """
    
    def scan_symbol(self, symbol: str) -> Dict[str, float]:
        """
        扫描单个品种
        
        Args:
            symbol: 品种代码
            
        Returns:
            Dict[str, float]: 品种指标数据
        """
    
    def get_market_data(self, symbol: str, timeframe: str = "1m") -> List[BarData]:
        """
        获取市场数据
        
        Args:
            symbol: 品种代码
            timeframe: 时间周期
            
        Returns:
            List[BarData]: K线数据列表
        """
```

#### EvaluationEngine

评估引擎。

```python
from modules.screening.evaluation_engine import EvaluationEngine, EvaluationConfig

class EvaluationEngine:
    """评估引擎"""
    
    def __init__(self, config: EvaluationConfig):
        """
        初始化评估引擎
        
        Args:
            config: 评估配置
        """
    
    def evaluate_symbols(self, symbol_data: Dict[str, Dict[str, float]]) -> Dict[str, float]:
        """
        评估品种
        
        Args:
            symbol_data: 品种数据
            
        Returns:
            Dict[str, float]: 品种评分
        """
    
    def calculate_trend_score(self, data: Dict[str, float]) -> float:
        """
        计算趋势评分
        
        Args:
            data: 技术指标数据
            
        Returns:
            float: 趋势评分
        """
    
    def calculate_volatility_score(self, data: Dict[str, float]) -> float:
        """
        计算波动率评分
        
        Args:
            data: 技术指标数据
            
        Returns:
            float: 波动率评分
        """
```

### 分配模块

#### CapitalAllocator

资金分配器。

```python
from modules.allocation.capital_allocator import CapitalAllocator, AllocationConfig

class CapitalAllocator:
    """资金分配器"""
    
    def __init__(self, config: AllocationConfig):
        """
        初始化分配器
        
        Args:
            config: 分配配置
        """
    
    def allocate_capital(self, 
                        screening_results: Dict[str, float],
                        total_capital: float) -> Dict[str, AllocationResult]:
        """
        分配资金
        
        Args:
            screening_results: 筛选结果
            total_capital: 总资金
            
        Returns:
            Dict[str, AllocationResult]: 分配结果
        """
    
    def calculate_position_size(self, 
                               symbol: str, 
                               allocated_capital: float,
                               price: float) -> int:
        """
        计算仓位大小
        
        Args:
            symbol: 品种代码
            allocated_capital: 分配资金
            price: 当前价格
            
        Returns:
            int: 仓位手数
        """
```

#### PositionManager

仓位管理器。

```python
from modules.allocation.position_manager import PositionManager, PositionConfig

class PositionManager:
    """仓位管理器"""
    
    def __init__(self, config: PositionConfig):
        """
        初始化仓位管理器
        
        Args:
            config: 仓位配置
        """
    
    def adjust_positions(self, 
                        target_positions: Dict[str, int],
                        current_positions: Dict[str, int]) -> List[OrderRequest]:
        """
        调整仓位
        
        Args:
            target_positions: 目标仓位
            current_positions: 当前仓位
            
        Returns:
            List[OrderRequest]: 委托请求列表
        """
    
    def check_risk_limits(self, positions: Dict[str, int]) -> List[str]:
        """
        检查风险限制
        
        Args:
            positions: 仓位信息
            
        Returns:
            List[str]: 风险警告列表
        """
```

### 切换模块

#### MainContractDetector

主力合约检测器。

```python
from modules.switching.main_contract_detector import MainContractDetector, DetectorConfig

class MainContractDetector:
    """主力合约检测器"""
    
    def __init__(self, config: DetectorConfig):
        """
        初始化检测器
        
        Args:
            config: 检测配置
        """
    
    def detect_main_contracts(self) -> Dict[str, str]:
        """
        检测主力合约
        
        Returns:
            Dict[str, str]: 品种->主力合约映射
        """
    
    def should_switch_contract(self, 
                              symbol: str, 
                              current_contract: str) -> Tuple[bool, str]:
        """
        判断是否需要切换合约
        
        Args:
            symbol: 品种代码
            current_contract: 当前合约
            
        Returns:
            Tuple[bool, str]: (是否切换, 新合约)
        """
```

### 策略模块

#### StrategyManager

策略管理器。

```python
from modules.strategy.strategy_manager import StrategyManager, StrategyConfig

class StrategyManager:
    """策略管理器"""
    
    def __init__(self, config: StrategyConfig):
        """
        初始化策略管理器
        
        Args:
            config: 策略配置
        """
    
    def create_strategy(self, 
                       strategy_name: str, 
                       symbol: str, 
                       parameters: Dict[str, Any]) -> str:
        """
        创建策略实例
        
        Args:
            strategy_name: 策略名称
            symbol: 交易品种
            parameters: 策略参数
            
        Returns:
            str: 策略实例ID
        """
    
    def start_strategy(self, strategy_id: str) -> bool:
        """
        启动策略
        
        Args:
            strategy_id: 策略ID
            
        Returns:
            bool: 启动是否成功
        """
    
    def stop_strategy(self, strategy_id: str) -> bool:
        """
        停止策略
        
        Args:
            strategy_id: 策略ID
            
        Returns:
            bool: 停止是否成功
        """
    
    def get_strategy_status(self, strategy_id: str) -> Dict[str, Any]:
        """
        获取策略状态
        
        Args:
            strategy_id: 策略ID
            
        Returns:
            Dict[str, Any]: 策略状态信息
        """
```

### 调度模块

#### TaskScheduler

任务调度器。

```python
from modules.scheduler.task_scheduler import TaskScheduler, SchedulerConfig

class TaskScheduler:
    """任务调度器"""
    
    def __init__(self, config: SchedulerConfig):
        """
        初始化调度器
        
        Args:
            config: 调度配置
        """
    
    def add_task(self, 
                task_name: str, 
                task_func: Callable,
                schedule_time: str,
                **kwargs) -> str:
        """
        添加定时任务
        
        Args:
            task_name: 任务名称
            task_func: 任务函数
            schedule_time: 调度时间
            **kwargs: 任务参数
            
        Returns:
            str: 任务ID
        """
    
    def remove_task(self, task_id: str) -> bool:
        """
        移除任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 移除是否成功
        """
    
    def start_scheduler(self) -> bool:
        """
        启动调度器
        
        Returns:
            bool: 启动是否成功
        """
    
    def stop_scheduler(self) -> bool:
        """
        停止调度器
        
        Returns:
            bool: 停止是否成功
        """
```

### 监控模块

#### SystemMonitor

系统监控器。

```python
from modules.monitoring.system_monitor import SystemMonitor, MonitorConfig

class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, config: MonitorConfig):
        """
        初始化监控器
        
        Args:
            config: 监控配置
        """
    
    def get_system_metrics(self) -> Dict[str, float]:
        """
        获取系统指标
        
        Returns:
            Dict[str, float]: 系统指标数据
        """
    
    def get_trading_metrics(self) -> Dict[str, Any]:
        """
        获取交易指标
        
        Returns:
            Dict[str, Any]: 交易指标数据
        """
    
    def check_system_health(self) -> Tuple[bool, List[str]]:
        """
        检查系统健康状态
        
        Returns:
            Tuple[bool, List[str]]: (健康状态, 问题列表)
        """
```

## 配置模块API

### SystemConfig

系统配置类。

```python
from config.system_config import SystemConfig, DatabaseConfig, LoggingConfig

@dataclass
class SystemConfig:
    """系统配置"""
    database: DatabaseConfig
    logging: LoggingConfig
    scheduler: SchedulerConfig
    monitoring: MonitoringConfig
    debug_mode: bool = False
    test_mode: bool = False
    
    @classmethod
    def load_from_file(cls, config_path: str) -> 'SystemConfig':
        """
        从文件加载配置
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            SystemConfig: 配置实例
        """
    
    def save_to_file(self, config_path: str) -> bool:
        """
        保存配置到文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            bool: 保存是否成功
        """
    
    def validate(self) -> Tuple[bool, List[str]]:
        """
        验证配置
        
        Returns:
            Tuple[bool, List[str]]: (验证结果, 错误列表)
        """
```

## 工具模块API

### Logger

日志工具。

```python
from utils.logger import get_logger, setup_logging

def get_logger(name: str) -> logging.Logger:
    """
    获取日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        logging.Logger: 日志器实例
    """

def setup_logging(config: LoggingConfig) -> bool:
    """
    设置日志系统
    
    Args:
        config: 日志配置
        
    Returns:
        bool: 设置是否成功
    """
```

### Helpers

辅助函数。

```python
from utils.helpers import (
    get_trading_day,
    is_trading_time,
    format_number,
    calculate_returns,
    validate_symbol
)

def get_trading_day() -> str:
    """
    获取交易日
    
    Returns:
        str: 交易日字符串 (YYYYMMDD)
    """

def is_trading_time() -> bool:
    """
    判断是否为交易时间
    
    Returns:
        bool: 是否为交易时间
    """

def format_number(value: float, precision: int = 2) -> str:
    """
    格式化数字
    
    Args:
        value: 数值
        precision: 精度
        
    Returns:
        str: 格式化后的字符串
    """

def calculate_returns(prices: List[float]) -> List[float]:
    """
    计算收益率
    
    Args:
        prices: 价格序列
        
    Returns:
        List[float]: 收益率序列
    """

def validate_symbol(symbol: str) -> bool:
    """
    验证品种代码
    
    Args:
        symbol: 品种代码
        
    Returns:
        bool: 是否有效
    """
```

## 事件系统API

### EventManager

事件管理器。

```python
from modules.scheduler.event_manager import EventManager, EventType, Event

class EventManager:
    """事件管理器"""
    
    def __init__(self):
        """初始化事件管理器"""
    
    def register_handler(self, 
                        event_type: EventType, 
                        handler: Callable[[Event], None]) -> str:
        """
        注册事件处理器
        
        Args:
            event_type: 事件类型
            handler: 处理函数
            
        Returns:
            str: 处理器ID
        """
    
    def unregister_handler(self, handler_id: str) -> bool:
        """
        注销事件处理器
        
        Args:
            handler_id: 处理器ID
            
        Returns:
            bool: 注销是否成功
        """
    
    def emit_event(self, 
                  event_type: EventType, 
                  data: Any, 
                  source: str) -> None:
        """
        发射事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
            source: 事件源
        """
    
    def start(self) -> bool:
        """
        启动事件管理器
        
        Returns:
            bool: 启动是否成功
        """
    
    def stop(self) -> bool:
        """
        停止事件管理器
        
        Returns:
            bool: 停止是否成功
        """
```

### EventType

事件类型枚举。

```python
from enum import Enum

class EventType(Enum):
    """事件类型"""
    
    # 系统事件
    SYSTEM_START = "system_start"
    SYSTEM_STOP = "system_stop"
    SYSTEM_ERROR = "system_error"
    
    # 账户事件
    ACCOUNT_LOGIN = "account_login"
    ACCOUNT_LOGOUT = "account_logout"
    ACCOUNT_UPDATE = "account_update"
    
    # 筛选事件
    SCAN_START = "scan_start"
    SCAN_COMPLETE = "scan_complete"
    SCAN_ERROR = "scan_error"
    
    # 分配事件
    ALLOCATION_START = "allocation_start"
    ALLOCATION_COMPLETE = "allocation_complete"
    ALLOCATION_ERROR = "allocation_error"
    
    # 策略事件
    STRATEGY_START = "strategy_start"
    STRATEGY_STOP = "strategy_stop"
    STRATEGY_ERROR = "strategy_error"
    
    # 交易事件
    ORDER_SENT = "order_sent"
    ORDER_FILLED = "order_filled"
    ORDER_CANCELLED = "order_cancelled"
    
    # 风险事件
    RISK_WARNING = "risk_warning"
    RISK_LIMIT_EXCEEDED = "risk_limit_exceeded"
    
    # 监控事件
    HEALTH_CHECK = "health_check"
    PERFORMANCE_UPDATE = "performance_update"
```

## 数据结构

### 核心数据类

```python
from dataclasses import dataclass
from datetime import datetime
from typing import Optional

@dataclass
class BarData:
    """K线数据"""
    symbol: str
    datetime: datetime
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: int
    open_interest: int

@dataclass
class PositionData:
    """持仓数据"""
    symbol: str
    direction: str  # "long" or "short"
    volume: int
    price: float
    pnl: float
    frozen: int

@dataclass
class OrderData:
    """委托数据"""
    orderid: str
    symbol: str
    direction: str
    offset: str
    price: float
    volume: int
    status: str
    datetime: datetime

@dataclass
class AllocationResult:
    """分配结果"""
    symbol: str
    score: float
    allocated_capital: float
    position_size: int
    risk_level: str
    margin_required: float

@dataclass
class ScanResult:
    """扫描结果"""
    symbol: str
    trend_score: float
    volatility_score: float
    breakout_score: float
    total_score: float
    indicators: Dict[str, float]
    timestamp: datetime
```

## 异常处理

### 自定义异常类

```python
class TradingSystemError(Exception):
    """交易系统基础异常"""
    pass

class ConfigurationError(TradingSystemError):
    """配置错误"""
    pass

class ConnectionError(TradingSystemError):
    """连接错误"""
    pass

class DataError(TradingSystemError):
    """数据错误"""
    pass

class StrategyError(TradingSystemError):
    """策略错误"""
    pass

class RiskError(TradingSystemError):
    """风险控制错误"""
    pass
```

### 异常处理示例

```python
try:
    system = IntelligentTradingSystem()
    system.start()
except ConfigurationError as e:
    logger.error(f"配置错误: {e}")
except ConnectionError as e:
    logger.error(f"连接错误: {e}")
except TradingSystemError as e:
    logger.error(f"系统错误: {e}")
except Exception as e:
    logger.exception(f"未知错误: {e}")
```

通过以上API参考，开发者可以：
1. 理解系统各模块的接口定义
2. 正确使用系统提供的功能
3. 扩展和定制系统功能
4. 处理各种异常情况

更多使用示例请参考用户指南和开发者指南。
