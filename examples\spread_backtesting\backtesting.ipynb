{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["#%%\n", "from vnpy.app.spread_trading.backtesting import BacktestingEngine\n", "from vnpy.app.spread_trading.strategies.statistical_arbitrage_strategy import (\n", "    StatisticalArbitrageStrategy\n", ")\n", "from vnpy.app.spread_trading.base import LegData, SpreadData\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["spread = SpreadData(\n", "    name=\"IF-Spread\",\n", "    legs=[LegData(\"IF1911.CFFEX\"), LegData(\"IF1912.CFFEX\")],\n", "    price_multipliers={\"IF1911.CFFEX\": 1, \"IF1912.CFFEX\": -1},\n", "    trading_multipliers={\"IF1911.CFFEX\": 1, \"IF1912.CFFEX\": -1},\n", "    active_symbol=\"IF1911.CFFEX\",\n", "    inverse_contracts={\"IF1911.CFFEX\": False, \"IF1912.CFFEX\": False},\n", "    min_volume=1\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["#%%\n", "engine = BacktestingEngine()\n", "engine.set_parameters(\n", "    spread=spread,\n", "    interval=\"1m\",\n", "    start=datetime(2019, 6, 10),\n", "    end=datetime(2019, 11, 10),\n", "    rate=0,\n", "    slippage=0,\n", "    size=300,\n", "    pricetick=0.2,\n", "    capital=1_000_000,\n", ")\n", "engine.add_strategy(StatisticalArbitrageStrategy, {})"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2019-11-14 12:22:16.758224\t开始加载历史数据\n", "2019-11-14 12:22:20.582443\t历史数据加载完成，数据量：7200\n", "2019-09-23 11:10:00 long 0.0 -1.6 -1.595771499962582\n", "2019-09-23 11:22:00 short 0.0 5.0 4.434537272006142\n", "2019-09-23 11:24:00 short 0.0 5.2 4.9688499583209484\n", "2019-09-23 11:28:00 long 0.0 -3.8 -3.695330911067618\n", "2019-09-23 13:25:00 long 0.0 -0.4 -0.23693471309323555\n", "2019-09-23 13:34:00 long 0.0 -1.8 -1.4179666393179509\n", "2019-09-23 14:02:00 long 0.0 -0.8 -0.5778248012256164\n", "2019-09-23 14:04:00 long 0.0 -1.2 -1.1925798475317886\n", "2019-09-23 14:06:00 long 0.0 -2.0 -1.8379710609249407\n", "2019-09-23 14:39:00 long 0.0 -2.8 -2.0334313701260256\n", "2019-09-23 14:43:00 long 0.0 -3.4 -2.787519389787807\n", "2019-09-23 14:57:00 long 0.0 -5.8 -4.27440470371467\n", "2019-09-24 10:05:00 long 0.0 -3.8 -3.179999999999999\n", "2019-09-24 10:06:00 long 0.0 -4.6 -3.857025532524194\n", "2019-09-24 10:57:00 long 0.0 -5.0 -4.445544546049673\n", "2019-09-24 11:25:00 short 0.0 3.2 1.6423691723275973\n", "2019-09-24 13:17:00 long 0.0 -4.0 -3.4607215153133146\n", "2019-09-24 13:18:00 long 0.0 -4.0 -3.873052190979691\n", "2019-09-24 13:20:00 short 0.0 1.4 0.6782346393911616\n", "2019-09-24 13:49:00 long 0.0 -5.6 -4.322203430984213\n", "2019-09-24 13:51:00 long 0.0 -5.4 -5.351933561796388\n", "2019-09-24 14:16:00 short 0.0 2.8 2.1558010238906187\n", "2019-09-24 14:23:00 short 0.0 4.2 4.128626114345908\n", "2019-09-24 14:34:00 short 0.0 5.6 5.020673910007105\n", "2019-09-25 10:00:00 long 0.0 -0.8 -0.4674138285233176\n", "2019-09-25 10:52:00 short 0.0 3.8 3.1140497542965124\n", "2019-09-25 11:06:00 long 0.0 -5.6 -4.718752682402608\n", "2019-09-25 11:07:00 long 0.0 -6.8 -5.8570761010398895\n", "2019-09-25 11:09:00 long 0.0 -9.6 -8.30701394770012\n", "2019-09-25 13:07:00 long 0.0 -2.4 -2.010688783461635\n", "2019-09-25 13:09:00 long 0.0 -3.2 -2.9096527568441326\n", "2019-09-25 13:14:00 long 0.0 -5.8 -4.557175209443296\n", "2019-09-25 13:16:00 short 0.0 2.8 2.71190168255482\n", "2019-09-25 13:40:00 short 0.0 3.0 2.393542602265727\n", "2019-09-25 13:48:00 short 0.0 5.0 3.9270050210519605\n", "2019-09-25 13:49:00 short 0.0 5.6 4.78127049528201\n", "2019-09-25 13:50:00 short 0.0 5.8 5.554609072384922\n", "2019-09-25 14:31:00 long 0.0 -1.8 -1.5605127783747337\n", "2019-09-25 14:32:00 long 0.0 -3.0 -2.376008752724753\n", "2019-09-25 14:55:00 short 0.0 2.6 2.088556900684659\n", "2019-09-26 09:46:00 short 0.0 3.0 2.8147886505838007\n", "2019-09-26 09:47:00 short 0.0 3.6 3.3125615481131314\n", "2019-09-26 09:48:00 short 0.0 4.0 3.8584272840510305\n", "2019-09-26 10:23:00 short 0.0 3.0 2.83069495897579\n", "2019-09-26 10:24:00 short 0.0 3.6 3.2789686646590304\n", "2019-09-26 10:46:00 long 0.0 -2.6 -2.146625258399799\n", "2019-09-26 11:21:00 short 0.0 3.6 2.265894519393671\n", "2019-09-26 11:22:00 long 0.0 -3.2 -3.114454901417574\n", "2019-09-26 13:16:00 long 0.0 -2.6 -2.3635511135521776\n", "2019-09-26 13:18:00 long 0.0 -6.2 -3.9327043465311387\n", "2019-09-26 13:45:00 short 0.0 3.2 3.0822778379433275\n", "2019-09-26 14:05:00 short 0.0 3.8 3.35896866465903\n", "2019-09-26 14:10:00 short 0.0 6.0 4.530235101054467\n", "2019-09-26 14:27:00 long 0.0 -3.4 -3.05339846006256\n", "2019-09-27 09:32:00 long 0.0 -3.8 -2.9337158110217847\n", "2019-09-27 09:51:00 short 0.0 2.8 2.4214462120237066\n", "2019-09-27 10:12:00 short 0.0 2.0 1.850606004963329\n", "2019-09-27 10:26:00 long 0.0 -7.0 -5.503958456759911\n", "2019-09-27 10:27:00 long 0.0 -6.4 -6.098888751680797\n", "2019-09-27 10:28:00 long 0.0 -8.4 -7.236973076138914\n", "2019-09-27 11:07:00 short 0.0 1.6 1.5607200579252218\n", "2019-09-27 11:16:00 long 0.0 -4.0 -3.87369181623589\n", "2019-09-27 13:08:00 long 0.0 -4.6 -4.461162528784269\n", "2019-09-27 13:09:00 long 0.0 -5.2 -5.0132355138051565\n", "2019-09-27 13:23:00 short 0.0 2.0 1.6314853990842237\n", "2019-09-27 13:47:00 long 0.0 -5.4 -4.616894473211934\n", "2019-09-27 13:49:00 long 0.0 -5.8 -5.520191631233008\n", "2019-09-27 14:13:00 short 0.0 1.2 0.35121113065253606\n", "2019-09-27 14:59:00 long 0.0 -4.0 -3.4791101865742533\n", "2019-09-30 09:30:00 long 0.0 -4.8 -4.074602650883545\n", "2019-09-30 09:49:00 long 0.0 -5.8 -5.643996370232946\n", "2019-09-30 10:11:00 short 0.0 -0.8 -1.357039066464651\n", "2019-09-30 10:16:00 short 0.0 -0.4 -0.8150054945121012\n", "2019-09-30 11:17:00 long 0.0 -6.8 -5.732108506679619\n", "2019-09-30 11:18:00 long 0.0 -8.2 -6.755858900198849\n", "2019-09-30 13:18:00 long 0.0 -4.4 -4.306701454431009\n", "2019-09-30 13:28:00 short 0.0 0.2 -0.02641300566843663\n", "2019-09-30 13:47:00 short 0.0 0.4 0.13580821130503562\n", "2019-09-30 13:48:00 short 0.0 0.4 0.1881505419127727\n", "2019-09-30 13:49:00 short 0.0 1.2 0.7885480092693475\n", "2019-09-30 14:14:00 short 0.0 1.8 1.1255994033242787\n", "2019-09-30 14:39:00 short 0.0 1.6 1.4130639802964866\n", "2019-09-30 14:42:00 short 0.0 5.2 3.058708226511668\n", "2019-09-30 14:56:00 short 0.0 7.0 6.611856732445789\n", "2019-10-08 09:50:00 long 0.0 -0.8 -0.5042824394129783\n", "2019-10-08 10:09:00 short 0.0 4.8 4.579714271135537\n", "2019-10-08 10:15:00 long 0.0 -1.0 -0.9597653330201108\n", "2019-10-08 11:09:00 short 0.0 5.0 3.4925396619093014\n", "2019-10-08 13:06:00 short 0.0 4.8 4.353353817434332\n", "2019-10-08 13:31:00 long 0.0 0.6 0.8356836956703317\n", "2019-10-08 13:32:00 long 0.0 -0.6 0.23100027793220734\n", "2019-10-09 09:39:00 short 0.0 3.2 3.020829220946119\n", "2019-10-09 09:50:00 long 0.0 -1.8 -1.1859802954868088\n", "2019-10-09 10:11:00 short 0.0 2.0 1.9282856857085695\n", "2019-10-09 10:27:00 long 0.0 -0.8 -0.7578352176302475\n", "2019-10-09 10:38:00 long 0.0 -1.4 -1.0869847541280548\n", "2019-10-09 10:56:00 short 0.0 2.4 2.2773247934811574\n", "2019-10-09 11:07:00 short 0.0 2.6 2.554598307751914\n", "2019-10-09 13:00:00 long 0.0 -1.0 -0.23364776376578344\n", "2019-10-09 13:44:00 long 0.0 -0.6 -0.21029847422132164\n", "2019-10-09 13:45:00 long 0.0 -0.6 -0.4967498169838851\n", "2019-10-09 13:55:00 short 0.0 3.0 2.882291367302731\n", "2019-10-09 14:20:00 short 0.0 1.8 1.370468664200729\n", "2019-10-09 14:26:00 short 0.0 2.4 1.8891997865528134\n", "2019-10-09 14:30:00 short 0.0 2.4 2.2919553343687915\n", "2019-10-10 09:39:00 short 0.0 2.0 1.8236313686219439\n", "2019-10-10 09:42:00 long 0.0 -1.4 -1.3311813952440235\n", "2019-10-10 10:18:00 long 0.0 -2.0 -1.9735315156087414\n", "2019-10-10 10:48:00 short 0.0 1.8 1.7838540758722048\n", "2019-10-10 11:04:00 long 0.0 -1.4 -1.0303946855997617\n", "2019-10-10 11:06:00 long 0.0 -1.4 -1.3954486605993353\n", "2019-10-10 11:17:00 short 0.0 2.2 2.191730976517145\n", "2019-10-10 13:03:00 long 0.0 -1.2 -1.1912837027615355\n", "2019-10-10 13:09:00 short 0.0 2.4 2.2715186998758803\n", "2019-10-10 13:32:00 short 0.0 2.2 1.760985047329109\n", "2019-10-10 13:59:00 long 0.0 -0.4 -0.34148046676766297\n", "2019-10-10 14:23:00 long 0.0 -0.4 -0.33726848797845266\n", "2019-10-10 14:27:00 long 0.0 -0.8 -0.6881176406988356\n", "2019-10-10 14:28:00 short 0.0 2.0 1.761665591397402\n", "2019-10-10 14:52:00 short 0.0 3.2 3.1233732527773492\n", "2019-10-10 14:56:00 long 0.0 0.4 0.4697260530982481\n", "2019-10-11 09:35:00 short 0.0 4.4 4.158784701647182\n", "2019-10-11 10:01:00 short 0.0 4.6 4.567844366632624\n", "2019-10-11 10:06:00 long 0.0 1.0 1.8429751228517368\n", "2019-10-11 10:07:00 long 0.0 1.4 1.5653746606637924\n", "2019-10-11 10:55:00 long 0.0 0.2 0.3793872179038522\n", "2019-10-11 13:27:00 long 0.0 1.2 1.2129834579902017\n", "2019-10-11 13:28:00 long 0.0 0.4 0.8687937306404183\n", "2019-10-11 13:30:00 long 0.0 0.4 0.6264684843912596\n", "2019-10-11 13:31:00 long 0.0 0.2 0.34785878029351336\n", "2019-10-11 13:38:00 long 0.0 -0.8 -0.46008379692124746\n", "2019-10-11 14:03:00 short 0.0 3.6 3.3819159132617918\n", "2019-10-11 14:04:00 short 0.0 3.8 3.654584687293339\n", "2019-10-11 14:05:00 short 0.0 4.4 4.059545395833821\n", "2019-10-11 14:09:00 short 0.0 5.6 4.790176984220482\n", "2019-10-11 14:34:00 long 0.0 -0.6 0.673372549358854\n", "2019-10-11 14:46:00 long 0.0 0.0 0.07978418103918505\n", "2019-11-14 12:22:20.696701\t策略初始化完成\n", "2019-11-14 12:22:20.696701\t开始回放历史数据\n", "2019-10-14 09:30:00 short 0.0 6.6 5.2771740484728955\n", "2019-10-14 09:35:00 cover -10 2.6 2.9499999999999997\n", "2019-10-14 09:51:00 long 0 -2.0 -1.8301008630874136\n", "2019-10-14 10:01:00 short 0 2.4 2.207898896340853\n", "2019-10-14 10:03:00 cover -10 -0.2 0.19000000000000025\n", "2019-10-14 10:04:00 cover -10 -1.0 0.17000000000000023\n", "2019-10-14 10:05:00 cover -10 -0.6 0.13000000000000023\n", "2019-10-14 10:21:00 long 0 -2.4 -2.354293492332353\n", "2019-10-14 10:53:00 long 0 -3.8 -3.2656684389189676\n", "2019-10-14 11:02:00 long 0 -4.8 -3.983709532096884\n", "2019-10-14 13:20:00 short 0 1.2 1.1204329970525808\n", "2019-10-14 13:42:00 short 0 1.0 0.932520022699814\n", "2019-10-14 13:46:00 cover -10 -0.2 -0.0999999999999995\n", "2019-10-14 14:01:00 short 0 1.4 1.1046164741766535\n", "2019-10-14 14:08:00 cover -10 0.2 0.5799999999999997\n", "2019-10-14 14:12:00 short 0 4.8 3.112579847531794\n", "2019-10-14 14:19:00 cover -10 0.2 1.3899999999999997\n", "2019-10-14 14:40:00 long 0 -0.2 -0.1255433615645123\n", "2019-10-14 14:49:00 sell 10 1.8 0.8400000000000001\n", "2019-10-14 14:58:00 short 0 2.2 2.0102984742213215\n", "2019-10-15 09:30:00 cover -10 0.2 0.7899999999999998\n", "2019-10-15 09:31:00 cover -10 -0.6 0.7099999999999999\n", "2019-10-15 09:32:00 cover -10 -0.2 0.6699999999999999\n", "2019-10-15 09:33:00 cover -10 -0.4 0.6099999999999999\n", "2019-10-15 09:34:00 cover -10 -0.2 0.59\n", "2019-10-15 09:57:00 short 0 2.8 2.789199786552815\n", "2019-10-15 10:01:00 cover -10 0.6 1.629999999999999\n", "2019-10-15 10:05:00 long 0 -0.8 -0.27128370276154\n", "2019-10-15 10:06:00 sell 10 2.6 1.559999999999999\n", "2019-10-15 10:27:00 long 0 0.2 0.4168051036812388\n", "2019-10-15 10:29:00 sell 10 1.4 1.3899999999999992\n", "2019-10-15 10:40:00 short 0 2.4 2.18319489631876\n", "2019-10-15 10:47:00 cover -10 1.4 1.4199999999999984\n", "2019-10-15 11:01:00 long 0 -0.2 -0.03253295216666663\n", "2019-10-15 11:03:00 sell 10 1.8 1.3599999999999985\n", "2019-10-15 11:10:00 short 0 2.4 2.3864349160706313\n", "2019-10-15 11:12:00 cover -10 0.4 1.0999999999999985\n", "2019-10-15 11:18:00 long 0 -0.6 -0.5886235252742719\n", "2019-10-15 11:22:00 sell 10 1.0 0.929999999999999\n", "2019-10-15 13:20:00 long 0 0.2 0.21116886272900737\n", "2019-10-15 13:30:00 sell 10 0.8 0.7600000000000006\n", "2019-10-15 13:48:00 long 0 -0.8 -0.5990905331227268\n", "2019-10-15 13:53:00 sell 10 1.4 0.4400000000000002\n", "2019-10-15 14:13:00 short 0 1.4 1.3973488065725945\n", "2019-10-15 14:18:00 cover -10 0.6 0.6600000000000004\n", "2019-10-15 14:19:00 short 0 1.6 1.597348806572593\n", "2019-10-15 14:22:00 cover -10 0.8 0.9000000000000001\n", "2019-10-16 09:30:00 long 0 0.8 0.8607695154586696\n", "2019-10-16 09:44:00 sell 10 0.8 0.5100000000000003\n", "2019-10-16 09:55:00 short 0 2.2 1.818799519615618\n", "2019-10-16 10:02:00 cover -10 0.4 0.9200000000000003\n", "2019-10-16 10:07:00 short 0 3.0 2.8411253229445754\n", "2019-10-16 10:15:00 cover -10 1.6 1.6199999999999999\n", "2019-10-16 10:42:00 short 0 2.2 2.16\n", "2019-10-16 10:43:00 cover -10 0.6 1.2000000000000002\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2019-10-16 11:03:00 long 0 0.2 0.3184496572199613\n", "2019-10-16 11:04:00 sell 10 2.0 1.25\n", "2019-10-16 11:29:00 long 0 0.2 0.3624556746878138\n", "2019-10-16 13:03:00 sell 10 1.6 1.2700000000000005\n", "2019-10-16 13:31:00 short 0 4.2 3.535493654014335\n", "2019-10-16 13:35:00 cover -10 1.8 2.1399999999999997\n", "2019-10-16 13:52:00 short 0 3.2 2.898489166907174\n", "2019-10-16 13:53:00 cover -10 0.8 1.339999999999999\n", "2019-10-16 14:21:00 short 0 3.8 3.130897177907629\n", "2019-10-16 14:33:00 cover -10 2.8 2.8999999999999995\n", "2019-10-16 14:42:00 long 0 2.4 2.415591956721122\n", "2019-10-16 14:47:00 sell 10 3.4 3.35\n", "2019-10-16 14:55:00 short 0 4.4 4.259999999999998\n", "2019-10-16 14:59:00 cover -10 2.4 3.18\n", "2019-10-17 10:03:00 short 0 3.2 3.1397142273814165\n", "2019-10-17 10:17:00 cover -10 3.4 3.5700000000000003\n", "2019-10-17 10:23:00 short 0 5.6 5.342577965360736\n", "2019-10-17 10:27:00 cover -10 4.2 4.420000000000001\n", "2019-10-17 11:11:00 short 0 6.2 6.088117640698874\n", "2019-10-17 11:12:00 cover -10 4.6 4.97\n", "2019-10-17 13:25:00 long 0 4.4 4.578734401160298\n", "2019-10-17 13:26:00 sell 10 5.6 5.310000000000001\n", "2019-10-17 13:27:00 short 0 6.2 6.102269383052316\n", "2019-10-17 13:28:00 cover -10 5.0 5.290000000000001\n", "2019-10-17 13:31:00 long 0 4.0 4.26585116464987\n", "2019-10-17 13:35:00 sell 10 5.2 5.1400000000000015\n", "2019-10-17 13:58:00 short 0 6.0 5.978999374217504\n", "2019-10-17 14:04:00 cover -10 5.0 5.379999999999999\n", "2019-10-17 14:13:00 long 0 4.4 4.423853292240882\n", "2019-10-17 14:15:00 sell 10 5.6 5.459999999999998\n", "2019-10-17 14:21:00 short 0 7.2 6.941763562443941\n", "2019-10-17 14:32:00 cover -10 6.4 6.429999999999998\n", "2019-10-17 14:55:00 long 0 5.0 5.235787635563271\n", "2019-10-18 09:37:00 sell 10 5.4 5.0600000000000005\n", "2019-10-18 09:45:00 long 0 2.6 2.7647390801613145\n", "2019-10-18 09:48:00 sell 10 4.2 4.070000000000001\n", "2019-10-18 10:22:00 short 0 4.2 4.035759349937911\n", "2019-10-18 10:32:00 cover -10 3.0 3.280000000000001\n", "2019-10-18 10:42:00 long 0 2.6 2.6851380485015626\n", "2019-10-18 10:47:00 sell 10 4.0 3.3900000000000006\n", "2019-10-18 11:10:00 short 0 3.6 3.3906057825965146\n", "2019-10-18 11:16:00 cover -10 2.8 3.0300000000000016\n", "2019-10-18 11:22:00 short 0 4.4 4.277344760741067\n", "2019-10-18 11:29:00 cover -10 3.0 3.6999999999999984\n", "2019-10-18 13:17:00 long 0 3.2 3.298149205382897\n", "2019-10-18 13:20:00 sell 10 5.8 5.079999999999997\n", "2019-10-18 13:32:00 short 0 6.2 6.165884610812311\n", "2019-10-18 13:37:00 cover -10 4.0 4.7799999999999985\n", "2019-10-18 13:51:00 long 0 3.4 3.431323145391513\n", "2019-10-18 13:52:00 sell 10 5.4 4.94\n", "2019-10-18 14:54:00 long 0 3.4 3.4202041028867014\n", "2019-10-18 14:55:00 sell 10 4.8 4.459999999999999\n", "2019-10-18 14:59:00 short 0 6.0 5.68508215645556\n", "2019-10-21 09:33:00 cover -10 4.8 4.939999999999999\n", "2019-10-21 09:45:00 long 0 4.2 4.228357586546087\n", "2019-10-21 09:52:00 sell 10 6.0 5.489999999999998\n", "2019-10-21 10:06:00 long 0 4.0 4.077558866185662\n", "2019-10-21 10:07:00 sell 10 5.2 5.019999999999999\n", "2019-10-21 10:10:00 short 0 6.2 6.136709128650397\n", "2019-10-21 10:17:00 cover -10 4.6 5.279999999999999\n", "2019-10-21 11:14:00 long 0 4.8 4.848413696967212\n", "2019-10-21 11:15:00 sell 10 6.8 6.140000000000002\n", "2019-10-21 13:09:00 short 0 7.0 6.82111691323818\n", "2019-10-21 13:13:00 cover -10 5.4 6.0600000000000005\n", "2019-10-21 13:24:00 long 0 4.4 4.64626533375276\n", "2019-10-21 13:25:00 sell 10 6.0 5.940000000000002\n", "2019-10-21 14:07:00 long 0 4.2 4.267583000760229\n", "2019-10-21 14:09:00 sell 10 6.0 5.4399999999999995\n", "2019-10-21 14:12:00 short 0 6.6 6.4214804667676555\n", "2019-10-21 14:24:00 cover -10 5.4 5.61\n", "2019-10-21 14:29:00 long 0 4.6 4.810529576736241\n", "2019-10-21 14:32:00 sell 10 5.8 5.609999999999999\n", "2019-10-21 14:49:00 short 0 6.0 5.99409198185403\n", "2019-10-21 14:51:00 cover -10 4.8 5.32\n", "2019-10-21 14:58:00 long 0 4.6 4.657393131168045\n", "2019-10-21 14:59:00 sell 10 5.4 5.350000000000001\n", "2019-10-22 09:32:00 short 0 6.2 6.1212655988396385\n", "2019-10-22 09:34:00 cover -10 4.8 5.410000000000002\n", "2019-10-22 09:35:00 long 0 4.4 4.416813910195941\n", "2019-10-22 09:50:00 sell 10 4.8 4.540000000000001\n", "2019-10-22 10:06:00 short 0 6.0 5.452567014223348\n", "2019-10-22 10:09:00 cover -10 4.4 4.469999999999999\n", "2019-10-22 10:38:00 short 0 5.8 5.406583330077867\n", "2019-10-22 10:41:00 cover -10 4.2 4.35\n", "2019-10-22 10:44:00 long 0 3.2 3.2612615615380456\n", "2019-10-22 10:45:00 sell 10 5.4 4.45\n", "2019-10-22 11:23:00 short 0 6.8 6.297055636295015\n", "2019-10-22 11:26:00 cover -10 4.4 4.9\n", "2019-10-22 13:41:00 short 0 5.8 5.774491659829511\n", "2019-10-22 13:43:00 cover -10 4.4 5.139999999999998\n", "2019-10-22 13:46:00 long 0 4.2 4.257350013843572\n", "2019-10-22 13:48:00 sell 10 5.6 5.089999999999996\n", "2019-10-22 14:29:00 long 0 3.8 4.001111797830323\n", "2019-10-22 14:35:00 sell 10 4.8 4.359999999999999\n", "2019-10-22 14:54:00 long 0 3.2 3.324295126587999\n", "2019-10-22 14:57:00 sell 10 4.8 4.1899999999999995\n", "2019-10-23 10:18:00 long 0 3.2 3.2032686114644164\n", "2019-10-23 10:26:00 sell 10 5.2 3.650000000000001\n", "2019-10-23 11:00:00 short 0 5.2 4.87331262919987\n", "2019-10-23 11:01:00 cover -10 3.6 3.81\n", "2019-10-23 11:20:00 long 0 2.8 2.855450930786114\n", "2019-10-23 11:22:00 sell 10 4.2 3.79\n", "2019-10-23 13:14:00 long 0 2.8 2.9081601218103983\n", "2019-10-23 13:15:00 sell 10 4.0 3.7\n", "2019-10-23 13:19:00 short 0 4.8 4.717524104363061\n", "2019-10-23 13:23:00 cover -10 3.4 3.879999999999998\n", "2019-10-23 13:35:00 long 0 3.4 3.4466933337600034\n", "2019-10-23 13:36:00 sell 10 4.6 4.3199999999999985\n", "2019-10-23 13:48:00 long 0 3.0 3.112712164498057\n", "2019-10-23 13:50:00 sell 10 4.4 4.03\n", "2019-10-23 13:54:00 short 0 5.0 4.936794863550142\n", "2019-10-23 13:57:00 cover -10 3.4 3.990000000000003\n", "2019-10-23 14:00:00 long 0 2.6 2.706953086306418\n", "2019-10-23 14:05:00 sell 10 4.0 3.820000000000001\n", "2019-10-23 14:17:00 short 0 4.6 4.529516003089774\n", "2019-10-23 14:20:00 cover -10 3.2 3.689999999999999\n", "2019-10-23 14:59:00 short 0 6.6 5.788547536431275\n", "2019-10-24 09:30:00 cover -10 4.2 4.570000000000001\n", "2019-10-24 10:20:00 short 0 4.8 4.753229160308103\n", "2019-10-24 10:23:00 cover -10 4.0 4.019999999999998\n", "2019-10-24 10:25:00 short 0 5.6 5.199761877760891\n", "2019-10-24 10:27:00 cover -10 3.6 4.179999999999998\n", "2019-10-24 10:49:00 long 0 2.6 2.6131527499222917\n", "2019-10-24 10:52:00 sell 10 4.8 3.6300000000000017\n", "2019-10-24 11:22:00 short 0 4.4 4.346340792246414\n", "2019-10-24 11:24:00 cover -10 3.2 3.4599999999999995\n", "2019-10-24 11:26:00 long 0 2.0 2.34018183321061\n", "2019-10-24 11:27:00 sell 10 4.6 3.4899999999999998\n", "2019-10-24 13:02:00 short 0 4.8 4.799999999999999\n", "2019-10-24 13:07:00 cover -10 3.0 3.7900000000000005\n", "2019-10-24 13:51:00 long 0 2.8 2.924775479287132\n", "2019-10-24 13:52:00 sell 10 4.0 3.7799999999999985\n", "2019-10-25 09:34:00 short 0 4.2 4.025214259481649\n", "2019-10-25 09:37:00 cover -10 2.4 3.18\n", "2019-10-25 09:43:00 long 0 1.6 1.9701852010645435\n", "2019-10-25 09:46:00 sell 10 3.4 3.0599999999999987\n", "2019-10-25 10:00:00 long 0 1.4 1.4325484065344893\n", "2019-10-25 10:02:00 sell 10 2.8 2.4800000000000004\n", "2019-10-25 10:11:00 long 0 0.6 1.1208775307281607\n", "2019-10-25 10:14:00 sell 10 2.8 2.2500000000000004\n", "2019-10-25 10:21:00 short 0 3.8 3.6216655913974023\n", "2019-10-25 10:25:00 cover -10 2.4 2.4899999999999993\n", "2019-10-25 10:35:00 long 0 1.2 1.6445243830815617\n", "2019-10-25 10:37:00 sell 10 3.2 2.9399999999999995\n", "2019-10-25 10:40:00 long 0 1.4 1.454354696878596\n", "2019-10-25 10:47:00 sell 10 2.6 2.440000000000001\n", "2019-10-25 11:14:00 short 0 4.8 4.665156893466275\n", "2019-10-25 11:15:00 cover -10 3.0 3.4600000000000017\n", "2019-10-25 11:29:00 long 0 1.8 1.9660061349837625\n", "2019-10-25 13:11:00 sell 10 2.6 2.4800000000000018\n", "2019-10-25 13:19:00 short 0 3.6 3.356289923765868\n", "2019-10-25 13:21:00 cover -10 1.6 2.2200000000000024\n", "2019-10-25 13:29:00 short 0 3.8 3.7998148021117455\n", "2019-10-25 13:30:00 cover -10 2.2 2.490000000000001\n", "2019-10-25 13:32:00 short 0 4.2 4.054584687293329\n", "2019-10-25 13:37:00 cover -10 2.4 2.8700000000000014\n", "2019-10-25 14:06:00 short 0 5.0 4.652125257065119\n", "2019-10-25 14:08:00 cover -10 2.6 3.7699999999999982\n", "2019-10-25 14:59:00 long 0 0.2 0.8383344086026074\n", "2019-10-28 09:30:00 long 0 -0.4 0.30238264195914666\n", "2019-10-28 09:42:00 sell 10 0.8 0.6799999999999998\n", "2019-10-28 09:44:00 sell 10 0.8 0.5399999999999998\n", "2019-10-28 09:48:00 short 0 1.8 1.746617756902571\n", "2019-10-28 09:55:00 short 0 2.2 1.8681933110677402\n", "2019-10-28 10:00:00 cover -10 0.6 0.690000000000002\n", "2019-10-28 10:12:00 long 0 -1.0 -0.5116936997377782\n", "2019-10-28 10:14:00 sell 10 1.2 0.7000000000000026\n", "2019-10-28 10:38:00 short 0 3.4 2.7491067866830154\n", "2019-10-28 10:40:00 cover -10 1.6 1.7300000000000018\n", "2019-10-28 10:51:00 short 0 3.4 3.242919797992182\n", "2019-10-28 10:53:00 cover -10 1.2 1.940000000000002\n", "2019-10-28 11:25:00 short 0 3.8 3.6886656775464384\n", "2019-10-28 11:27:00 cover -10 2.0 2.770000000000001\n", "2019-10-28 13:06:00 long 0 1.2 1.354038835561576\n", "2019-10-28 13:08:00 sell 10 3.0 2.5400000000000014\n", "2019-10-28 13:39:00 long 0 0.8 0.8601818332105844\n", "2019-10-28 13:40:00 sell 10 2.6 1.9799999999999998\n", "2019-10-28 13:45:00 long 0 0.2 0.49866480257714874\n", "2019-10-28 13:48:00 sell 10 1.8 1.7499999999999996\n", "2019-10-28 13:51:00 short 0 3.4 3.3268980203246885\n", "2019-10-28 13:54:00 cover -10 1.8 1.97\n", "2019-10-28 14:22:00 short 0 2.8 2.648749021908633\n", "2019-10-28 14:23:00 cover -10 1.8 1.85\n", "2019-10-29 09:35:00 short 0 3.0 2.911665016500031\n", "2019-10-29 09:36:00 cover -10 1.0 2.02\n", "2019-10-29 09:55:00 short 0 3.0 2.974665715643837\n", "2019-10-29 09:58:00 cover -10 1.8 2.1199999999999997\n", "2019-10-29 10:15:00 short 0 3.2 3.1817374897442305\n", "2019-10-29 10:18:00 cover -10 2.2 2.2800000000000002\n", "2019-10-29 10:54:00 long 0 1.6 1.82678788880706\n", "2019-10-29 10:55:00 sell 10 3.0 2.6100000000000003\n", "2019-10-29 11:10:00 long 0 1.6 1.7152673405477727\n", "2019-10-29 11:14:00 sell 10 2.8 2.48\n", "2019-10-29 11:15:00 long 0 1.6 1.6152673405477709\n", "2019-10-29 11:16:00 sell 10 2.4 2.39\n", "2019-10-29 11:18:00 short 0 3.6 3.410425435640707\n", "2019-10-29 11:20:00 cover -10 2.2 2.45\n", "2019-10-29 13:30:00 short 0 3.8 3.65301697684107\n", "2019-10-29 13:35:00 cover -10 2.8 2.809999999999999\n", "2019-10-29 14:11:00 long 0 1.4 1.5328206644528608\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2019-10-29 14:14:00 sell 10 2.8 2.3899999999999997\n", "2019-10-29 14:40:00 short 0 4.0 3.599719056781405\n", "2019-10-29 14:42:00 cover -10 2.6 2.7100000000000004\n", "2019-10-30 09:32:00 long 0 1.4 1.702651193427421\n", "2019-10-30 09:33:00 sell 10 2.6 2.5500000000000007\n", "2019-10-30 09:40:00 short 0 3.4 3.2675848040166766\n", "2019-10-30 09:50:00 cover -10 2.8 2.8599999999999994\n", "2019-10-30 10:05:00 long 0 2.2 2.233116507008849\n", "2019-10-30 10:06:00 sell 10 3.4 3.1299999999999994\n", "2019-10-30 10:15:00 short 0 4.4 3.9870165420097914\n", "2019-10-30 10:19:00 cover -10 2.8 3.09\n", "2019-10-30 10:31:00 short 0 5.4 4.915457117137707\n", "2019-10-30 10:41:00 cover -10 4.0 4.34\n", "2019-10-30 10:52:00 long 0 3.2 3.2019246599640794\n", "2019-10-30 11:08:00 sell 10 4.2 3.2299999999999995\n", "2019-10-30 11:24:00 short 0 4.6 4.327174264961146\n", "2019-10-30 11:25:00 cover -10 3.2 3.3300000000000005\n", "2019-10-30 11:26:00 short 0 4.6 4.529233969908972\n", "2019-10-30 13:00:00 cover -10 2.8 3.5199999999999996\n", "2019-10-30 13:15:00 short 0 4.8 4.5817661014573865\n", "2019-10-30 13:27:00 cover -10 3.6 3.8800000000000012\n", "2019-10-30 13:40:00 short 0 5.4 5.023194896318707\n", "2019-10-30 13:47:00 cover -10 4.0 4.040000000000001\n", "2019-10-30 14:46:00 short 0 5.4 5.3579189152169375\n", "2019-10-30 14:55:00 cover -10 4.4 4.7600000000000025\n", "2019-10-31 10:54:00 short 0 4.8 4.619647799968375\n", "2019-10-31 10:55:00 cover -10 3.6 3.879999999999999\n", "2019-10-31 11:14:00 short 0 5.4 5.07833188405687\n", "2019-10-31 11:18:00 cover -10 4.2 4.24\n", "2019-10-31 13:23:00 long 0 2.8 2.8267878888070865\n", "2019-10-31 13:24:00 sell 10 4.0 3.59\n", "2019-10-31 13:35:00 short 0 4.4 4.371665016500027\n", "2019-10-31 13:37:00 cover -10 3.2 3.5199999999999996\n", "2019-10-31 13:46:00 short 0 4.8 4.632419662491447\n", "2019-10-31 13:49:00 cover -10 3.6 3.7600000000000002\n", "2019-10-31 14:31:00 long 0 2.4 2.5361632823094\n", "2019-10-31 14:33:00 sell 10 3.6 3.2800000000000002\n", "2019-10-31 14:39:00 short 0 4.4 4.236175168483585\n", "2019-10-31 14:48:00 cover -10 3.4 3.430000000000002\n", "2019-10-31 14:59:00 short 0 5.2 4.890464990571925\n", "2019-11-01 09:30:00 cover -10 3.0 3.950000000000003\n", "2019-11-01 09:39:00 short 0 5.4 5.271586303032787\n", "2019-11-01 09:40:00 cover -10 3.6 4.000000000000003\n", "2019-11-01 10:08:00 short 0 5.4 5.205917366429879\n", "2019-11-01 10:14:00 cover -10 3.6 4.0600000000000005\n", "2019-11-01 10:15:00 long 0 2.2 2.5373800224763086\n", "2019-11-01 10:17:00 sell 10 4.2 3.9799999999999995\n", "2019-11-01 11:14:00 short 0 4.6 4.574601809837338\n", "2019-11-01 11:16:00 cover -10 3.8 3.8099999999999987\n", "2019-11-01 11:20:00 long 0 3.0 3.0100632936747744\n", "2019-11-01 11:21:00 sell 10 4.0 3.79\n", "2019-11-01 11:23:00 long 0 2.4 2.7675803375085684\n", "2019-11-01 11:24:00 sell 10 4.0 3.7600000000000002\n", "2019-11-01 13:12:00 long 0 1.4 1.876497489474039\n", "2019-11-01 13:31:00 sell 10 2.6 1.7000000000000004\n", "2019-11-01 13:53:00 short 0 2.8 2.799019136228547\n", "2019-11-01 13:57:00 cover -10 1.6 1.8200000000000014\n", "2019-11-01 14:13:00 short 0 3.4 3.1813199329137047\n", "2019-11-01 14:16:00 cover -10 1.4 2.080000000000003\n", "2019-11-01 14:33:00 long 0 1.2 1.4018306305724892\n", "2019-11-01 14:34:00 sell 10 3.2 2.6800000000000015\n", "2019-11-01 14:53:00 long 0 0.8 0.8080410967167102\n", "2019-11-01 14:54:00 sell 10 3.2 1.8300000000000014\n", "2019-11-04 09:31:00 long 0 0.6 0.600210102581137\n", "2019-11-04 09:32:00 sell 10 2.6 1.840000000000001\n", "2019-11-04 09:49:00 short 0 3.4 3.2633137253205806\n", "2019-11-04 09:52:00 cover -10 2.2 2.21\n", "2019-11-04 10:38:00 short 0 3.4 3.253984924527946\n", "2019-11-04 10:39:00 cover -10 2.0 2.01\n", "2019-11-04 10:53:00 short 0 3.4 3.340578771065301\n", "2019-11-04 11:02:00 cover -10 2.4 2.7600000000000007\n", "2019-11-04 11:09:00 long 0 1.8 1.8158994132730166\n", "2019-11-04 11:12:00 sell 10 3.0 2.8200000000000003\n", "2019-11-04 11:28:00 short 0 4.0 3.959444312483505\n", "2019-11-04 11:29:00 cover -10 2.6 2.83\n", "2019-11-04 13:03:00 short 0 4.2 4.108447236605963\n", "2019-11-04 13:04:00 cover -10 2.8 2.9500000000000006\n", "2019-11-04 13:23:00 long 0 2.0 2.037445653886075\n", "2019-11-04 13:35:00 sell 10 2.4 2.33\n", "2019-11-04 13:39:00 short 0 3.0 2.864142842854293\n", "2019-11-04 13:40:00 cover -10 1.8 2.1099999999999994\n", "2019-11-04 13:43:00 short 0 3.0 2.9727452896155904\n", "2019-11-04 13:45:00 cover -10 1.8 2.18\n", "2019-11-04 13:51:00 short 0 3.6 3.2950862032359645\n", "2019-11-04 13:55:00 cover -10 2.4 2.46\n", "2019-11-04 14:22:00 long 0 2.2 2.287440098139454\n", "2019-11-04 14:30:00 sell 10 2.8 2.7899999999999996\n", "2019-11-04 14:38:00 long 0 1.6 1.6808003202562465\n", "2019-11-04 14:39:00 sell 10 2.8 2.669999999999999\n", "2019-11-04 14:59:00 long 0 2.0 2.0013525874952394\n", "2019-11-05 09:32:00 sell 10 2.8 2.71\n", "2019-11-05 09:59:00 long 0 1.2 1.210063293674752\n", "2019-11-05 10:06:00 sell 10 1.8 1.75\n", "2019-11-05 10:07:00 short 0 2.6 2.5889499366462543\n", "2019-11-05 10:10:00 cover -10 1.4 1.73\n", "2019-11-05 10:15:00 short 0 2.8 2.7172299611087896\n", "2019-11-05 10:17:00 cover -10 1.6 1.7899999999999998\n", "2019-11-05 10:30:00 short 0 2.8 2.7924354077980764\n", "2019-11-05 10:32:00 cover -10 1.0 1.9300000000000002\n", "2019-11-05 11:23:00 long 0 0.0 0.4999999999999971\n", "2019-11-05 11:26:00 sell 10 2.0 1.6799999999999997\n", "2019-11-05 14:01:00 short 0 1.2 1.1484355879804886\n", "2019-11-05 14:02:00 long 0 -0.6 -0.3055342858886768\n", "2019-11-05 14:03:00 sell 10 0.6 0.43\n", "2019-11-05 14:12:00 short 0 1.4 1.3687616170334773\n", "2019-11-05 14:13:00 cover -10 0.2 0.3399999999999999\n", "2019-11-05 14:26:00 short 0 1.6 1.4303772913449242\n", "2019-11-05 14:27:00 cover -10 0.4 0.40999999999999936\n", "2019-11-06 10:02:00 short 0 1.0 0.9707402333838302\n", "2019-11-06 10:03:00 long 0 -0.2 -0.19827329179203557\n", "2019-11-06 10:04:00 sell 10 0.8 0.39000000000000135\n", "2019-11-06 10:06:00 sell 10 1.0 0.39000000000000135\n", "2019-11-06 10:26:00 short 0 1.8 1.5147184905645277\n", "2019-11-06 10:34:00 cover -10 0.8 1.110000000000001\n", "2019-11-06 10:59:00 long 0 -0.4 0.05614169408481229\n", "2019-11-06 11:01:00 sell 10 1.4 0.9999999999999998\n", "2019-11-06 11:14:00 short 0 2.2 2.1251568934662934\n", "2019-11-06 11:16:00 cover -10 0.8 0.8999999999999997\n", "2019-11-06 13:06:00 long 0 0.0 0.11926861885267015\n", "2019-11-06 13:08:00 sell 10 1.0 0.8899999999999993\n", "2019-11-06 13:48:00 short 0 2.2 2.095086203235964\n", "2019-11-06 13:50:00 cover -10 0.6 1.03\n", "2019-11-06 14:23:00 short 0 2.2 2.1000000000000023\n", "2019-11-06 14:28:00 cover -10 0.8 1.2399999999999998\n", "2019-11-06 14:53:00 short 0 2.8 2.7034813257969215\n", "2019-11-06 14:54:00 cover -10 1.8 1.8199999999999992\n", "2019-11-06 14:55:00 short 0 2.8 2.787016542009806\n", "2019-11-07 09:31:00 cover -10 2.0 2.0699999999999994\n", "2019-11-07 09:59:00 long 0 0.4 0.4718016229225689\n", "2019-11-07 10:02:00 sell 10 1.6 1.5199999999999991\n", "2019-11-07 10:17:00 long 0 0.4 0.4081601218103623\n", "2019-11-07 10:18:00 sell 10 1.6 1.1800000000000002\n", "2019-11-07 10:19:00 long 0 0.4 0.4012831066427295\n", "2019-11-07 10:26:00 sell 10 1.2 0.89\n", "2019-11-07 10:27:00 short 0 2.2 2.106876171665185\n", "2019-11-07 10:30:00 cover -10 0.4 0.9899999999999997\n", "2019-11-07 10:57:00 long 0 0.4 0.4131527499222649\n", "2019-11-07 11:07:00 sell 10 1.6 1.1799999999999997\n", "2019-11-07 11:17:00 short 0 2.2 1.909799979995996\n", "2019-11-07 11:18:00 cover -10 0.8 0.8800000000000002\n", "2019-11-07 13:09:00 short 0 2.4 2.1073821775254546\n", "2019-11-07 13:12:00 cover -10 1.0 1.1899999999999993\n", "2019-11-07 13:59:00 short 0 2.6 2.2587026647662305\n", "2019-11-07 14:01:00 cover -10 1.0 1.5100000000000002\n", "2019-11-07 14:23:00 short 0 2.6 2.472745289615583\n", "2019-11-07 14:24:00 cover -10 1.4 1.6599999999999997\n", "2019-11-07 14:37:00 long 0 0.6 0.7093184457478421\n", "2019-11-07 14:43:00 sell 10 1.6 1.3200000000000007\n", "2019-11-07 14:49:00 short 0 2.0 1.9433923280734922\n", "2019-11-07 14:54:00 cover -10 0.8 1.200000000000001\n", "2019-11-07 14:55:00 short 0 2.2 2.1610433579144246\n", "2019-11-08 09:30:00 cover -10 1.0 1.460000000000001\n", "2019-11-08 10:16:00 long 0 0.8 0.8150185187725605\n", "2019-11-08 10:17:00 sell 10 2.2 1.520000000000001\n", "2019-11-08 10:30:00 long 0 0.2 0.3917025250471836\n", "2019-11-08 10:31:00 sell 10 2.4 1.41\n", "2019-11-08 10:51:00 short 0 2.0 1.9047326594522187\n", "2019-11-08 10:55:00 cover -10 1.2 1.2900000000000003\n", "2019-11-08 13:00:00 long 0 0.2 0.4043913836341373\n", "2019-11-08 13:03:00 sell 10 1.8 1.3600000000000003\n", "2019-11-08 13:30:00 short 0 2.2 2.0201515035589255\n", "2019-11-08 13:32:00 cover -10 1.0 1.0999999999999999\n", "2019-11-08 13:44:00 long 0 -0.4 0.0007939009042223777\n", "2019-11-08 13:45:00 sell 10 1.4 1.2899999999999998\n", "2019-11-08 14:29:00 long 0 0.8 0.8201695036997836\n", "2019-11-08 14:30:00 sell 10 2.2 1.9799999999999998\n", "2019-11-08 14:33:00 sell 10 1.8 1.6599999999999997\n", "2019-11-08 14:52:00 long 0 0.8 0.8895455886296623\n", "2019-11-08 14:53:00 sell 10 2.0 1.6699999999999995\n", "2019-11-14 12:22:21.510177\t历史数据回放结束\n", "2019-11-14 12:22:21.510177\t开始计算逐日盯市盈亏\n", "2019-11-14 12:22:21.510177\t逐日盯市盈亏计算完成\n", "2019-11-14 12:22:21.510177\t开始计算策略统计指标\n", "2019-11-14 12:22:21.536544\t------------------------------\n", "2019-11-14 12:22:21.536544\t首个交易日：\t2019-10-14\n", "2019-11-14 12:22:21.536544\t最后交易日：\t2019-11-08\n", "2019-11-14 12:22:21.536544\t总交易日：\t20\n", "2019-11-14 12:22:21.536544\t盈利交易日：\t12\n", "2019-11-14 12:22:21.536544\t亏损交易日：\t8\n", "2019-11-14 12:22:21.536544\t起始资金：\t1,000,000.00\n", "2019-11-14 12:22:21.536544\t结束资金：\t1,065,400.00\n", "2019-11-14 12:22:21.536544\t总收益率：\t6.54%\n", "2019-11-14 12:22:21.536544\t年化收益：\t78.48%\n", "2019-11-14 12:22:21.536544\t最大回撤: \t-24,600.00\n", "2019-11-14 12:22:21.536544\t百分比最大回撤: -2.31%\n", "2019-11-14 12:22:21.536544\t最长回撤天数: \t4\n", "2019-11-14 12:22:21.536544\t总盈亏：\t65,400.00\n", "2019-11-14 12:22:21.536544\t总手续费：\t0.00\n", "2019-11-14 12:22:21.536544\t总滑点：\t0.00\n", "2019-11-14 12:22:21.536544\t总成交金额：\t3,531,000.00\n", "2019-11-14 12:22:21.536544\t总成交笔数：\t406\n", "2019-11-14 12:22:21.536544\t日均盈亏：\t3,270.00\n", "2019-11-14 12:22:21.536544\t日均手续费：\t0.00\n", "2019-11-14 12:22:21.537521\t日均滑点：\t0.00\n", "2019-11-14 12:22:21.537521\t日均成交金额：\t176,550.00\n", "2019-11-14 12:22:21.537521\t日均成交笔数：\t20.3\n", "2019-11-14 12:22:21.537521\t日均收益率：\t0.26%\n", "2019-11-14 12:22:21.537521\t收益标准差：\t0.76%\n", "2019-11-14 12:22:21.537521\tSharpe Ratio：\t5.29\n", "2019-11-14 12:22:21.537521\t收益回撤比：\t2.83\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Github\\vnpy\\vnpy\\app\\spread_trading\\backtesting.py:286: FutureWarning: \n", "The current behaviour of 'Series.argmax' is deprecated, use 'idxmax'\n", "instead.\n", "The behavior of 'argmax' will be corrected to return the positional\n", "maximum in the future. For now, use 'series.values.argmax' or\n", "'np.argmax(np.array(values))' to get the position of the maximum\n", "row.\n", "  max_drawdown_start = df[\"balance\"][:max_drawdown_end].argmax()\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x1152 with 4 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["#%%\n", "engine.load_data()\n", "engine.run_backtesting()\n", "df = engine.calculate_result()\n", "engine.calculate_statistics()\n", "engine.show_chart()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='1', tradeid='1', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=5.0, volume=10, time='09:31:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='2', tradeid='2', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.0, volume=10, time='09:36:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='4', tradeid='3', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=0.2, volume=10, time='10:02:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='7', tradeid='4', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.2, volume=10, time='10:06:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='12', tradeid='5', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=0.4, volume=10, time='13:43:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='13', tradeid='6', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.6, volume=10, time='13:47:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='14', tradeid='7', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.6, volume=10, time='14:02:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='15', tradeid='8', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.6, volume=10, time='14:09:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='16', tradeid='9', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.2, volume=10, time='14:13:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='17', tradeid='10', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.0, volume=10, time='14:20:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='18', tradeid='11', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.0, volume=10, time='14:41:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='19', tradeid='12', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=0.8, volume=10, time='14:50:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='20', tradeid='13', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.0, volume=10, time='14:59:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='25', tradeid='14', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.2, volume=10, time='09:35:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='26', tradeid='15', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='09:58:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='27', tradeid='16', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.0, volume=10, time='10:02:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='28', tradeid='17', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='10:06:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='29', tradeid='18', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.2, volume=10, time='10:07:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='30', tradeid='19', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.8, volume=10, time='10:28:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='31', tradeid='20', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.8, volume=10, time='10:30:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='32', tradeid='21', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.2, volume=10, time='10:41:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='33', tradeid='22', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='10:48:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='34', tradeid='23', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.4, volume=10, time='11:02:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='35', tradeid='24', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.2, volume=10, time='11:04:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='36', tradeid='25', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.4, volume=10, time='11:11:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='37', tradeid='26', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.4, volume=10, time='11:13:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='38', tradeid='27', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.4, volume=10, time='11:19:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='39', tradeid='28', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.2, volume=10, time='11:23:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='40', tradeid='29', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.8, volume=10, time='13:21:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='41', tradeid='30', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.4, volume=10, time='13:31:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='42', tradeid='31', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.2, volume=10, time='13:49:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='43', tradeid='32', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=0.6, volume=10, time='13:54:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='44', tradeid='33', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=0.8, volume=10, time='14:14:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='45', tradeid='34', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.6, volume=10, time='14:19:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='46', tradeid='35', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.2, volume=10, time='14:20:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='47', tradeid='36', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.2, volume=10, time='14:23:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='48', tradeid='37', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.8, volume=10, time='09:31:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='49', tradeid='38', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=0.8, volume=10, time='09:45:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='50', tradeid='39', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.8, volume=10, time='09:56:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='51', tradeid='40', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.2, volume=10, time='10:03:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='52', tradeid='41', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='10:08:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='53', tradeid='42', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.8, volume=10, time='10:16:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='54', tradeid='43', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=0.6, volume=10, time='10:43:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='55', tradeid='44', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.8, volume=10, time='10:44:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='56', tradeid='45', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.0, volume=10, time='11:04:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='57', tradeid='46', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.2, volume=10, time='11:05:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='58', tradeid='47', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.6, volume=10, time='13:00:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='59', tradeid='48', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.4, volume=10, time='13:04:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='60', tradeid='49', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='13:32:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='61', tradeid='50', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.8, volume=10, time='13:36:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='62', tradeid='51', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=0.8, volume=10, time='13:53:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='63', tradeid='52', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.4, volume=10, time='13:54:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='64', tradeid='53', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='14:22:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='65', tradeid='54', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.4, volume=10, time='14:34:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='66', tradeid='55', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.0, volume=10, time='14:43:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='67', tradeid='56', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='14:48:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='68', tradeid='57', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='14:56:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='69', tradeid='58', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='09:30:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='70', tradeid='59', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.2, volume=10, time='10:04:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='71', tradeid='60', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.0, volume=10, time='10:18:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='72', tradeid='61', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=5.0, volume=10, time='10:24:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='73', tradeid='62', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=5.2, volume=10, time='10:28:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='74', tradeid='63', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.6, volume=10, time='11:12:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='75', tradeid='64', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=5.6, volume=10, time='11:13:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='76', tradeid='65', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=5.6, volume=10, time='13:26:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='77', tradeid='66', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=6.2, volume=10, time='13:27:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='78', tradeid='67', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=5.0, volume=10, time='13:28:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='79', tradeid='68', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.8, volume=10, time='13:29:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='80', tradeid='69', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='13:32:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='81', tradeid='70', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=5.6, volume=10, time='13:36:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='82', tradeid='71', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=5.8, volume=10, time='13:59:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='83', tradeid='72', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=6.0, volume=10, time='14:05:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='84', tradeid='73', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.6, volume=10, time='14:14:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='85', tradeid='74', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=5.0, volume=10, time='14:16:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='86', tradeid='75', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=7.0, volume=10, time='14:22:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='87', tradeid='76', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=6.6, volume=10, time='14:33:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='88', tradeid='77', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=5.6, volume=10, time='14:56:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='89', tradeid='78', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.0, volume=10, time='09:38:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='90', tradeid='79', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.8, volume=10, time='09:46:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='91', tradeid='80', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='09:49:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='92', tradeid='81', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.4, volume=10, time='10:23:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='93', tradeid='82', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.8, volume=10, time='10:33:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='94', tradeid='83', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='10:43:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='95', tradeid='84', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.0, volume=10, time='10:48:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='96', tradeid='85', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.4, volume=10, time='11:11:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='97', tradeid='86', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.4, volume=10, time='11:17:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='98', tradeid='87', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.0, volume=10, time='11:23:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='99', tradeid='88', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=5.2, volume=10, time='13:00:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='100', tradeid='89', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.6, volume=10, time='13:18:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='101', tradeid='90', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=5.0, volume=10, time='13:21:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='102', tradeid='91', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=6.0, volume=10, time='13:33:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='103', tradeid='92', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=5.8, volume=10, time='13:38:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='104', tradeid='93', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=5.4, volume=10, time='13:52:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='105', tradeid='94', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.8, volume=10, time='13:53:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='106', tradeid='95', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.8, volume=10, time='14:55:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='107', tradeid='96', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='14:56:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='108', tradeid='97', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=6.6, volume=10, time='09:30:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='109', tradeid='98', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=5.8, volume=10, time='09:34:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='110', tradeid='99', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.8, volume=10, time='09:46:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='111', tradeid='100', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=5.6, volume=10, time='09:53:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='112', tradeid='101', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=5.2, volume=10, time='10:07:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='113', tradeid='102', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=5.0, volume=10, time='10:08:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='114', tradeid='103', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=6.4, volume=10, time='10:11:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='115', tradeid='104', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=5.8, volume=10, time='10:18:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='116', tradeid='105', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=6.8, volume=10, time='11:15:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='117', tradeid='106', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=5.8, volume=10, time='11:16:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='118', tradeid='107', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=6.4, volume=10, time='13:10:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='119', tradeid='108', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=5.6, volume=10, time='13:14:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='120', tradeid='109', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=6.0, volume=10, time='13:25:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='121', tradeid='110', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=6.6, volume=10, time='13:26:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='122', tradeid='111', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.8, volume=10, time='14:08:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='123', tradeid='112', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=5.2, volume=10, time='14:10:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='124', tradeid='113', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=5.6, volume=10, time='14:13:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='125', tradeid='114', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=5.6, volume=10, time='14:25:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='126', tradeid='115', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=5.2, volume=10, time='14:30:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='127', tradeid='116', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.8, volume=10, time='14:33:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='128', tradeid='117', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=5.6, volume=10, time='14:50:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='129', tradeid='118', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=5.6, volume=10, time='14:52:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='130', tradeid='119', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=5.4, volume=10, time='14:59:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='131', tradeid='120', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=5.2, volume=10, time='09:30:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='132', tradeid='121', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=6.2, volume=10, time='09:33:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='133', tradeid='122', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.4, volume=10, time='09:35:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='134', tradeid='123', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.6, volume=10, time='09:36:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='135', tradeid='124', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.6, volume=10, time='09:51:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='136', tradeid='125', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.8, volume=10, time='10:07:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='137', tradeid='126', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='10:10:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='138', tradeid='127', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.4, volume=10, time='10:39:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='139', tradeid='128', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.4, volume=10, time='10:42:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='140', tradeid='129', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=5.4, volume=10, time='10:45:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='141', tradeid='130', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.4, volume=10, time='10:46:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='142', tradeid='131', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=5.0, volume=10, time='11:24:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='143', tradeid='132', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=5.2, volume=10, time='11:27:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='144', tradeid='133', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=5.2, volume=10, time='13:42:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='145', tradeid='134', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.8, volume=10, time='13:44:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='146', tradeid='135', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.8, volume=10, time='13:47:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='147', tradeid='136', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.8, volume=10, time='13:49:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='148', tradeid='137', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.2, volume=10, time='14:30:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='149', tradeid='138', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.6, volume=10, time='14:36:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='150', tradeid='139', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.4, volume=10, time='14:55:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='151', tradeid='140', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='14:58:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='152', tradeid='141', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.2, volume=10, time='10:19:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='153', tradeid='142', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='10:27:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='154', tradeid='143', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='11:01:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='155', tradeid='144', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='11:02:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='156', tradeid='145', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.4, volume=10, time='11:21:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='157', tradeid='146', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.0, volume=10, time='11:23:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='158', tradeid='147', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.0, volume=10, time='13:15:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='159', tradeid='148', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.8, volume=10, time='13:16:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='160', tradeid='149', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.4, volume=10, time='13:20:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='161', tradeid='150', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.8, volume=10, time='13:24:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='162', tradeid='151', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.6, volume=10, time='13:36:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='163', tradeid='152', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.4, volume=10, time='13:37:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='164', tradeid='153', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='13:49:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='165', tradeid='154', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.4, volume=10, time='13:51:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='166', tradeid='155', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=5.0, volume=10, time='13:55:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='167', tradeid='156', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='13:58:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='168', tradeid='157', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.4, volume=10, time='14:01:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='169', tradeid='158', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.4, volume=10, time='14:06:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='170', tradeid='159', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='14:18:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='171', tradeid='160', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='14:21:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='172', tradeid='161', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='09:30:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='173', tradeid='162', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.4, volume=10, time='09:31:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='174', tradeid='163', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.8, volume=10, time='10:21:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='175', tradeid='164', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.0, volume=10, time='10:24:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='176', tradeid='165', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.6, volume=10, time='10:26:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='177', tradeid='166', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='10:28:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='178', tradeid='167', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='10:50:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='179', tradeid='168', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.4, volume=10, time='10:53:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='180', tradeid='169', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='11:23:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='181', tradeid='170', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='11:25:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='182', tradeid='171', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.6, volume=10, time='11:27:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='183', tradeid='172', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.8, volume=10, time='11:28:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='184', tradeid='173', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.8, volume=10, time='13:03:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='185', tradeid='174', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.4, volume=10, time='13:08:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='186', tradeid='175', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.0, volume=10, time='13:52:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='187', tradeid='176', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.0, volume=10, time='13:53:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='188', tradeid='177', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='09:35:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='189', tradeid='178', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='09:38:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='190', tradeid='179', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='09:44:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='191', tradeid='180', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='09:47:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='192', tradeid='181', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.2, volume=10, time='10:01:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='193', tradeid='182', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.0, volume=10, time='10:03:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='194', tradeid='183', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.0, volume=10, time='10:12:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='195', tradeid='184', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.6, volume=10, time='10:15:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='196', tradeid='185', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.4, volume=10, time='10:22:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='197', tradeid='186', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='10:26:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='198', tradeid='187', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.4, volume=10, time='10:36:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='199', tradeid='188', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.2, volume=10, time='10:38:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='200', tradeid='189', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.4, volume=10, time='10:41:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='201', tradeid='190', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.8, volume=10, time='10:48:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='202', tradeid='191', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.0, volume=10, time='11:15:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='203', tradeid='192', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.2, volume=10, time='11:16:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='204', tradeid='193', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='13:00:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='205', tradeid='194', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='13:12:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='206', tradeid='195', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.4, volume=10, time='13:20:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='207', tradeid='196', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.0, volume=10, time='13:22:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='208', tradeid='197', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.2, volume=10, time='13:30:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='209', tradeid='198', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='13:31:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='210', tradeid='199', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='13:33:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='211', tradeid='200', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='13:38:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='212', tradeid='201', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=5.2, volume=10, time='14:07:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='213', tradeid='202', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='14:09:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='215', tradeid='203', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.8, volume=10, time='09:31:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='217', tradeid='204', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=0.2, volume=10, time='09:45:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='219', tradeid='205', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.0, volume=10, time='09:56:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='220', tradeid='206', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.6, volume=10, time='10:01:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='221', tradeid='207', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.4, volume=10, time='10:13:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='222', tradeid='208', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.2, volume=10, time='10:15:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='223', tradeid='209', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.0, volume=10, time='10:39:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='224', tradeid='210', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.0, volume=10, time='10:41:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='225', tradeid='211', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='10:52:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='226', tradeid='212', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.6, volume=10, time='10:54:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='227', tradeid='213', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.0, volume=10, time='11:26:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='228', tradeid='214', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.2, volume=10, time='11:28:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='229', tradeid='215', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.4, volume=10, time='13:07:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='230', tradeid='216', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='13:09:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='231', tradeid='217', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='13:40:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='232', tradeid='218', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.6, volume=10, time='13:41:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='233', tradeid='219', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.4, volume=10, time='13:46:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='234', tradeid='220', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='13:49:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='235', tradeid='221', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.0, volume=10, time='13:52:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='236', tradeid='222', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.4, volume=10, time='13:55:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='237', tradeid='223', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.8, volume=10, time='14:23:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='238', tradeid='224', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='14:24:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='239', tradeid='225', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.0, volume=10, time='09:36:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='240', tradeid='226', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.2, volume=10, time='09:37:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='241', tradeid='227', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.2, volume=10, time='09:56:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='242', tradeid='228', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.8, volume=10, time='09:59:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='243', tradeid='229', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.4, volume=10, time='10:16:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='244', tradeid='230', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='10:19:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='245', tradeid='231', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.0, volume=10, time='10:55:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='246', tradeid='232', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='10:56:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='247', tradeid='233', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.0, volume=10, time='11:11:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='248', tradeid='234', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.6, volume=10, time='11:15:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='249', tradeid='235', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.4, volume=10, time='11:16:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='250', tradeid='236', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='11:17:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='251', tradeid='237', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='11:19:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='252', tradeid='238', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.0, volume=10, time='11:21:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='253', tradeid='239', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='13:31:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='254', tradeid='240', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.0, volume=10, time='13:36:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='255', tradeid='241', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.4, volume=10, time='14:12:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='256', tradeid='242', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.0, volume=10, time='14:15:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='257', tradeid='243', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='14:41:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='258', tradeid='244', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='14:43:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='259', tradeid='245', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='09:33:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='260', tradeid='246', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.0, volume=10, time='09:34:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='261', tradeid='247', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.8, volume=10, time='09:41:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='262', tradeid='248', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='09:51:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='263', tradeid='249', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.4, volume=10, time='10:06:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='264', tradeid='250', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='10:07:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='265', tradeid='251', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='10:16:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='266', tradeid='252', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.2, volume=10, time='10:20:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='267', tradeid='253', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.6, volume=10, time='10:32:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='268', tradeid='254', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='10:42:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='269', tradeid='255', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.4, volume=10, time='10:53:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='270', tradeid='256', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='11:09:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='271', tradeid='257', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.2, volume=10, time='11:25:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='272', tradeid='258', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.6, volume=10, time='11:26:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='273', tradeid='259', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.8, volume=10, time='11:27:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='274', tradeid='260', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.4, volume=10, time='13:01:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='275', tradeid='261', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.0, volume=10, time='13:16:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='276', tradeid='262', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='13:28:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='277', tradeid='263', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='13:41:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='278', tradeid='264', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='13:48:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='279', tradeid='265', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.6, volume=10, time='14:47:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='280', tradeid='266', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.4, volume=10, time='14:56:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='281', tradeid='267', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='10:55:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='282', tradeid='268', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='10:56:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='283', tradeid='269', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='11:15:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='284', tradeid='270', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='11:19:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='285', tradeid='271', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.0, volume=10, time='13:24:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='286', tradeid='272', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='13:25:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='287', tradeid='273', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='13:36:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='288', tradeid='274', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.4, volume=10, time='13:38:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='289', tradeid='275', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.8, volume=10, time='13:47:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='290', tradeid='276', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.2, volume=10, time='13:50:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='291', tradeid='277', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='14:32:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='292', tradeid='278', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='14:34:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='293', tradeid='279', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.6, volume=10, time='14:40:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='294', tradeid='280', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='14:49:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='295', tradeid='281', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.0, volume=10, time='09:30:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='296', tradeid='282', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.0, volume=10, time='09:31:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='297', tradeid='283', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='09:40:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='298', tradeid='284', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='09:41:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='299', tradeid='285', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='10:09:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='300', tradeid='286', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.2, volume=10, time='10:15:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='301', tradeid='287', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='10:16:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='302', tradeid='288', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='10:18:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='303', tradeid='289', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='11:15:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='304', tradeid='290', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.2, volume=10, time='11:17:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='305', tradeid='291', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.0, volume=10, time='11:21:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='306', tradeid='292', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='11:22:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='307', tradeid='293', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=4.0, volume=10, time='11:24:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='308', tradeid='294', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='11:25:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='309', tradeid='295', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='13:13:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='310', tradeid='296', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.4, volume=10, time='13:32:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='311', tradeid='297', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.2, volume=10, time='13:54:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='312', tradeid='298', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.0, volume=10, time='13:58:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='313', tradeid='299', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.2, volume=10, time='14:14:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='314', tradeid='300', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.2, volume=10, time='14:17:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='315', tradeid='301', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.2, volume=10, time='14:34:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='316', tradeid='302', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.6, volume=10, time='14:35:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='317', tradeid='303', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.2, volume=10, time='14:54:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='318', tradeid='304', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.4, volume=10, time='14:55:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='319', tradeid='305', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='09:32:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='320', tradeid='306', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.6, volume=10, time='09:33:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='321', tradeid='307', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='09:50:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='322', tradeid='308', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.2, volume=10, time='09:53:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='323', tradeid='309', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.0, volume=10, time='10:39:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='324', tradeid='310', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.6, volume=10, time='10:40:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='325', tradeid='311', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.0, volume=10, time='10:54:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='326', tradeid='312', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.2, volume=10, time='11:03:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='327', tradeid='313', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.0, volume=10, time='11:10:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='328', tradeid='314', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='11:13:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='329', tradeid='315', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='11:29:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='330', tradeid='316', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='13:00:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='331', tradeid='317', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='13:04:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='332', tradeid='318', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=3.2, volume=10, time='13:05:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='333', tradeid='319', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.8, volume=10, time='13:24:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='334', tradeid='320', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='13:36:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='335', tradeid='321', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.8, volume=10, time='13:40:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='336', tradeid='322', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.4, volume=10, time='13:41:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='337', tradeid='323', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.2, volume=10, time='13:44:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='338', tradeid='324', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.4, volume=10, time='13:46:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='339', tradeid='325', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.6, volume=10, time='13:52:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='340', tradeid='326', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='13:56:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='341', tradeid='327', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='14:23:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='342', tradeid='328', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=3.0, volume=10, time='14:31:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='343', tradeid='329', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='14:39:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='344', tradeid='330', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='14:40:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='345', tradeid='331', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.4, volume=10, time='09:30:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='346', tradeid='332', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.4, volume=10, time='09:33:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='347', tradeid='333', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.2, volume=10, time='10:00:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='348', tradeid='334', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='10:07:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='349', tradeid='335', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.8, volume=10, time='10:08:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='350', tradeid='336', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.8, volume=10, time='10:11:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='351', tradeid='337', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.8, volume=10, time='10:16:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='352', tradeid='338', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.8, volume=10, time='10:18:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='353', tradeid='339', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.2, volume=10, time='10:31:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='354', tradeid='340', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.2, volume=10, time='10:33:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='355', tradeid='341', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.8, volume=10, time='11:24:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='356', tradeid='342', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.2, volume=10, time='11:27:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='358', tradeid='343', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.6, volume=10, time='14:03:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='359', tradeid='344', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=0.6, volume=10, time='14:04:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='360', tradeid='345', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=0.2, volume=10, time='14:13:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='361', tradeid='346', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.2, volume=10, time='14:14:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='362', tradeid='347', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=0.4, volume=10, time='14:27:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='363', tradeid='348', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.4, volume=10, time='14:28:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='365', tradeid='349', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.8, volume=10, time='10:04:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='367', tradeid='350', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=0.4, volume=10, time='10:07:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='368', tradeid='351', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.6, volume=10, time='10:27:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='369', tradeid='352', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.8, volume=10, time='10:35:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='370', tradeid='353', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.2, volume=10, time='11:00:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='371', tradeid='354', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.0, volume=10, time='11:02:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='372', tradeid='355', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.2, volume=10, time='11:15:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='373', tradeid='356', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.2, volume=10, time='11:17:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='374', tradeid='357', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.4, volume=10, time='13:07:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='375', tradeid='358', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=0.4, volume=10, time='13:09:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='376', tradeid='359', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.2, volume=10, time='13:49:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='377', tradeid='360', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.6, volume=10, time='13:51:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='378', tradeid='361', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.6, volume=10, time='14:24:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='379', tradeid='362', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.6, volume=10, time='14:29:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='380', tradeid='363', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.8, volume=10, time='14:54:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='381', tradeid='364', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.8, volume=10, time='14:55:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='382', tradeid='365', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.6, volume=10, time='14:56:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='383', tradeid='366', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.6, volume=10, time='09:32:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='384', tradeid='367', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.2, volume=10, time='10:00:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='385', tradeid='368', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.4, volume=10, time='10:03:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='386', tradeid='369', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.6, volume=10, time='10:18:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='387', tradeid='370', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=0.4, volume=10, time='10:19:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='388', tradeid='371', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.2, volume=10, time='10:20:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='389', tradeid='372', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.2, volume=10, time='10:27:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='390', tradeid='373', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.6, volume=10, time='10:28:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='391', tradeid='374', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.0, volume=10, time='10:31:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='392', tradeid='375', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.4, volume=10, time='10:58:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='393', tradeid='376', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.0, volume=10, time='11:08:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='394', tradeid='377', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=0.8, volume=10, time='11:18:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='395', tradeid='378', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.4, volume=10, time='11:19:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='396', tradeid='379', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.0, volume=10, time='13:10:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='397', tradeid='380', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.2, volume=10, time='13:13:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='398', tradeid='381', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.4, volume=10, time='14:00:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='399', tradeid='382', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.8, volume=10, time='14:02:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='400', tradeid='383', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.4, volume=10, time='14:24:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='401', tradeid='384', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.4, volume=10, time='14:25:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='402', tradeid='385', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.0, volume=10, time='14:38:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='403', tradeid='386', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.0, volume=10, time='14:44:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='404', tradeid='387', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.6, volume=10, time='14:50:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='405', tradeid='388', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.2, volume=10, time='14:55:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='406', tradeid='389', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.2, volume=10, time='14:56:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='407', tradeid='390', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.0, volume=10, time='09:31:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='408', tradeid='391', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.2, volume=10, time='10:17:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='409', tradeid='392', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.0, volume=10, time='10:18:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='410', tradeid='393', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.4, volume=10, time='10:31:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='411', tradeid='394', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=0.6, volume=10, time='10:32:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='412', tradeid='395', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.0, volume=10, time='10:52:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='413', tradeid='396', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.0, volume=10, time='10:56:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='414', tradeid='397', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.8, volume=10, time='13:01:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='415', tradeid='398', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.4, volume=10, time='13:04:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='416', tradeid='399', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.8, volume=10, time='13:31:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='417', tradeid='400', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=0.8, volume=10, time='13:33:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='418', tradeid='401', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=1.4, volume=10, time='13:45:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='419', tradeid='402', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.0, volume=10, time='13:46:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='420', tradeid='403', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.2, volume=10, time='14:30:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='422', tradeid='404', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=1.8, volume=10, time='14:34:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='423', tradeid='405', direction=<Direction.LONG: '多'>, offset=<Offset.NONE: ''>, price=2.0, volume=10, time='14:53:00')\n", "TradeData(gateway_name='BACKTESTING', symbol='IF-Spread', exchange=<Exchange.LOCAL: 'LOCAL'>, orderid='424', tradeid='406', direction=<Direction.SHORT: '空'>, offset=<Offset.NONE: ''>, price=2.0, volume=10, time='14:54:00')\n"]}], "source": ["for trade in engine.trades.values():\n", "    print(trade)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.1"}}, "nbformat": 4, "nbformat_minor": 2}