"""
仓位管理器
提供实时仓位监控、风险控制、止盈止损管理等功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass

from vnpy.trader.object import PositionData, TickData, TradeData
from vnpy.trader.constant import Direction, Offset

from ...config import get_trading_config, get_risk_config
from ...utils.logger import get_logger
from ...utils.helpers import safe_float, format_number, format_percentage


@dataclass
class PositionInfo:
    """仓位信息"""
    symbol: str
    direction: Direction
    volume: int
    frozen: int
    price: float
    pnl: float
    percent: float
    margin: float
    last_price: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0


@dataclass
class RiskMetrics:
    """风险指标"""
    total_margin: float
    total_pnl: float
    margin_ratio: float
    risk_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float


class PositionManager:
    """仓位管理器"""
    
    def __init__(self):
        """初始化仓位管理器"""
        self.logger = get_logger("PositionManager")
        self.trading_config = get_trading_config()
        self.risk_config = get_risk_config()
        
        # 仓位数据
        self.positions: Dict[str, PositionInfo] = {}
        self.trades_history: List[TradeData] = []
        
        # 风险控制参数
        self.max_total_risk = 0.20  # 总风险不超过20%
        self.max_single_risk = 0.05  # 单品种风险不超过5%
        self.max_margin_ratio = 0.80  # 最大保证金使用率80%
        
        # 止盈止损参数
        self.profit_target_ratio = 2.0  # 2倍ATR止盈
        self.stop_loss_ratio = 1.5     # 1.5倍ATR止损
        self.trailing_stop_ratio = 1.5  # 1.5倍ATR移动止损
        
        self.logger.info("仓位管理器初始化完成")
    
    def update_position(self, position_data: PositionData):
        """更新仓位数据"""
        try:
            symbol = position_data.symbol
            
            if position_data.volume == 0:
                # 仓位为0，移除记录
                if symbol in self.positions:
                    del self.positions[symbol]
                    self.logger.info(f"移除空仓位: {symbol}")
            else:
                # 更新仓位信息
                position_info = PositionInfo(
                    symbol=symbol,
                    direction=position_data.direction,
                    volume=position_data.volume,
                    frozen=position_data.frozen,
                    price=position_data.price,
                    pnl=position_data.pnl,
                    percent=position_data.percent,
                    margin=getattr(position_data, 'margin', 0),
                    last_price=getattr(position_data, 'last_price', position_data.price)
                )
                
                # 计算未实现盈亏
                if position_info.last_price > 0:
                    if position_info.direction == Direction.LONG:
                        position_info.unrealized_pnl = (position_info.last_price - position_info.price) * position_info.volume
                    else:
                        position_info.unrealized_pnl = (position_info.price - position_info.last_price) * position_info.volume
                
                self.positions[symbol] = position_info
                
        except Exception as e:
            self.logger.error(f"更新仓位数据失败: {e}")
    
    def update_tick(self, tick_data: TickData):
        """更新行情数据"""
        try:
            symbol = tick_data.symbol
            if symbol in self.positions:
                position = self.positions[symbol]
                position.last_price = tick_data.last_price
                
                # 重新计算未实现盈亏
                if position.direction == Direction.LONG:
                    position.unrealized_pnl = (tick_data.last_price - position.price) * position.volume
                else:
                    position.unrealized_pnl = (position.price - tick_data.last_price) * position.volume
                
        except Exception as e:
            self.logger.error(f"更新行情数据失败: {e}")
    
    def add_trade(self, trade_data: TradeData):
        """添加成交记录"""
        try:
            self.trades_history.append(trade_data)
            self.logger.debug(f"添加成交记录: {trade_data.symbol} {trade_data.direction.value} {trade_data.volume}手")
        except Exception as e:
            self.logger.error(f"添加成交记录失败: {e}")
    
    def get_position_summary(self) -> Dict[str, Any]:
        """获取仓位汇总"""
        try:
            if not self.positions:
                return {
                    'total_positions': 0,
                    'total_margin': 0,
                    'total_pnl': 0,
                    'total_unrealized_pnl': 0,
                    'long_positions': 0,
                    'short_positions': 0
                }
            
            total_margin = sum(pos.margin for pos in self.positions.values())
            total_pnl = sum(pos.pnl for pos in self.positions.values())
            total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
            
            long_positions = sum(1 for pos in self.positions.values() if pos.direction == Direction.LONG)
            short_positions = sum(1 for pos in self.positions.values() if pos.direction == Direction.SHORT)
            
            return {
                'total_positions': len(self.positions),
                'total_margin': total_margin,
                'total_pnl': total_pnl,
                'total_unrealized_pnl': total_unrealized_pnl,
                'long_positions': long_positions,
                'short_positions': short_positions,
                'positions': list(self.positions.values())
            }
        
        except Exception as e:
            self.logger.error(f"获取仓位汇总失败: {e}")
            return {}
    
    def calculate_risk_metrics(self, total_capital: float) -> RiskMetrics:
        """计算风险指标"""
        try:
            summary = self.get_position_summary()
            
            total_margin = summary.get('total_margin', 0)
            total_pnl = summary.get('total_pnl', 0) + summary.get('total_unrealized_pnl', 0)
            
            # 计算保证金使用率
            margin_ratio = (total_margin / total_capital) if total_capital > 0 else 0
            
            # 计算风险比例
            risk_ratio = abs(total_pnl) / total_capital if total_capital > 0 else 0
            
            # 计算最大回撤（简化版本）
            max_drawdown = self._calculate_max_drawdown()
            
            # 计算胜率
            win_rate = self._calculate_win_rate()
            
            # 计算盈亏比
            profit_factor = self._calculate_profit_factor()
            
            return RiskMetrics(
                total_margin=total_margin,
                total_pnl=total_pnl,
                margin_ratio=margin_ratio,
                risk_ratio=risk_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                profit_factor=profit_factor
            )
        
        except Exception as e:
            self.logger.error(f"计算风险指标失败: {e}")
            return RiskMetrics(0, 0, 0, 0, 0, 0, 0)
    
    def _calculate_max_drawdown(self) -> float:
        """计算最大回撤"""
        # 简化实现，实际应该基于历史净值曲线
        if not self.trades_history:
            return 0.0
        
        # 这里应该实现更复杂的回撤计算逻辑
        return 0.0
    
    def _calculate_win_rate(self) -> float:
        """计算胜率"""
        if not self.trades_history:
            return 0.0
        
        # 简化实现，需要配对开平仓计算
        profitable_trades = sum(1 for trade in self.trades_history if getattr(trade, 'pnl', 0) > 0)
        total_trades = len(self.trades_history)
        
        return (profitable_trades / total_trades * 100) if total_trades > 0 else 0.0
    
    def _calculate_profit_factor(self) -> float:
        """计算盈亏比"""
        if not self.trades_history:
            return 0.0
        
        # 简化实现
        total_profit = sum(getattr(trade, 'pnl', 0) for trade in self.trades_history if getattr(trade, 'pnl', 0) > 0)
        total_loss = abs(sum(getattr(trade, 'pnl', 0) for trade in self.trades_history if getattr(trade, 'pnl', 0) < 0))
        
        return (total_profit / total_loss) if total_loss > 0 else 0.0
    
    def check_risk_limits(self, total_capital: float) -> List[str]:
        """检查风险限制"""
        warnings = []
        
        try:
            risk_metrics = self.calculate_risk_metrics(total_capital)
            
            # 检查保证金使用率
            if risk_metrics.margin_ratio > self.max_margin_ratio:
                warnings.append(f"保证金使用率过高: {format_percentage(risk_metrics.margin_ratio)} > {format_percentage(self.max_margin_ratio)}")
            
            # 检查总风险
            if risk_metrics.risk_ratio > self.max_total_risk:
                warnings.append(f"总风险过高: {format_percentage(risk_metrics.risk_ratio)} > {format_percentage(self.max_total_risk)}")
            
            # 检查单品种风险
            for symbol, position in self.positions.items():
                single_risk = abs(position.unrealized_pnl) / total_capital if total_capital > 0 else 0
                if single_risk > self.max_single_risk:
                    warnings.append(f"{symbol} 单品种风险过高: {format_percentage(single_risk)} > {format_percentage(self.max_single_risk)}")
            
        except Exception as e:
            self.logger.error(f"检查风险限制失败: {e}")
            warnings.append(f"风险检查失败: {str(e)}")
        
        return warnings
    
    def get_position_details(self) -> pd.DataFrame:
        """获取仓位详情"""
        if not self.positions:
            return pd.DataFrame()
        
        try:
            data = []
            for symbol, position in self.positions.items():
                data.append({
                    '品种': symbol,
                    '方向': '多头' if position.direction == Direction.LONG else '空头',
                    '持仓': position.volume,
                    '冻结': position.frozen,
                    '均价': position.price,
                    '现价': position.last_price,
                    '浮动盈亏': position.unrealized_pnl,
                    '持仓盈亏': position.pnl,
                    '保证金': position.margin,
                    '盈亏比例(%)': position.percent
                })
            
            return pd.DataFrame(data)
        
        except Exception as e:
            self.logger.error(f"获取仓位详情失败: {e}")
            return pd.DataFrame()
    
    def display_positions(self):
        """显示仓位信息"""
        df_positions = self.get_position_details()
        
        if df_positions.empty:
            self.logger.info("当前无持仓")
            return
        
        self.logger.info("=" * 100)
        self.logger.info("当前持仓情况")
        self.logger.info("=" * 100)
        
        for _, row in df_positions.iterrows():
            self.logger.info(f"{row['品种']:12} | {row['方向']:4} | {row['持仓']:6}手 | "
                           f"均价:{row['均价']:8.2f} | 现价:{row['现价']:8.2f} | "
                           f"浮盈:{row['浮动盈亏']:10.2f} | 保证金:{row['保证金']:10.2f}")
        
        # 显示汇总信息
        summary = self.get_position_summary()
        self.logger.info("-" * 100)
        self.logger.info(f"持仓品种: {summary['total_positions']}个 | "
                        f"多头: {summary['long_positions']}个 | 空头: {summary['short_positions']}个")
        self.logger.info(f"总保证金: {format_number(summary['total_margin'])}元 | "
                        f"浮动盈亏: {format_number(summary['total_unrealized_pnl'])}元")


if __name__ == "__main__":
    # 测试仓位管理器
    manager = PositionManager()
    
    # 模拟仓位数据
    from vnpy.trader.object import PositionData
    from vnpy.trader.constant import Direction
    
    # 创建模拟仓位
    position = PositionData(
        symbol="RB888.SHFE",
        exchange="SHFE",
        direction=Direction.LONG,
        volume=10,
        frozen=0,
        price=3500.0,
        pnl=1000.0,
        percent=2.5,
        gateway_name="test"
    )
    
    manager.update_position(position)
    
    # 显示仓位
    manager.display_positions()
    
    # 计算风险指标
    risk_metrics = manager.calculate_risk_metrics(100000)
    print(f"风险指标: {risk_metrics}")
    
    # 检查风险限制
    warnings = manager.check_risk_limits(100000)
    if warnings:
        print(f"风险警告: {warnings}")
    
    print("仓位管理器测试完成")
