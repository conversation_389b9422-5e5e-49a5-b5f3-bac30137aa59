# VNPY数据删除工具使用说明

## 功能描述

这个工具用于删除VNPY数据库中所有合约从**2025年4月21日至今**的数据，包括：
- K线数据（Bar数据）
- Tick数据

## ⚠️ 重要警告

1. **数据删除不可逆**：一旦删除，数据无法恢复，除非使用备份
2. **自动备份**：程序会在删除前自动备份SQLite数据库
3. **谨慎操作**：请确认删除的时间范围是您想要的

## 支持的数据库类型

- ✅ **SQLite** - 完全支持，包含自动备份功能
- ✅ **MySQL** - 支持删除操作
- ✅ **PostgreSQL** - 支持删除操作  
- ✅ **MongoDB** - 支持删除操作
- ⚠️ **InfluxDB** - 暂不支持（需要特殊处理）

## 使用方法

### 1. 环境准备

确保您的VNPY环境已正确配置，并且可以正常访问数据库。

### 2. 运行程序

```bash
python delete_contract_data_by_timerange.py
```

### 3. 操作流程

1. **确认删除范围**：程序会显示删除时间范围（2025-04-21至当前时间）
2. **安全确认**：需要输入`YES`确认删除操作
3. **自动备份**：对于SQLite数据库，会自动创建备份文件
4. **扫描合约**：自动获取数据库中的所有合约
5. **批量删除**：逐个处理每个合约的数据
6. **统计报告**：显示删除结果统计

### 4. 交互示例

```
================================================================================
VNPY数据删除工具
功能：删除所有合约2025年4月21日至今的数据
================================================================================
删除时间范围：2025-04-21 00:00:00 至 2025-01-20 10:30:00

⚠️  警告：此操作将删除指定时间范围内的所有合约数据，该操作不可逆！
📅 删除范围：2025-04-21 至 2025-01-20
请确认是否继续？(输入 'YES' 继续，其他任意键取消): YES

============================================================
开始备份数据库...
源文件: C:\Users\<USER>\.vntrader\database.db
备份文件: C:\Users\<USER>\.vntrader\backup\database_backup_20250120_103000.db
✅ SQLite数据库备份完成
============================================================

正在获取数据库中的所有合约...
找到 150 个有数据的合约

开始删除数据...
--------------------------------------------------------------------------------
[1/150] 处理合约: rb2101.SHFE (1m)
  📊 删除Bar数据: 1250 条
  📈 删除Tick数据: 45000 条
  ✅ 合约 rb2101.SHFE 处理完成
...
```

## 程序特性

### 安全特性

1. **双重确认**：需要明确输入`YES`才能执行删除
2. **自动备份**：SQLite数据库会自动备份到backup文件夹
3. **错误处理**：单个合约删除失败不会影响其他合约
4. **详细日志**：显示每个合约的处理结果

### 智能处理

1. **数据库兼容**：自动识别数据库类型并选择合适的删除方法
2. **批量操作**：高效处理大量合约数据
3. **进度显示**：实时显示处理进度
4. **统计报告**：完成后显示详细的删除统计

### 删除范围

- **开始时间**：2025年4月21日 00:00:00
- **结束时间**：当前时间
- **数据类型**：
  - 所有时间周期的K线数据（1分钟、5分钟、15分钟、1小时、日线等）
  - Tick数据（仅处理有1分钟K线的合约）

## 备份文件位置

对于SQLite数据库，备份文件保存在：
```
~/.vntrader/backup/database_backup_YYYYMMDD_HHMMSS.db
```

## 恢复数据

如果需要恢复数据，可以：

1. **停止VNPY程序**
2. **替换数据库文件**：
   ```bash
   cp ~/.vntrader/backup/database_backup_YYYYMMDD_HHMMSS.db ~/.vntrader/database.db
   ```
3. **重启VNPY程序**

## 注意事项

1. **运行前停止交易**：建议在非交易时间运行此工具
2. **确认时间范围**：程序硬编码删除2025年4月21日至今的数据
3. **磁盘空间**：确保有足够空间保存备份文件
4. **数据库连接**：确保数据库没有被其他程序占用

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 确认VNPY配置正确
   - 检查数据库服务是否运行

2. **权限不足**
   - 确保对数据库文件有读写权限
   - 对于MySQL/PostgreSQL，确认用户权限

3. **备份失败**
   - 检查磁盘空间
   - 确认目标文件夹权限

### 日志信息

程序会显示详细的处理日志：
- ✅ 成功操作
- ❌ 失败操作  
- ⚠️ 警告信息
- 📊 统计数据

## 技术细节

### 删除机制

- **关系型数据库**（SQLite/MySQL/PostgreSQL）：使用ORM批量删除
- **MongoDB**：使用MongoEngine查询删除
- **事务处理**：使用数据库事务确保数据一致性

### 时间处理

- 自动转换为数据库时区（默认：Asia/Shanghai）
- 精确的时间范围匹配（>= start_time AND <= end_time）

## 联系支持

如有问题，请检查：
1. VNPY版本兼容性
2. 数据库配置
3. Python环境依赖 