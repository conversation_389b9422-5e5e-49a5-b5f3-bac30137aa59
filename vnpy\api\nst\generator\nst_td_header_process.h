void processFrontConnected(Task *task);

void processRspUserLogin(Task *task);

void processAnsOrderInsert(Task *task);

void processRspOrderInsert(Task *task);

void processAnsOrderAction(Task *task);

void processRspOrderAction(Task *task);

void processOrderRtn(Task *task);

void processTradeRtn(Task *task);

void processRspTradingAccount(Task *task);

void processRspError(Task *task);

void processRspQryOrder(Task *task);

void processRspQryTrade(Task *task);

void processRspQryInvestorPosition(Task *task);

void processRspQryChangePwd(Task *task);

void processRspLogout(Task *task);

void processRtnInstrumentStatus(Task *task);

void processRspTest(Task *task);

void processErrRtnOrderInsert(Task *task);

void processErrRtnOrderAction(Task *task);

