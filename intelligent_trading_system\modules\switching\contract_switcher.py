"""
合约切换器
负责执行主力合约切换，更新策略配置，处理持仓转移等
"""

import json
import os
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

try:
    from vnpy.app.cta_strategy import CtaEngine
except ImportError:
    CtaEngine = None

from vnpy.trader.constant import Exchange
from vnpy.trader.object import PositionData

from .main_contract_detector import MainContractDetector
from ...config import get_trading_config
from ...utils.logger import get_logger
from ...utils.helpers import extract_product_code, parse_vt_symbol, generate_vt_symbol


@dataclass
class SwitchRecord:
    """切换记录"""
    timestamp: datetime
    product_code: str
    old_contract: str
    new_contract: str
    reason: str
    success: bool
    error_msg: str = ""


class ContractSwitcher:
    """合约切换器"""
    
    def __init__(self, cta_engine = None):
        """初始化合约切换器"""
        self.logger = get_logger("ContractSwitcher")
        self.trading_config = get_trading_config()
        self.cta_engine = cta_engine
        
        # 初始化主力合约检测器
        self.detector = MainContractDetector()
        
        # 切换记录
        self.switch_records: List[SwitchRecord] = []
        
        # 配置文件路径
        self.strategy_settings_file = "cta_strategy_setting.json"
        self.switch_log_file = "contract_switch_log.json"
        
        self.logger.info("合约切换器初始化完成")
    
    def check_all_strategies_for_switch(self) -> List[Dict[str, Any]]:
        """检查所有策略是否需要切换合约"""
        switch_suggestions = []
        
        try:
            # 加载策略配置
            strategy_settings = self._load_strategy_settings()
            if not strategy_settings:
                self.logger.warning("未找到策略配置文件")
                return switch_suggestions
            
            # 获取策略持仓数据
            strategy_positions = self._get_strategy_positions()
            
            # 遍历每个策略
            for strategy_name, strategy_info in strategy_settings.items():
                try:
                    # 检查策略是否有持仓
                    current_pos = strategy_positions.get(strategy_name, {}).get("pos", 0)
                    
                    if current_pos == 0:  # 只有无持仓时才考虑切换
                        # 获取当前合约信息
                        current_vt_symbol = strategy_info.get("vt_symbol", "")
                        if not current_vt_symbol:
                            continue
                        
                        current_symbol, exchange_str = parse_vt_symbol(current_vt_symbol)
                        product_code = extract_product_code(current_symbol)
                        
                        try:
                            exchange = Exchange(exchange_str)
                        except ValueError:
                            self.logger.warning(f"无效的交易所代码: {exchange_str}")
                            continue
                        
                        # 检查是否需要切换
                        need_switch, new_contract, reason = self.detector.check_switch_needed(
                            current_symbol, product_code, exchange
                        )
                        
                        if need_switch and new_contract:
                            new_vt_symbol = generate_vt_symbol(new_contract, exchange_str)
                            
                            switch_suggestion = {
                                'strategy_name': strategy_name,
                                'current_contract': current_vt_symbol,
                                'new_contract': new_vt_symbol,
                                'product_code': product_code,
                                'reason': reason,
                                'current_position': current_pos
                            }
                            
                            switch_suggestions.append(switch_suggestion)
                            self.logger.info(f"策略 {strategy_name} 建议切换: {current_vt_symbol} -> {new_vt_symbol}")
                
                except Exception as e:
                    self.logger.error(f"检查策略 {strategy_name} 切换需求失败: {e}")
            
            return switch_suggestions
        
        except Exception as e:
            self.logger.error(f"检查策略切换需求失败: {e}")
            return []
    
    def execute_contract_switch(self, strategy_name: str, new_vt_symbol: str, 
                              reason: str = "") -> bool:
        """执行合约切换"""
        try:
            # 加载策略配置
            strategy_settings = self._load_strategy_settings()
            if not strategy_settings:
                self.logger.error("无法加载策略配置文件")
                return False
            
            if strategy_name not in strategy_settings:
                self.logger.error(f"策略 {strategy_name} 不存在")
                return False
            
            # 获取当前合约
            old_vt_symbol = strategy_settings[strategy_name].get("vt_symbol", "")
            
            # 检查策略是否有持仓
            strategy_positions = self._get_strategy_positions()
            current_pos = strategy_positions.get(strategy_name, {}).get("pos", 0)
            
            if current_pos != 0:
                self.logger.warning(f"策略 {strategy_name} 当前有持仓 {current_pos}，不能切换合约")
                return False
            
            # 更新策略配置
            strategy_settings[strategy_name]["vt_symbol"] = new_vt_symbol
            
            # 保存配置文件
            if self._save_strategy_settings(strategy_settings):
                # 记录切换
                old_symbol, _ = parse_vt_symbol(old_vt_symbol)
                new_symbol, _ = parse_vt_symbol(new_vt_symbol)
                product_code = extract_product_code(new_symbol)
                
                switch_record = SwitchRecord(
                    timestamp=datetime.now(),
                    product_code=product_code,
                    old_contract=old_vt_symbol,
                    new_contract=new_vt_symbol,
                    reason=reason,
                    success=True
                )
                
                self.switch_records.append(switch_record)
                self._save_switch_log()
                
                self.logger.info(f"策略 {strategy_name} 合约切换成功: {old_vt_symbol} -> {new_vt_symbol}")
                
                # 如果有CTA引擎，尝试重新加载策略
                if self.cta_engine:
                    try:
                        self._reload_strategy(strategy_name)
                    except Exception as e:
                        self.logger.warning(f"重新加载策略失败: {e}")
                
                return True
            else:
                self.logger.error("保存策略配置失败")
                return False
        
        except Exception as e:
            self.logger.error(f"执行合约切换失败: {e}")
            
            # 记录失败的切换
            switch_record = SwitchRecord(
                timestamp=datetime.now(),
                product_code="",
                old_contract="",
                new_contract=new_vt_symbol,
                reason=reason,
                success=False,
                error_msg=str(e)
            )
            
            self.switch_records.append(switch_record)
            self._save_switch_log()
            
            return False
    
    def batch_switch_contracts(self, switch_suggestions: List[Dict[str, Any]]) -> Dict[str, bool]:
        """批量执行合约切换"""
        results = {}
        
        for suggestion in switch_suggestions:
            strategy_name = suggestion['strategy_name']
            new_contract = suggestion['new_contract']
            reason = suggestion['reason']
            
            success = self.execute_contract_switch(strategy_name, new_contract, reason)
            results[strategy_name] = success
            
            if success:
                self.logger.info(f"策略 {strategy_name} 切换成功")
            else:
                self.logger.error(f"策略 {strategy_name} 切换失败")
        
        return results
    
    def auto_switch_all_strategies(self) -> Dict[str, Any]:
        """自动切换所有需要切换的策略"""
        self.logger.info("开始自动合约切换检查...")
        
        # 检查切换建议
        switch_suggestions = self.check_all_strategies_for_switch()
        
        if not switch_suggestions:
            self.logger.info("没有需要切换的策略")
            return {'total': 0, 'success': 0, 'failed': 0, 'details': []}
        
        self.logger.info(f"发现 {len(switch_suggestions)} 个策略需要切换")
        
        # 执行批量切换
        results = self.batch_switch_contracts(switch_suggestions)
        
        # 统计结果
        total = len(results)
        success = sum(1 for r in results.values() if r)
        failed = total - success
        
        summary = {
            'total': total,
            'success': success,
            'failed': failed,
            'details': []
        }
        
        # 添加详细信息
        for suggestion in switch_suggestions:
            strategy_name = suggestion['strategy_name']
            detail = {
                'strategy_name': strategy_name,
                'old_contract': suggestion['current_contract'],
                'new_contract': suggestion['new_contract'],
                'reason': suggestion['reason'],
                'success': results.get(strategy_name, False)
            }
            summary['details'].append(detail)
        
        self.logger.info(f"自动切换完成: 总计 {total}, 成功 {success}, 失败 {failed}")
        return summary
    
    def _load_strategy_settings(self) -> Optional[Dict[str, Any]]:
        """加载策略配置"""
        try:
            if os.path.exists(self.strategy_settings_file):
                with open(self.strategy_settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                self.logger.warning(f"策略配置文件不存在: {self.strategy_settings_file}")
                return None
        except Exception as e:
            self.logger.error(f"加载策略配置失败: {e}")
            return None
    
    def _save_strategy_settings(self, settings: Dict[str, Any]) -> bool:
        """保存策略配置"""
        try:
            with open(self.strategy_settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            self.logger.error(f"保存策略配置失败: {e}")
            return False
    
    def _get_strategy_positions(self) -> Dict[str, Dict[str, Any]]:
        """获取策略持仓数据"""
        try:
            # 尝试从CTA引擎获取策略数据
            if self.cta_engine:
                strategy_data = {}
                for strategy_name in self.cta_engine.strategies.keys():
                    strategy = self.cta_engine.strategies[strategy_name]
                    strategy_data[strategy_name] = {
                        'pos': getattr(strategy, 'pos', 0),
                        'trading': getattr(strategy, 'trading', False)
                    }
                return strategy_data
            else:
                # 如果没有CTA引擎，尝试从文件加载
                strategy_data_file = "cta_strategy_data.json"
                if os.path.exists(strategy_data_file):
                    with open(strategy_data_file, 'r', encoding='utf-8') as f:
                        return json.load(f)
                else:
                    return {}
        except Exception as e:
            self.logger.error(f"获取策略持仓数据失败: {e}")
            return {}
    
    def _reload_strategy(self, strategy_name: str):
        """重新加载策略"""
        if not self.cta_engine:
            return
        
        try:
            # 停止策略
            if strategy_name in self.cta_engine.strategies:
                self.cta_engine.stop_strategy(strategy_name)
                self.logger.info(f"策略 {strategy_name} 已停止")
            
            # 重新加载策略配置
            self.cta_engine.reload_strategy(strategy_name)
            self.logger.info(f"策略 {strategy_name} 配置已重新加载")
            
        except Exception as e:
            self.logger.error(f"重新加载策略 {strategy_name} 失败: {e}")
    
    def _save_switch_log(self):
        """保存切换日志"""
        try:
            log_data = []
            for record in self.switch_records:
                log_data.append({
                    'timestamp': record.timestamp.isoformat(),
                    'product_code': record.product_code,
                    'old_contract': record.old_contract,
                    'new_contract': record.new_contract,
                    'reason': record.reason,
                    'success': record.success,
                    'error_msg': record.error_msg
                })
            
            with open(self.switch_log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
        
        except Exception as e:
            self.logger.error(f"保存切换日志失败: {e}")
    
    def get_switch_history(self, days: int = 30) -> List[SwitchRecord]:
        """获取切换历史"""
        cutoff_date = datetime.now() - timedelta(days=days)
        return [record for record in self.switch_records if record.timestamp >= cutoff_date]
    
    def display_switch_summary(self):
        """显示切换摘要"""
        if not self.switch_records:
            self.logger.info("暂无合约切换记录")
            return
        
        recent_records = self.get_switch_history(7)  # 最近7天
        
        self.logger.info("=" * 80)
        self.logger.info("合约切换摘要 (最近7天)")
        self.logger.info("=" * 80)
        
        success_count = sum(1 for r in recent_records if r.success)
        failed_count = len(recent_records) - success_count
        
        self.logger.info(f"总切换次数: {len(recent_records)}")
        self.logger.info(f"成功: {success_count}, 失败: {failed_count}")
        
        if recent_records:
            self.logger.info("\n最近切换记录:")
            for record in recent_records[-5:]:  # 显示最近5条
                status = "✅" if record.success else "❌"
                self.logger.info(f"{status} {record.timestamp.strftime('%m-%d %H:%M')} "
                               f"{record.old_contract} -> {record.new_contract} "
                               f"({record.reason})")


if __name__ == "__main__":
    # 测试合约切换器
    switcher = ContractSwitcher()
    
    # 检查切换建议
    suggestions = switcher.check_all_strategies_for_switch()
    print(f"切换建议: {suggestions}")
    
    # 显示切换摘要
    switcher.display_switch_summary()
    
    print("合约切换器测试完成")
