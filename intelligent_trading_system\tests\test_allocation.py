"""
资金分配模块测试
测试资金分配和仓位管理功能
"""

import unittest
import os
import sys

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from modules.allocation.capital_allocator import CapitalAllocator
from modules.allocation.position_manager import PositionManager


class TestAllocationModule(unittest.TestCase):
    """资金分配模块测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.capital_allocator = CapitalAllocator()
        self.position_manager = PositionManager()
    
    def test_capital_allocator_initialization(self):
        """测试资金分配器初始化"""
        self.assertIsNotNone(self.capital_allocator)
        print("✓ 资金分配器初始化测试通过")
    
    def test_position_manager_initialization(self):
        """测试仓位管理器初始化"""
        self.assertIsNotNone(self.position_manager)
        print("✓ 仓位管理器初始化测试通过")
    
    def test_allocation_methods(self):
        """测试分配方法"""
        self.assertTrue(hasattr(self.capital_allocator, 'allocate_capital'))
        self.assertTrue(hasattr(self.position_manager, 'check_all_risk_limits'))
        print("✓ 分配方法测试通过")


if __name__ == "__main__":
    unittest.main()
