"""
账户管理器测试
测试账户连接、状态监控和管理功能
"""

import unittest
import os
import sys
from unittest.mock import Mock, patch

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from core.account_manager import AccountManager


class TestAccountManager(unittest.TestCase):
    """账户管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.account_manager = AccountManager()
    
    def test_account_manager_initialization(self):
        """测试账户管理器初始化"""
        self.assertIsNotNone(self.account_manager)
        print("✓ 账户管理器初始化测试通过")
    
    def test_account_status_methods(self):
        """测试账户状态方法"""
        # 测试获取账户状态
        status = self.account_manager.get_all_account_status()
        self.assertIsInstance(status, dict)
        print("✓ 账户状态方法测试通过")
    
    def test_connection_methods(self):
        """测试连接方法"""
        # 由于需要实际的CTP连接，这里只测试方法存在
        self.assertTrue(hasattr(self.account_manager, 'connect_all_accounts'))
        self.assertTrue(hasattr(self.account_manager, 'disconnect_all_accounts'))
        print("✓ 连接方法测试通过")


if __name__ == "__main__":
    unittest.main()
