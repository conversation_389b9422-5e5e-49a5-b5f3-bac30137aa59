{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#%%\n", "from vnpy.app.cta_strategy.backtesting import BacktestingEngine, OptimizationSetting\n", "from vnpy.app.cta_strategy.strategies.atr_rsi_strategy import (\n", "    AtrRsiStrategy,\n", ")\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#%%\n", "engine = BacktestingEngine()\n", "engine.set_parameters(\n", "    vt_symbol=\"IF88.CFFEX\",\n", "    interval=\"1m\",\n", "    start=datetime(2019, 1, 1),\n", "    end=datetime(2019, 4, 30),\n", "    rate=0.3/10000,\n", "    slippage=0.2,\n", "    size=300,\n", "    pricetick=0.2,\n", "    capital=1_000_000,\n", ")\n", "engine.add_strategy(AtrRsiStrategy, {})"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": false}, "outputs": [], "source": ["#%%\n", "engine.load_data()\n", "engine.run_backtesting()\n", "df = engine.calculate_result()\n", "engine.calculate_statistics()\n", "engine.show_chart()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["setting = OptimizationSetting()\n", "setting.set_target(\"sharpe_ratio\")\n", "setting.add_parameter(\"atr_length\", 3, 39, 1)\n", "setting.add_parameter(\"atr_ma_length\", 10, 30, 1)\n", "\n", "engine.run_ga_optimization(setting)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.1"}}, "nbformat": 4, "nbformat_minor": 2}