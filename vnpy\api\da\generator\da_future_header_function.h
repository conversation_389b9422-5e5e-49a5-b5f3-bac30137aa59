int reqUserLogin(const dict &req, int reqid);

int reqUserLogout(const dict &req, int reqid);

int reqSafeVerify(const dict &req, int reqid);

int reqVerifyCode(const dict &req, int reqid);

int reqSetVerifyQA(const dict &req, int reqid);

int reqGetQuestion(const dict &req, int reqid);

int reqOrderInsert(const dict &req, int reqid);

int reqOrderModify(const dict &req, int reqid);

int reqOrderCancel(const dict &req, int reqid);

int reqPasswordUpdate(const dict &req, int reqid);

int reqQryOrder(const dict &req, int reqid);

int reqQryTrade(const dict &req, int reqid);

int reqQryCapital(const dict &req, int reqid);

int reqQryVersion(const dict &req, int reqid);

int reqQryCurrency(const dict &req, int reqid);

int reqQryExchange(const dict &req, int reqid);

int reqQryPosition(const dict &req, int reqid);

int reqQryStrategy(const dict &req, int reqid);

int reqQryCommodity(const dict &req, int reqid);

int reqQryInstrument(const dict &req, int reqid);

int reqQryExchangeTime(const dict &req, int reqid);

int reqQryCommodityTime(const dict &req, int reqid);

int reqQryTotalPosition(const dict &req, int reqid);

int reqQryStrategyDetail(const dict &req, int reqid);

