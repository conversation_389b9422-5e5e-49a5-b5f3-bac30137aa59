"""
合约切换模块测试
测试主力合约检测和切换功能
"""

import unittest
import os
import sys

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from modules.switching.main_contract_detector import MainContractDetector
from modules.switching.contract_switcher import ContractSwitcher


class TestSwitchingModule(unittest.TestCase):
    """合约切换模块测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.contract_detector = MainContractDetector()
        self.contract_switcher = ContractSwitcher(self.contract_detector)
    
    def test_contract_detector_initialization(self):
        """测试合约检测器初始化"""
        self.assertIsNotNone(self.contract_detector)
        print("✓ 合约检测器初始化测试通过")
    
    def test_contract_switcher_initialization(self):
        """测试合约切换器初始化"""
        self.assertIsNotNone(self.contract_switcher)
        print("✓ 合约切换器初始化测试通过")
    
    def test_switching_methods(self):
        """测试切换方法"""
        self.assertTrue(hasattr(self.contract_switcher, 'check_all_strategies_for_switch'))
        self.assertTrue(hasattr(self.contract_switcher, 'execute_contract_switch'))
        print("✓ 切换方法测试通过")


if __name__ == "__main__":
    unittest.main()
