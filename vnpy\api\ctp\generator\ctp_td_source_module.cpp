.def("reqAuthenticate", &TdApi::reqAuthenticate)
.def("reqUserLogin", &TdApi::reqUserLogin)
.def("reqUserLogout", &TdApi::reqUserLogout)
.def("reqUserPasswordUpdate", &TdApi::reqUserPasswordUpdate)
.def("reqTradingAccountPasswordUpdate", &TdApi::reqTradingAccountPasswordUpdate)
.def("reqUserAuthMethod", &TdApi::reqUserAuthMethod)
.def("reqGenUserCaptcha", &TdApi::reqGenUserCaptcha)
.def("reqGenUserText", &TdApi::reqGenUserText)
.def("reqUserLoginWithCaptcha", &TdApi::reqUserLoginWithCaptcha)
.def("reqUserLoginWithText", &TdApi::reqUserLoginWithText)
.def("reqUserLoginWithOTP", &TdApi::reqUserLoginWithOTP)
.def("reqOrderInsert", &TdApi::reqOrderInsert)
.def("reqParkedOrderInsert", &TdApi::reqParkedOrderInsert)
.def("reqParkedOrderAction", &TdApi::reqParkedOrderAction)
.def("reqOrderAction", &TdApi::reqOrderAction)
.def("reqQueryMaxOrderVolume", &TdApi::reqQueryMaxOrderVolume)
.def("reqSettlementInfoConfirm", &TdApi::reqSettlementInfoConfirm)
.def("reqRemoveParkedOrder", &TdApi::reqRemoveParkedOrder)
.def("reqRemoveParkedOrderAction", &TdApi::reqRemoveParkedOrderAction)
.def("reqExecOrderInsert", &TdApi::reqExecOrderInsert)
.def("reqExecOrderAction", &TdApi::reqExecOrderAction)
.def("reqForQuoteInsert", &TdApi::reqForQuoteInsert)
.def("reqQuoteInsert", &TdApi::reqQuoteInsert)
.def("reqQuoteAction", &TdApi::reqQuoteAction)
.def("reqBatchOrderAction", &TdApi::reqBatchOrderAction)
.def("reqOptionSelfCloseInsert", &TdApi::reqOptionSelfCloseInsert)
.def("reqOptionSelfCloseAction", &TdApi::reqOptionSelfCloseAction)
.def("reqCombActionInsert", &TdApi::reqCombActionInsert)
.def("reqQryOrder", &TdApi::reqQryOrder)
.def("reqQryTrade", &TdApi::reqQryTrade)
.def("reqQryInvestorPosition", &TdApi::reqQryInvestorPosition)
.def("reqQryTradingAccount", &TdApi::reqQryTradingAccount)
.def("reqQryInvestor", &TdApi::reqQryInvestor)
.def("reqQryTradingCode", &TdApi::reqQryTradingCode)
.def("reqQryInstrumentMarginRate", &TdApi::reqQryInstrumentMarginRate)
.def("reqQryInstrumentCommissionRate", &TdApi::reqQryInstrumentCommissionRate)
.def("reqQryExchange", &TdApi::reqQryExchange)
.def("reqQryProduct", &TdApi::reqQryProduct)
.def("reqQryInstrument", &TdApi::reqQryInstrument)
.def("reqQryDepthMarketData", &TdApi::reqQryDepthMarketData)
.def("reqQrySettlementInfo", &TdApi::reqQrySettlementInfo)
.def("reqQryTransferBank", &TdApi::reqQryTransferBank)
.def("reqQryInvestorPositionDetail", &TdApi::reqQryInvestorPositionDetail)
.def("reqQryNotice", &TdApi::reqQryNotice)
.def("reqQrySettlementInfoConfirm", &TdApi::reqQrySettlementInfoConfirm)
.def("reqQryInvestorPositionCombineDetail", &TdApi::reqQryInvestorPositionCombineDetail)
.def("reqQryCFMMCTradingAccountKey", &TdApi::reqQryCFMMCTradingAccountKey)
.def("reqQryEWarrantOffset", &TdApi::reqQryEWarrantOffset)
.def("reqQryInvestorProductGroupMargin", &TdApi::reqQryInvestorProductGroupMargin)
.def("reqQryExchangeMarginRate", &TdApi::reqQryExchangeMarginRate)
.def("reqQryExchangeMarginRateAdjust", &TdApi::reqQryExchangeMarginRateAdjust)
.def("reqQryExchangeRate", &TdApi::reqQryExchangeRate)
.def("reqQrySecAgentACIDMap", &TdApi::reqQrySecAgentACIDMap)
.def("reqQryProductExchRate", &TdApi::reqQryProductExchRate)
.def("reqQryProductGroup", &TdApi::reqQryProductGroup)
.def("reqQryMMInstrumentCommissionRate", &TdApi::reqQryMMInstrumentCommissionRate)
.def("reqQryMMOptionInstrCommRate", &TdApi::reqQryMMOptionInstrCommRate)
.def("reqQryInstrumentOrderCommRate", &TdApi::reqQryInstrumentOrderCommRate)
.def("reqQrySecAgentTradingAccount", &TdApi::reqQrySecAgentTradingAccount)
.def("reqQrySecAgentCheckMode", &TdApi::reqQrySecAgentCheckMode)
.def("reqQrySecAgentTradeInfo", &TdApi::reqQrySecAgentTradeInfo)
.def("reqQryOptionInstrTradeCost", &TdApi::reqQryOptionInstrTradeCost)
.def("reqQryOptionInstrCommRate", &TdApi::reqQryOptionInstrCommRate)
.def("reqQryExecOrder", &TdApi::reqQryExecOrder)
.def("reqQryForQuote", &TdApi::reqQryForQuote)
.def("reqQryQuote", &TdApi::reqQryQuote)
.def("reqQryOptionSelfClose", &TdApi::reqQryOptionSelfClose)
.def("reqQryInvestUnit", &TdApi::reqQryInvestUnit)
.def("reqQryCombInstrumentGuard", &TdApi::reqQryCombInstrumentGuard)
.def("reqQryCombAction", &TdApi::reqQryCombAction)
.def("reqQryTransferSerial", &TdApi::reqQryTransferSerial)
.def("reqQryAccountregister", &TdApi::reqQryAccountregister)
.def("reqQryContractBank", &TdApi::reqQryContractBank)
.def("reqQryParkedOrder", &TdApi::reqQryParkedOrder)
.def("reqQryParkedOrderAction", &TdApi::reqQryParkedOrderAction)
.def("reqQryTradingNotice", &TdApi::reqQryTradingNotice)
.def("reqQryBrokerTradingParams", &TdApi::reqQryBrokerTradingParams)
.def("reqQryBrokerTradingAlgos", &TdApi::reqQryBrokerTradingAlgos)
.def("reqQueryCFMMCTradingAccountToken", &TdApi::reqQueryCFMMCTradingAccountToken)
.def("reqFromBankToFutureByFuture", &TdApi::reqFromBankToFutureByFuture)
.def("reqFromFutureToBankByFuture", &TdApi::reqFromFutureToBankByFuture)
.def("reqQueryBankAccountMoneyByFuture", &TdApi::reqQueryBankAccountMoneyByFuture)

.def("onFrontConnected", &TdApi::onFrontConnected)
.def("onFrontDisconnected", &TdApi::onFrontDisconnected)
.def("onHeartBeatWarning", &TdApi::onHeartBeatWarning)
.def("onRspAuthenticate", &TdApi::onRspAuthenticate)
.def("onRspUserLogin", &TdApi::onRspUserLogin)
.def("onRspUserLogout", &TdApi::onRspUserLogout)
.def("onRspUserPasswordUpdate", &TdApi::onRspUserPasswordUpdate)
.def("onRspTradingAccountPasswordUpdate", &TdApi::onRspTradingAccountPasswordUpdate)
.def("onRspUserAuthMethod", &TdApi::onRspUserAuthMethod)
.def("onRspGenUserCaptcha", &TdApi::onRspGenUserCaptcha)
.def("onRspGenUserText", &TdApi::onRspGenUserText)
.def("onRspOrderInsert", &TdApi::onRspOrderInsert)
.def("onRspParkedOrderInsert", &TdApi::onRspParkedOrderInsert)
.def("onRspParkedOrderAction", &TdApi::onRspParkedOrderAction)
.def("onRspOrderAction", &TdApi::onRspOrderAction)
.def("onRspQueryMaxOrderVolume", &TdApi::onRspQueryMaxOrderVolume)
.def("onRspSettlementInfoConfirm", &TdApi::onRspSettlementInfoConfirm)
.def("onRspRemoveParkedOrder", &TdApi::onRspRemoveParkedOrder)
.def("onRspRemoveParkedOrderAction", &TdApi::onRspRemoveParkedOrderAction)
.def("onRspExecOrderInsert", &TdApi::onRspExecOrderInsert)
.def("onRspExecOrderAction", &TdApi::onRspExecOrderAction)
.def("onRspForQuoteInsert", &TdApi::onRspForQuoteInsert)
.def("onRspQuoteInsert", &TdApi::onRspQuoteInsert)
.def("onRspQuoteAction", &TdApi::onRspQuoteAction)
.def("onRspBatchOrderAction", &TdApi::onRspBatchOrderAction)
.def("onRspOptionSelfCloseInsert", &TdApi::onRspOptionSelfCloseInsert)
.def("onRspOptionSelfCloseAction", &TdApi::onRspOptionSelfCloseAction)
.def("onRspCombActionInsert", &TdApi::onRspCombActionInsert)
.def("onRspQryOrder", &TdApi::onRspQryOrder)
.def("onRspQryTrade", &TdApi::onRspQryTrade)
.def("onRspQryInvestorPosition", &TdApi::onRspQryInvestorPosition)
.def("onRspQryTradingAccount", &TdApi::onRspQryTradingAccount)
.def("onRspQryInvestor", &TdApi::onRspQryInvestor)
.def("onRspQryTradingCode", &TdApi::onRspQryTradingCode)
.def("onRspQryInstrumentMarginRate", &TdApi::onRspQryInstrumentMarginRate)
.def("onRspQryInstrumentCommissionRate", &TdApi::onRspQryInstrumentCommissionRate)
.def("onRspQryExchange", &TdApi::onRspQryExchange)
.def("onRspQryProduct", &TdApi::onRspQryProduct)
.def("onRspQryInstrument", &TdApi::onRspQryInstrument)
.def("onRspQryDepthMarketData", &TdApi::onRspQryDepthMarketData)
.def("onRspQrySettlementInfo", &TdApi::onRspQrySettlementInfo)
.def("onRspQryTransferBank", &TdApi::onRspQryTransferBank)
.def("onRspQryInvestorPositionDetail", &TdApi::onRspQryInvestorPositionDetail)
.def("onRspQryNotice", &TdApi::onRspQryNotice)
.def("onRspQrySettlementInfoConfirm", &TdApi::onRspQrySettlementInfoConfirm)
.def("onRspQryInvestorPositionCombineDetail", &TdApi::onRspQryInvestorPositionCombineDetail)
.def("onRspQryCFMMCTradingAccountKey", &TdApi::onRspQryCFMMCTradingAccountKey)
.def("onRspQryEWarrantOffset", &TdApi::onRspQryEWarrantOffset)
.def("onRspQryInvestorProductGroupMargin", &TdApi::onRspQryInvestorProductGroupMargin)
.def("onRspQryExchangeMarginRate", &TdApi::onRspQryExchangeMarginRate)
.def("onRspQryExchangeMarginRateAdjust", &TdApi::onRspQryExchangeMarginRateAdjust)
.def("onRspQryExchangeRate", &TdApi::onRspQryExchangeRate)
.def("onRspQrySecAgentACIDMap", &TdApi::onRspQrySecAgentACIDMap)
.def("onRspQryProductExchRate", &TdApi::onRspQryProductExchRate)
.def("onRspQryProductGroup", &TdApi::onRspQryProductGroup)
.def("onRspQryMMInstrumentCommissionRate", &TdApi::onRspQryMMInstrumentCommissionRate)
.def("onRspQryMMOptionInstrCommRate", &TdApi::onRspQryMMOptionInstrCommRate)
.def("onRspQryInstrumentOrderCommRate", &TdApi::onRspQryInstrumentOrderCommRate)
.def("onRspQrySecAgentTradingAccount", &TdApi::onRspQrySecAgentTradingAccount)
.def("onRspQrySecAgentCheckMode", &TdApi::onRspQrySecAgentCheckMode)
.def("onRspQrySecAgentTradeInfo", &TdApi::onRspQrySecAgentTradeInfo)
.def("onRspQryOptionInstrTradeCost", &TdApi::onRspQryOptionInstrTradeCost)
.def("onRspQryOptionInstrCommRate", &TdApi::onRspQryOptionInstrCommRate)
.def("onRspQryExecOrder", &TdApi::onRspQryExecOrder)
.def("onRspQryForQuote", &TdApi::onRspQryForQuote)
.def("onRspQryQuote", &TdApi::onRspQryQuote)
.def("onRspQryOptionSelfClose", &TdApi::onRspQryOptionSelfClose)
.def("onRspQryInvestUnit", &TdApi::onRspQryInvestUnit)
.def("onRspQryCombInstrumentGuard", &TdApi::onRspQryCombInstrumentGuard)
.def("onRspQryCombAction", &TdApi::onRspQryCombAction)
.def("onRspQryTransferSerial", &TdApi::onRspQryTransferSerial)
.def("onRspQryAccountregister", &TdApi::onRspQryAccountregister)
.def("onRspError", &TdApi::onRspError)
.def("onRtnOrder", &TdApi::onRtnOrder)
.def("onRtnTrade", &TdApi::onRtnTrade)
.def("onErrRtnOrderInsert", &TdApi::onErrRtnOrderInsert)
.def("onErrRtnOrderAction", &TdApi::onErrRtnOrderAction)
.def("onRtnInstrumentStatus", &TdApi::onRtnInstrumentStatus)
.def("onRtnBulletin", &TdApi::onRtnBulletin)
.def("onRtnTradingNotice", &TdApi::onRtnTradingNotice)
.def("onRtnErrorConditionalOrder", &TdApi::onRtnErrorConditionalOrder)
.def("onRtnExecOrder", &TdApi::onRtnExecOrder)
.def("onErrRtnExecOrderInsert", &TdApi::onErrRtnExecOrderInsert)
.def("onErrRtnExecOrderAction", &TdApi::onErrRtnExecOrderAction)
.def("onErrRtnForQuoteInsert", &TdApi::onErrRtnForQuoteInsert)
.def("onRtnQuote", &TdApi::onRtnQuote)
.def("onErrRtnQuoteInsert", &TdApi::onErrRtnQuoteInsert)
.def("onErrRtnQuoteAction", &TdApi::onErrRtnQuoteAction)
.def("onRtnForQuoteRsp", &TdApi::onRtnForQuoteRsp)
.def("onRtnCFMMCTradingAccountToken", &TdApi::onRtnCFMMCTradingAccountToken)
.def("onErrRtnBatchOrderAction", &TdApi::onErrRtnBatchOrderAction)
.def("onRtnOptionSelfClose", &TdApi::onRtnOptionSelfClose)
.def("onErrRtnOptionSelfCloseInsert", &TdApi::onErrRtnOptionSelfCloseInsert)
.def("onErrRtnOptionSelfCloseAction", &TdApi::onErrRtnOptionSelfCloseAction)
.def("onRtnCombAction", &TdApi::onRtnCombAction)
.def("onErrRtnCombActionInsert", &TdApi::onErrRtnCombActionInsert)
.def("onRspQryContractBank", &TdApi::onRspQryContractBank)
.def("onRspQryParkedOrder", &TdApi::onRspQryParkedOrder)
.def("onRspQryParkedOrderAction", &TdApi::onRspQryParkedOrderAction)
.def("onRspQryTradingNotice", &TdApi::onRspQryTradingNotice)
.def("onRspQryBrokerTradingParams", &TdApi::onRspQryBrokerTradingParams)
.def("onRspQryBrokerTradingAlgos", &TdApi::onRspQryBrokerTradingAlgos)
.def("onRspQueryCFMMCTradingAccountToken", &TdApi::onRspQueryCFMMCTradingAccountToken)
.def("onRtnFromBankToFutureByBank", &TdApi::onRtnFromBankToFutureByBank)
.def("onRtnFromFutureToBankByBank", &TdApi::onRtnFromFutureToBankByBank)
.def("onRtnRepealFromBankToFutureByBank", &TdApi::onRtnRepealFromBankToFutureByBank)
.def("onRtnRepealFromFutureToBankByBank", &TdApi::onRtnRepealFromFutureToBankByBank)
.def("onRtnFromBankToFutureByFuture", &TdApi::onRtnFromBankToFutureByFuture)
.def("onRtnFromFutureToBankByFuture", &TdApi::onRtnFromFutureToBankByFuture)
.def("onRtnRepealFromBankToFutureByFutureManual", &TdApi::onRtnRepealFromBankToFutureByFutureManual)
.def("onRtnRepealFromFutureToBankByFutureManual", &TdApi::onRtnRepealFromFutureToBankByFutureManual)
.def("onRtnQueryBankBalanceByFuture", &TdApi::onRtnQueryBankBalanceByFuture)
.def("onErrRtnBankToFutureByFuture", &TdApi::onErrRtnBankToFutureByFuture)
.def("onErrRtnFutureToBankByFuture", &TdApi::onErrRtnFutureToBankByFuture)
.def("onErrRtnRepealBankToFutureByFutureManual", &TdApi::onErrRtnRepealBankToFutureByFutureManual)
.def("onErrRtnRepealFutureToBankByFutureManual", &TdApi::onErrRtnRepealFutureToBankByFutureManual)
.def("onErrRtnQueryBankBalanceByFuture", &TdApi::onErrRtnQueryBankBalanceByFuture)
.def("onRtnRepealFromBankToFutureByFuture", &TdApi::onRtnRepealFromBankToFutureByFuture)
.def("onRtnRepealFromFutureToBankByFuture", &TdApi::onRtnRepealFromFutureToBankByFuture)
.def("onRspFromBankToFutureByFuture", &TdApi::onRspFromBankToFutureByFuture)
.def("onRspFromFutureToBankByFuture", &TdApi::onRspFromFutureToBankByFuture)
.def("onRspQueryBankAccountMoneyByFuture", &TdApi::onRspQueryBankAccountMoneyByFuture)
.def("onRtnOpenAccountByBank", &TdApi::onRtnOpenAccountByBank)
.def("onRtnCancelAccountByBank", &TdApi::onRtnCancelAccountByBank)
.def("onRtnChangeAccountByBank", &TdApi::onRtnChangeAccountByBank)
;
