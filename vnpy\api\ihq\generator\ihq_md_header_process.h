void processFrontConnected(Task *task);

void processFrontDisconnected(Task *task);

void processHeartBeatWarning(Task *task);

void processRspError(Task *task);

void processRspUserLogin(Task *task);

void processRspUserLogout(Task *task);

void processRspSubAllMarketData(Task *task);

void processRspUnSubAllMarketData(Task *task);

void processRspSubAllTickByTick(Task *task);

void processRspUnSubAllTickByTick(Task *task);

void processRspSubMarketData(Task *task);

void processRspUnSubMarketData(Task *task);

void processRspSubTickByTick(Task *task);

void processRspUnSubTickByTick(Task *task);

void processRtnDepthMarketData(Task *task);

void processRtnTickByTick(Task *task);

void processRtnOrderBook(Task *task);

void processRspSubOrderBook(Task *task);

void processRspUnSubOrderBook(Task *task);

