TStockFtdcErrorIDType = "int"
TStockFtdcUserIDType = "string"
TStockFtdcClOrdIDType = "int"
TStockFtdcSecurityIDType = "string"
TStockFtdcPriceType = "double"
TStockFtdcMoneyType = "double"
TStockFtdcVolumeType = "int"
TStockFtdcPartyIDType = "string"
TStockFtdcQuoteReqIDType = "string"
TStockFtdcOrderIDType = "string"
TStockFtdcQuoteReqIDType = "string"
TStockFtdcTimeType = "string"
TStockFtdcDateType = "string"
TStockFtdcTradeVolumeType = "int"
TStockFtdcTradeIDType = "string"
TStockFtdcExecIDType = "string"
TStockFtdcErrorMsgType = "string"
TStockFtdcTradingSystemNameType = "string"
TStockFtdcUserIDType = "string"
TStockFtdcPasswordType = "string"
TStockFtdcProductInfoType = "string"
TStockFtdcProtocolInfoType = "string"
TStockFtdcDataCenterIDType = "int"
TStockFtdcSequenceNoType = "int"
TStockFtdcOrdRejReasonType = "int"
TStockFtdcOwnerTypeType = "int"
TStockFtdcSideType = "char"
TStockFtdcLockType = "char"
TStockFtdcPositionEffectType = "char"
TStockFtdcOrdTypeType = "char"
TStockFtdcTimeInForceType = "char"
TStockFtdcCoveredOrUncoveredType = "char"
TStockFtdcExecTypeType = "char"
TStockFtdcOrdStatusType = "char"
TStockFtdcHedgeFlagType = "char"
TStockFtdcOptionTypeType = "char"
TStockFtdcProductType = "char"
TStockFtdcCallOrPutType = "char"
TStockFtdcClientTypeType = "char"
TStockFtdcTradStatusType = "char"
