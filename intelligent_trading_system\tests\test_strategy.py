"""
策略管理模块测试
测试策略管理和参数优化功能
"""

import unittest
import os
import sys
from unittest.mock import Mock

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from modules.strategy.strategy_manager import StrategyManager
from modules.strategy.parameter_optimizer import ParameterOptimizer


class TestStrategyModule(unittest.TestCase):
    """策略管理模块测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 使用Mock CTA引擎
        mock_cta_engine = Mock()
        self.strategy_manager = StrategyManager(mock_cta_engine)
        self.parameter_optimizer = ParameterOptimizer()
    
    def test_strategy_manager_initialization(self):
        """测试策略管理器初始化"""
        self.assertIsNotNone(self.strategy_manager)
        print("✓ 策略管理器初始化测试通过")
    
    def test_parameter_optimizer_initialization(self):
        """测试参数优化器初始化"""
        self.assertIsNotNone(self.parameter_optimizer)
        print("✓ 参数优化器初始化测试通过")
    
    def test_strategy_methods(self):
        """测试策略方法"""
        self.assertTrue(hasattr(self.strategy_manager, 'start_strategy'))
        self.assertTrue(hasattr(self.strategy_manager, 'stop_strategy'))
        self.assertTrue(hasattr(self.parameter_optimizer, 'optimize_parameters'))
        print("✓ 策略方法测试通过")


if __name__ == "__main__":
    unittest.main()
