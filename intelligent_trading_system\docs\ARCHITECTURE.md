# 智能量化交易系统架构文档

## 目录
1. [架构概述](#架构概述)
2. [系统分层](#系统分层)
3. [核心组件](#核心组件)
4. [数据流设计](#数据流设计)
5. [事件驱动架构](#事件驱动架构)
6. [并发处理](#并发处理)
7. [扩展性设计](#扩展性设计)
8. [安全性设计](#安全性设计)

## 架构概述

智能量化交易系统采用模块化、事件驱动的架构设计，基于vnpy框架构建，具有以下特点：

- **模块化设计**: 功能模块独立，便于维护和扩展
- **事件驱动**: 松耦合的事件通信机制
- **多进程架构**: 基于zdrun.py的父子进程模型
- **配置驱动**: 统一的配置管理系统
- **可扩展性**: 支持插件式功能扩展

### 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                    智能量化交易系统                              │
├─────────────────────────────────────────────────────────────────┤
│  应用层 (Application Layer)                                     │
│  ├─ 主控程序 (main_controller.py)                               │
│  ├─ 系统启动 (start_system.py)                                  │
│  └─ 测试程序 (test_main.py)                                     │
├─────────────────────────────────────────────────────────────────┤
│  业务层 (Business Layer)                                        │
│  ├─ 品种筛选 (Screening Module)                                 │
│  ├─ 资金分配 (Allocation Module)                                │
│  ├─ 合约切换 (Switching Module)                                 │
│  ├─ 策略管理 (Strategy Module)                                  │
│  ├─ 任务调度 (Scheduler Module)                                 │
│  └─ 系统监控 (Monitoring Module)                                │
├─────────────────────────────────────────────────────────────────┤
│  服务层 (Service Layer)                                         │
│  ├─ 账户管理 (Account Manager)                                  │
│  ├─ 事件管理 (Event Manager)                                    │
│  ├─ 配置管理 (Config Manager)                                   │
│  └─ 日志管理 (Logger)                                           │
├─────────────────────────────────────────────────────────────────┤
│  框架层 (Framework Layer)                                       │
│  ├─ vnpy主引擎 (MainEngine)                                     │
│  ├─ vnpy事件引擎 (EventEngine)                                  │
│  ├─ CTA策略引擎 (CtaEngine)                                     │
│  └─ 数据库引擎 (DatabaseManager)                                │
├─────────────────────────────────────────────────────────────────┤
│  基础设施层 (Infrastructure Layer)                              │
│  ├─ CTP网关 (CTP Gateway)                                       │
│  ├─ 数据库 (SQLite/MySQL)                                       │
│  ├─ 文件系统 (File System)                                      │
│  └─ 网络通信 (Network)                                          │
└─────────────────────────────────────────────────────────────────┘
```

## 系统分层

### 1. 应用层 (Application Layer)

应用层是系统的入口点，负责系统启动、进程管理和用户交互。

**主要组件:**
- `main_controller.py`: 主控程序，协调所有模块
- `start_system.py`: 系统启动脚本，支持多种运行模式
- `test_main.py`: 测试程序，验证系统功能

**设计原则:**
- 单一职责：每个程序专注特定功能
- 进程隔离：基于zdrun.py的多进程架构
- 模式支持：支持正常、测试、调试等多种模式

### 2. 业务层 (Business Layer)

业务层实现核心交易逻辑，包含六个主要功能模块。

#### 品种筛选模块 (Screening Module)
```
modules/screening/
├── futures_scanner.py      # 期货扫描器
├── evaluation_engine.py    # 评估引擎
└── __init__.py
```

**职责:**
- 获取市场数据
- 计算技术指标
- 评估品种质量
- 生成筛选结果

#### 资金分配模块 (Allocation Module)
```
modules/allocation/
├── capital_allocator.py    # 资金分配器
├── position_manager.py     # 仓位管理器
└── __init__.py
```

**职责:**
- 基于筛选结果分配资金
- 计算仓位大小
- 管理风险限制
- 调整现有仓位

#### 合约切换模块 (Switching Module)
```
modules/switching/
├── main_contract_detector.py  # 主力合约检测
├── contract_switcher.py       # 合约切换器
└── __init__.py
```

**职责:**
- 检测主力合约变化
- 执行合约切换
- 管理切换风险
- 记录切换历史

#### 策略管理模块 (Strategy Module)
```
modules/strategy/
├── strategy_manager.py     # 策略管理器
├── parameter_optimizer.py  # 参数优化器
└── __init__.py
```

**职责:**
- 管理策略生命周期
- 优化策略参数
- 监控策略表现
- 动态调整策略

#### 任务调度模块 (Scheduler Module)
```
modules/scheduler/
├── task_scheduler.py       # 任务调度器
├── event_manager.py        # 事件管理器
└── __init__.py
```

**职责:**
- 定时任务调度
- 事件驱动处理
- 任务依赖管理
- 异常处理和重试

#### 系统监控模块 (Monitoring Module)
```
modules/monitoring/
├── system_monitor.py       # 系统监控器
├── alert_manager.py        # 告警管理器
└── __init__.py
```

**职责:**
- 系统性能监控
- 交易指标监控
- 异常检测和告警
- 健康状态检查

### 3. 服务层 (Service Layer)

服务层提供通用服务，支撑业务层功能。

#### 账户管理 (Account Manager)
```python
class AccountManager:
    """账户管理服务"""
    - CTP连接管理
    - 账户信息获取
    - 持仓和委托查询
    - 登录状态维护
```

#### 事件管理 (Event Manager)
```python
class EventManager:
    """事件管理服务"""
    - 事件注册和分发
    - 异步事件处理
    - 事件优先级管理
    - 事件历史记录
```

#### 配置管理 (Config Manager)
```python
class ConfigManager:
    """配置管理服务"""
    - 配置文件加载
    - 配置验证
    - 动态配置更新
    - 配置版本管理
```

### 4. 框架层 (Framework Layer)

框架层基于vnpy框架，提供交易系统基础功能。

**核心组件:**
- **MainEngine**: vnpy主引擎，管理所有网关和应用
- **EventEngine**: vnpy事件引擎，处理系统事件
- **CtaEngine**: CTA策略引擎，执行交易策略
- **DatabaseManager**: 数据库管理器，处理数据存储

### 5. 基础设施层 (Infrastructure Layer)

基础设施层提供底层技术支撑。

**组件:**
- **CTP Gateway**: CTP接口网关
- **Database**: 数据库系统 (SQLite/MySQL)
- **File System**: 文件系统
- **Network**: 网络通信

## 核心组件

### 主控制器 (Main Controller)

主控制器是系统的核心协调者，负责：

```python
class IntelligentTradingSystem:
    """智能交易系统主控制器"""
    
    def __init__(self):
        self.main_engine = None          # vnpy主引擎
        self.account_manager = None      # 账户管理器
        self.event_manager = None        # 事件管理器
        self.task_scheduler = None       # 任务调度器
        self.system_monitor = None       # 系统监控器
        
        # 业务模块
        self.futures_scanner = None      # 期货扫描器
        self.evaluation_engine = None    # 评估引擎
        self.capital_allocator = None    # 资金分配器
        self.position_manager = None     # 仓位管理器
        self.contract_detector = None    # 合约检测器
        self.contract_switcher = None    # 合约切换器
        self.strategy_manager = None     # 策略管理器
        self.parameter_optimizer = None  # 参数优化器
        self.alert_manager = None        # 告警管理器
```

### 配置系统

配置系统采用分层设计：

```
config/
├── system_config.py        # 系统配置
├── trading_config.py       # 交易配置
└── risk_config.py          # 风险配置
```

**配置特点:**
- 类型安全：使用dataclass定义
- 验证机制：内置配置验证
- 默认值：提供合理默认配置
- 环境隔离：支持不同环境配置

### 事件系统

事件系统实现松耦合通信：

```python
# 事件类型定义
class EventType(Enum):
    SYSTEM_START = "system_start"
    SCAN_COMPLETE = "scan_complete"
    ALLOCATION_COMPLETE = "allocation_complete"
    # ... 更多事件类型

# 事件处理流程
EventManager.register_handler(EventType.SCAN_COMPLETE, handle_scan_complete)
EventManager.emit_event(EventType.SCAN_COMPLETE, scan_results, "FuturesScanner")
```

## 数据流设计

### 主要数据流

```
1. 市场数据流:
   CTP网关 → vnpy引擎 → 数据库 → 筛选模块 → 评估引擎

2. 筛选结果流:
   评估引擎 → 资金分配器 → 仓位管理器 → 策略管理器

3. 交易指令流:
   策略管理器 → CTA引擎 → CTP网关 → 交易所

4. 监控数据流:
   各模块 → 系统监控器 → 告警管理器 → 外部通知
```

### 数据存储设计

```
数据库表结构:
├── bar_data           # K线数据
├── tick_data          # tick数据
├── contract_data      # 合约信息
├── position_data      # 持仓数据
├── order_data         # 委托数据
├── trade_data         # 成交数据
├── strategy_data      # 策略数据
├── scan_results       # 筛选结果
├── allocation_results # 分配结果
└── system_logs        # 系统日志
```

### 缓存策略

```python
# 多级缓存设计
class CacheManager:
    """缓存管理器"""
    
    # L1缓存：内存缓存 (5分钟)
    memory_cache = {}
    
    # L2缓存：Redis缓存 (1小时)
    redis_cache = None
    
    # L3缓存：数据库缓存 (持久化)
    database_cache = None
```

## 事件驱动架构

### 事件驱动模型

系统采用发布-订阅模式实现事件驱动：

```python
# 事件发布者
class EventPublisher:
    def emit_event(self, event_type, data, source):
        event = Event(event_type, data, source, timestamp=datetime.now())
        self.event_queue.put(event)

# 事件订阅者
class EventSubscriber:
    def register_handler(self, event_type, handler):
        self.handlers[event_type].append(handler)
    
    def handle_event(self, event):
        for handler in self.handlers[event.type]:
            handler(event)
```

### 事件处理流程

```
1. 事件产生 → 2. 事件队列 → 3. 事件分发 → 4. 事件处理 → 5. 结果反馈
     ↓              ↓              ↓              ↓              ↓
  业务模块      EventManager    事件路由器      事件处理器      业务逻辑
```

### 关键事件类型

```python
# 系统生命周期事件
SYSTEM_START, SYSTEM_STOP, SYSTEM_ERROR

# 业务流程事件
SCAN_START, SCAN_COMPLETE, SCAN_ERROR
ALLOCATION_START, ALLOCATION_COMPLETE, ALLOCATION_ERROR
STRATEGY_START, STRATEGY_STOP, STRATEGY_ERROR

# 交易事件
ORDER_SENT, ORDER_FILLED, ORDER_CANCELLED
POSITION_OPENED, POSITION_CLOSED, POSITION_UPDATED

# 风险事件
RISK_WARNING, RISK_LIMIT_EXCEEDED, EMERGENCY_STOP
```

## 并发处理

### 多进程架构

基于zdrun.py的父子进程模型：

```python
# 父进程：监控和管理
class ParentProcess:
    def monitor_child_process(self):
        """监控子进程状态"""
        while self.running:
            if not self.child_process.is_alive():
                self.restart_child_process()
            time.sleep(1)

# 子进程：业务逻辑
class ChildProcess:
    def run_trading_logic(self):
        """运行交易逻辑"""
        system = IntelligentTradingSystem()
        system.start()
```

### 多线程处理

```python
# 线程池管理
class ThreadPoolManager:
    def __init__(self, max_workers=4):
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
    
    def submit_task(self, func, *args, **kwargs):
        """提交异步任务"""
        return self.executor.submit(func, *args, **kwargs)
```

### 异步任务处理

```python
# 异步任务队列
class AsyncTaskQueue:
    def __init__(self):
        self.task_queue = Queue()
        self.worker_threads = []
    
    def add_task(self, task):
        """添加任务到队列"""
        self.task_queue.put(task)
    
    def process_tasks(self):
        """处理任务队列"""
        while True:
            task = self.task_queue.get()
            self.execute_task(task)
```

## 扩展性设计

### 插件架构

```python
# 插件基类
class PluginBase:
    """插件基类"""
    
    def initialize(self):
        """初始化插件"""
        pass
    
    def execute(self, *args, **kwargs):
        """执行插件逻辑"""
        pass
    
    def cleanup(self):
        """清理插件资源"""
        pass

# 插件管理器
class PluginManager:
    def load_plugin(self, plugin_path):
        """动态加载插件"""
        pass
    
    def register_plugin(self, plugin_name, plugin_class):
        """注册插件"""
        pass
```

### 接口标准化

```python
# 标准化接口
class ScannerInterface:
    """扫描器接口"""
    
    def scan_symbols(self) -> Dict[str, float]:
        raise NotImplementedError
    
    def get_indicators(self, symbol: str) -> Dict[str, float]:
        raise NotImplementedError

class AllocatorInterface:
    """分配器接口"""
    
    def allocate_capital(self, results: Dict) -> Dict:
        raise NotImplementedError
```

### 配置驱动扩展

```python
# 扩展配置
@dataclass
class ExtensionConfig:
    enabled_plugins: List[str]
    plugin_configs: Dict[str, Dict]
    custom_indicators: List[str]
    custom_strategies: List[str]
```

## 安全性设计

### 访问控制

```python
# 权限管理
class PermissionManager:
    def check_permission(self, user, action, resource):
        """检查用户权限"""
        pass
    
    def grant_permission(self, user, permission):
        """授予权限"""
        pass
```

### 数据安全

```python
# 数据加密
class DataEncryption:
    def encrypt_sensitive_data(self, data):
        """加密敏感数据"""
        pass
    
    def decrypt_sensitive_data(self, encrypted_data):
        """解密敏感数据"""
        pass
```

### 审计日志

```python
# 审计日志
class AuditLogger:
    def log_action(self, user, action, resource, result):
        """记录用户操作"""
        audit_record = {
            'timestamp': datetime.now(),
            'user': user,
            'action': action,
            'resource': resource,
            'result': result,
            'ip_address': self.get_client_ip()
        }
        self.save_audit_record(audit_record)
```

### 风险控制

```python
# 多层风险控制
class RiskController:
    def pre_trade_check(self, order):
        """交易前风险检查"""
        pass
    
    def real_time_monitor(self):
        """实时风险监控"""
        pass
    
    def emergency_stop(self):
        """紧急停止"""
        pass
```

通过以上架构设计，系统具备了：
- **高可用性**: 多进程架构保证系统稳定性
- **高性能**: 多线程和异步处理提升效率
- **可扩展性**: 插件架构支持功能扩展
- **可维护性**: 模块化设计便于维护
- **安全性**: 多层安全机制保护系统

这种架构设计确保了系统能够在复杂的交易环境中稳定运行，同时为未来的功能扩展和性能优化提供了良好的基础。
