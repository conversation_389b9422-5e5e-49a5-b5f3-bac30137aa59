ERROR_SUCCEED = 0
OMS_NORMSL = 'N'
OMS_NOT_WORK = 'D'
IS_ENCRYPT_YES = True
IS_ENCRYPT_NO = False
CA_LOGIN = 'Y'
NOT_CA_LOGIN = 'N'
FORCE_PWD = 'Y'
NOT_FORCE_PWD = 'N'
PASSWORD_TYPE_TRADE = 'T'
PASSWORD_TYPE_QUOTE = 'Q'
PASSWORD_TYPE_AUTH = 'A'
QRY_NOTIFY_ALL = 0
QRY_NOTIFY_SPECIFIC = 1
EXCHANGE_STATE_YES = 'Y'
EXCHANGE_STATE_NO = 'N'
EXCHANGE_STATE_COVER = 'C'
EXCHANGE_STATE_OPEN = 'O'
EXCHANGE_STATE_SETTLE = 'S'
COMMODITY_TYPE_GOODS = 'G'
COMMODITY_TYPE_FUTURE = 'F'
COMMODITY_TYPE_OPTION = 'O'
COMMODITY_TYPE_SPREAD_MONTH = 'M'
COMMODITY_TYPE_SPREAD_COMMODITY = 'C'
COMMODITY_STATE_YES = 'Y'
COMMODITY_STATE_NO = 'N'
COMMODITY_STATE_COVER = 'C'
DELIVERY_MODE_GOODS = 'G'
DELIVERY_MODE_CASH = 'C'
DELIVERY_MODE_EXECUTE = 'E'
DEPOSIT_CALCULATE_MODE_NORMAL = 'N'
DEPOSIT_CALCULATE_MODE_CLEAN = 'C'
DEPOSIT_CALCULATE_MODE_LOCK = 'L'
CONTRACT_TYPE_SINGLE = '0'
CONTRACT_TYPE_SPREAD = '1'
CONTRACT_TYPE_SWAP = '2'
CONTRACT_TYPE_COMMODITY = '3'
CONTRACT_STATE_YES = 'Y'
CONTRACT_STATE_NO = 'N'
CONTRACT_STATE_COVER = 'C'
CONTRACT_TRADE_STATE_UNKNOW = -1
CONTRACT_TRADE_STATE_OPEN = 0
CONTRACT_TRADE_STATE_NOBONUS = 1
CONTRACT_TRADE_STATE_AUCTION = 2
CONTRACT_TRADE_STATE_HALT = 3
CONTRACT_TRADE_STATE_CLOSE = 4
CONTRACT_TRADE_STATE_BEFORE_OPEN = 5
CONTRACT_TRADE_STATE_BEFORE_CLOSE = 6
CONTRACT_TRADE_STATE_FAST = 7
RISK_ORDER_YES = 'Y'
RISK_ORDER_NO = 'N'
DIRECT_BUY = 'B'
DIRECT_SELL = 'S'
OFFSET_NONE = 'N'
OFFSET_OPEN = 'O'
OFFSET_COVER = 'C'
OFFSET_COVER_TODAY = 'T'
COVER_MODE_NONE = 'N'
COVER_MODE_UNFINISHED = 'U'
COVER_MODE_OPENCOVER = 'C'
COVER_MODE_COVERTODAY = 'T'
HEDGE_NONE = 'N'
HEDGE_T = 'T'
HEDGE_B = 'B'
MANUALFEE_YES = 'Y'
MANUALFEE_NO = 'N'
ORDER_TYPE_LIMIT = '1'
ORDER_TYPE_MARKET = '2'
ORDER_TYPE_STOP_LIMIT = '3'
ORDER_TYPE_STOP_MARKET = '4'
ORDER_WAY_SELF_ETRADER = 'E'
ORDER_WAY_PROXY_ETRADER = 'P'
ORDER_WAY_JTRADER = 'J'
ORDER_WAY_MANUAL = 'M'
ORDER_WAY_CARRY = 'C'
ORDER_WAY_PROGRAM = 'S'
ORDER_MODE_FOK = '1'
ORDER_MODE_FAK = '2'
ORDER_MODE_GFD = '3'
ORDER_MODE_GTC = '4'
ORDER_MODE_GTD = '5'
ORDER_INPUT_YES = 'Y'
ORDER_INPUT_NO = 'N'
ORDER_STATE_FAIL = '0'
ORDER_STATE_ACCEPT = '1'
ORDER_STATE_SUPPENDED = '2'
ORDER_STATE_QUEUED = '3'
ORDER_STATE_DELETEING = '4'
ORDER_STATE_MODIFYING = '5'
ORDER_STATE_PARTDELETED = '6'
ORDER_STATE_DELETED = '7'
ORDER_STATE_PARTFINISHED = '8'
ORDER_STATE_FINISHED = '9'
DEL_YES = 'Y'
DEL_NO = 'N'
DEL_DISAPPEAR = 'D'
MATCH_WAY_ALL = 'A'
MATCH_WAY_SELF_ETRADER = 'E'
MATCH_WAY_PROXY_ETRADER = 'P'
MATCH_WAY_JTRADER = 'J'
MATCH_WAY_MANUAL = 'M'
MATCH_WAY_CARRY = 'C'
MATCH_WAY_DELIVERY = 'D'
MATCH_WAY_PROGRAM = 'S'
MATCH_MODE_NORMAL = 'N'
MATCH_MODE_UPDATE = 'U'
MATCH_MODE_OTHER = 'O'
ADD_ONE_YES = 'Y'
ADD_ONE_NO = 'N'
CURRENCY_PRIMARY_YES = 'Y'
CURRENCY_PRIMARY_NO = 'N'
CURRENCY_SHARE_YES = 'Y'
CURRENCY_SHARE_NO = 'N'
ContainTotle_Yes = 'Y'
ContainTotle_No = 'N'
LWFlag_L = 'L'
LWFlag_W = 'W'
LJFFlag_J = 'J'
LJFFlag_F = 'F'
DEPOSIT_MODE_B = '1'
DEPOSIT_MODE_D = '2'
DEPOSIT_MODE_CB = '3'
DEPOSIT_MODE_CD = '4'
DEPOSIT_MODE_Z = '5'
CASH_TYPE_OUT = 'O'
CASH_TYPE_IN = 'I'
CASH_STATE_NOT_CHECK = 'N'
CASH_STATE_CHECK = 'Y'
CASH_STATE_FAIL = 'F'
CASH_MODE_A = 'A'
CASH_MODE_B = 'B'
CASH_MODE_C = 'C'
CASH_MODE_E = 'E'
ADJUST_STATE_NOT_CHECK = 'N'
ADJUST_STATE_CHECK = 'Y'
ADJUST_STATE_FAIL = 'F'
MONEY_CHG_ADJUST = 0x00000001
MONEY_CHG_CASHIN = 0x00000002
MONEY_CHG_CASHOUT = 0x00000004
MONEY_CHG_FEE = 0x00000008
MONEY_CHG_FROZEN = 0x00000010
MONEY_CHG_COVERPROFIT = 0x00000020
MONEY_CHG_DAYCVERPROFIT = 0x00000040
MONEY_CHG_FLOATPROFIT = 0x00000080
MONEY_CHG_DAYFLOATPROFIT = 0x00000100
MONEY_CHG_UNEXPIREDPROFIT = 0x00000200
MONEY_CHG_PREMIUM = 0x00000400
MONEY_CHG_DEPOSIT = 0x00000800
MONEY_CHG_KEEPDEPOSIT = 0x00001000
MONEY_CHG_PLEDGE = 0x00002000
MONEY_CHG_TAVAILABLE = 0x00004000
