#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
期货分钟级K线品种筛选器
专为突破策略设计，支持多周期验证和实时波动分析
"""

import sys
import os
# 添加父目录到Python路径，以便导入vnpy模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
import talib
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
import warnings
from typing import Dict, List, Tuple, Optional
from contract_info import ContractInfo
from futures_screening_system.system_config import is_blacklisted, get_blacklist_reason, get_scanner_config

# 导入数据库备份功能
try:
    from auto_trading_system.core.database_backup import create_screening_backup, get_backup_database_path
    DATABASE_BACKUP_AVAILABLE = True
except ImportError:
    print("警告: 数据库备份模块不可用，将使用原始数据库")
    DATABASE_BACKUP_AVAILABLE = False

# vnpy相关导入在需要时动态导入

warnings.filterwarnings('ignore')

class FuturesMinuteScanner:
    """期货分钟级K线品种筛选器"""
    
    def __init__(self, contract_info_path: str = "期货全品种手续费保证金.xls"):
        """初始化筛选器

        Args:
            contract_info_path: 合约信息文件路径
        """
        # 从contract_info.py获取品种信息
        print(f"正在初始化合约信息，文件路径: {contract_info_path}")
        self.contract_info = ContractInfo(contract_info_path)
        print("合约信息初始化完成，正在获取品种列表...")
        self.symbols = self._get_futures_symbols()

        # 从统一配置文件加载参数
        scanner_config = get_scanner_config()
        self.config = self._flatten_config(scanner_config)
        
        # 缓存机制
        self.data_cache = {}
        self.cache_timeout = 300  # 5分钟缓存超时
        
        print(f"初始化完成，共发现 {len(self.symbols)} 个期货品种")

    def _flatten_config(self, scanner_config: dict) -> dict:
        """扁平化配置结构，便于代码使用"""
        config = {}

        # 趋势过滤器参数
        trend_filters = scanner_config.get('trend_filters', {})
        config.update({
            'adx_threshold': trend_filters.get('adx_threshold', 25),
            'adx_min_threshold': trend_filters.get('adx_min_threshold', 10),
            'ema_periods': trend_filters.get('ema_periods', [12, 36, 72]),
            'di_period': trend_filters.get('di_period', 14),
        })

        # 波动率过滤器参数
        volatility_filters = scanner_config.get('volatility_filters', {})
        config.update({
            'atr_multiplier': volatility_filters.get('atr_multiplier', 1.8),
            'atr_period': volatility_filters.get('atr_period', 14),
            'bb_expansion_threshold': volatility_filters.get('bb_expansion_threshold', 25),
            'bb_period': volatility_filters.get('bb_period', 20),
            'vix_threshold': volatility_filters.get('vix_threshold', 20),
        })

        # 突破信号参数
        breakout_signals = scanner_config.get('breakout_signals', {})
        config.update({
            'donchian_period': breakout_signals.get('donchian_period', 20),
            'macd_fast': breakout_signals.get('macd_fast', 12),
            'macd_slow': breakout_signals.get('macd_slow', 26),
            'macd_signal': breakout_signals.get('macd_signal', 9),
            'kdj_periods': [
                breakout_signals.get('kdj_k_period', 9),
                breakout_signals.get('kdj_slow_k', 3),
                breakout_signals.get('kdj_slow_d', 3)
            ],
        })

        # 排除条件参数
        exclusion_criteria = scanner_config.get('exclusion_criteria', {})
        config.update({
            'min_amplitude_multiplier': exclusion_criteria.get('min_amplitude_multiplier', 1.0),
            'min_volume': exclusion_criteria.get('min_volume', 3000),
        })

        # 评分权重
        scoring_weights = scanner_config.get('scoring_weights', {})
        config['score_weights'] = {
            'atr_expansion': scoring_weights.get('atr_expansion_weight', 0.4),
            'bb_expansion': scoring_weights.get('bb_expansion_weight', 0.3),
            'volume_change': scoring_weights.get('volume_change_weight', 0.3)
        }

        # 评级标准
        rating_criteria = scanner_config.get('rating_criteria', {})
        config['rating_criteria'] = {
            'excellent_threshold': rating_criteria.get('excellent_threshold', 60),
            'good_threshold': rating_criteria.get('good_threshold', 40),
            'fair_threshold': rating_criteria.get('fair_threshold', 25),
        }

        return config
    
    def _get_futures_symbols(self) -> List[str]:
        """从contract_info获取期货品种列表并应用黑名单过滤"""
        all_symbols = []

        try:
            # 尝试从contract_info中获取888合约列表
            symbols = self.contract_info.get_888_contracts()
            if symbols:
                print(f"从合约信息中获取到 {len(symbols)} 个品种")
                all_symbols = symbols
        except Exception as e:
            print(f"从合约信息获取品种失败: {e}")

        # 如果获取失败，使用默认的品种列表
        if not all_symbols:
            print("使用默认品种列表")
            for product, exchange in self.contract_info.EXCHANGE_MAP.items():
                # 根据交易所格式化品种代码
                if exchange in ["CFFEX", "CZCE"]:
                    formatted_product = product.upper()
                else:
                    formatted_product = product.lower()

                symbol = f"{formatted_product}888.{exchange}"
                all_symbols.append(symbol)

            print(f"生成默认品种列表: {len(all_symbols)} 个品种")

        # 应用黑名单过滤
        filtered_symbols = []
        blacklisted_symbols = []

        for symbol in all_symbols:
            if is_blacklisted(symbol):
                blacklisted_symbols.append({
                    'symbol': symbol,
                    'reason': get_blacklist_reason(symbol)
                })
            else:
                filtered_symbols.append(symbol)

        # 显示黑名单信息
        if blacklisted_symbols:
            print(f"\n⚠️ 黑名单品种排除:")
            for item in blacklisted_symbols:
                print(f"  - {item['symbol']}: {item['reason']}")

        print(f"黑名单过滤前: {len(all_symbols)} 个品种")
        print(f"黑名单过滤后: {len(filtered_symbols)} 个品种")

        return filtered_symbols
    
    def _get_cache_key(self, symbol: str, period: str) -> str:
        """生成缓存键"""
        return f"{symbol}_{period}_{datetime.now().strftime('%Y%m%d_%H%M')}"
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.data_cache:
            return False
        
        cache_time = self.data_cache[cache_key].get('timestamp', 0)
        return (time.time() - cache_time) < self.cache_timeout
    
    def fetch_data(self, symbol: str, period: str = '30') -> Optional[pd.DataFrame]:
        """获取期货K线数据

        Args:
            symbol: 品种代码 (格式: cu888.SHFE)
            period: 周期 ('1', '5', '15', '30', 'D')

        Returns:
            DataFrame: K线数据
        """
        cache_key = self._get_cache_key(symbol, period)

        # 检查缓存
        if self._is_cache_valid(cache_key):
            return self.data_cache[cache_key]['data']

        try:
            # 从vnpy数据库获取1分钟数据
            df_1min = self._get_1min_data_from_vnpy(symbol)

            if df_1min is None or df_1min.empty:
                print(f"未找到 {symbol} 的1分钟数据")
                return None

            # 根据需要的周期合成数据
            if period == '1':
                df = df_1min
            elif period == 'D':
                df = self._resample_to_daily(df_1min)
            else:
                # 合成其他分钟周期
                df = self._resample_to_minutes(df_1min, int(period))

            if df is not None and not df.empty:
                # 标准化列名
                df = self._standardize_columns(df)

                # 缓存数据
                self.data_cache[cache_key] = {
                    'data': df,
                    'timestamp': time.time()
                }

                return df

        except Exception as e:
            print(f"获取 {symbol} {period}分钟数据失败: {e}")

        return None

    def _get_1min_data_from_vnpy(self, symbol: str) -> Optional[pd.DataFrame]:
        """从vnpy数据库获取1分钟数据

        Args:
            symbol: 品种代码 (格式: cu888.SHFE)

        Returns:
            DataFrame: 1分钟K线数据
        """
        try:
            # 解析symbol和exchange
            if '.' in symbol:
                symbol_part, exchange_str = symbol.split('.')
            else:
                symbol_part = symbol
                exchange_str = 'SHFE'

            # 导入vnpy相关模块
            from vnpy.trader.database import database_manager
            from vnpy.trader.constant import Exchange, Interval

            # 使用备份数据库（如果可用）
            if DATABASE_BACKUP_AVAILABLE:
                backup_db_path = get_backup_database_path()
                if backup_db_path:
                    print(f"使用备份数据库进行筛选: {backup_db_path}")
                    # 临时切换到备份数据库
                    original_db_path = database_manager.db_path
                    database_manager.db_path = str(backup_db_path)
                    database_manager.init()
                else:
                    print("备份数据库不可用，使用原始数据库")

            # 转换交易所
            exchange_map = {
                'SHFE': Exchange.SHFE,
                'DCE': Exchange.DCE,
                'CZCE': Exchange.CZCE,
                'CFFEX': Exchange.CFFEX,
                'INE': Exchange.INE,
                'GFEX': Exchange.GFEX
            }
            exchange = exchange_map.get(exchange_str, Exchange.SHFE)

            # 设置查询参数
            end_time = datetime.now()
            start_time = end_time - timedelta(days=50)  # 获取最近30天数据

            # 查询1分钟数据
            bars = database_manager.load_bar_data(
                symbol=symbol_part,
                exchange=exchange,
                interval=Interval.MINUTE,
                start=start_time,
                end=end_time
            )

            if not bars:
                print(f"未找到 {symbol} 的数据")
                return None

            # 转换为DataFrame
            data = []
            for bar in bars:
                data.append({
                    'datetime': bar.datetime,
                    'open': bar.open_price,
                    'high': bar.high_price,
                    'low': bar.low_price,
                    'close': bar.close_price,
                    'volume': bar.volume,
                    'turnover': getattr(bar, 'turnover', 0)
                })

            df = pd.DataFrame(data)
            if not df.empty:
                # 确保datetime列是datetime类型
                df['datetime'] = pd.to_datetime(df['datetime'])
                df.set_index('datetime', inplace=True)
                df.sort_index(inplace=True)

            print(f"成功获取 {symbol} 的 {len(df)} 根1分钟K线")
            return df

        except Exception as e:
            print(f"从vnpy数据库获取 {symbol} 数据失败: {e}")
            return None
        finally:
            # 恢复原始数据库连接（如果使用了备份数据库）
            if DATABASE_BACKUP_AVAILABLE and 'original_db_path' in locals():
                try:
                    database_manager.db_path = original_db_path
                    database_manager.init()
                except Exception as e:
                    print(f"恢复原始数据库连接失败: {e}")



    def _resample_to_minutes(self, df_1min: pd.DataFrame, minutes: int) -> pd.DataFrame:
        """将1分钟数据重采样为指定分钟周期

        Args:
            df_1min: 1分钟数据
            minutes: 目标分钟数

        Returns:
            DataFrame: 重采样后的数据
        """
        try:
            if df_1min.empty:
                return pd.DataFrame()

            # 确保索引是datetime类型
            if not isinstance(df_1min.index, pd.DatetimeIndex):
                print(f"数据索引不是DatetimeIndex: {type(df_1min.index)}")
                return pd.DataFrame()

            # 重采样规则
            rule = f'{minutes}T'

            # 执行重采样
            df_resampled = df_1min.resample(rule).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum',
                'turnover': 'sum'
            }).dropna()

            # 重置索引，将datetime作为列
            df_resampled.reset_index(inplace=True)

            return df_resampled

        except Exception as e:
            print(f"重采样到{minutes}分钟失败: {e}")
            return pd.DataFrame()

    def _resample_to_daily(self, df_1min: pd.DataFrame) -> pd.DataFrame:
        """将1分钟数据重采样为日线数据

        Args:
            df_1min: 1分钟数据

        Returns:
            DataFrame: 日线数据
        """
        try:
            if df_1min.empty:
                return pd.DataFrame()

            # 确保索引是datetime类型
            if not isinstance(df_1min.index, pd.DatetimeIndex):
                print(f"数据索引不是DatetimeIndex: {type(df_1min.index)}")
                return pd.DataFrame()

            # 按日期分组重采样
            df_daily = df_1min.resample('D').agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum',
                'turnover': 'sum'
            }).dropna()

            # 重置索引
            df_daily.reset_index(inplace=True)

            return df_daily

        except Exception as e:
            print(f"重采样到日线失败: {e}")
            return pd.DataFrame()
    

    
    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化DataFrame列名"""
        column_mapping = {
            'date': 'datetime',
            'time': 'datetime',
            '日期': 'datetime',
            '时间': 'datetime',
            '开盘价': 'open',
            '最高价': 'high', 
            '最低价': 'low',
            '收盘价': 'close',
            '成交量': 'volume',
            '持仓量': 'open_interest'
        }
        
        df = df.rename(columns=column_mapping)
        
        # 确保必要的列存在
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in df.columns:
                if col == 'volume':
                    df[col] = 1000  # 默认成交量
                else:
                    df[col] = df.get('close', 0)  # 使用收盘价作为默认值
        
        return df

    def calculate_trend_indicators(self, df_daily: pd.DataFrame, df_30m: pd.DataFrame) -> Dict:
        """计算趋势过滤指标

        Args:
            df_daily: 日线数据
            df_30m: 30分钟数据

        Returns:
            Dict: 趋势指标结果
        """
        result = {
            'daily_adx': 0,
            'ema_alignment': False,
            'di_direction': 0,  # 1=多头, -1=空头, 0=无方向
            'trend_score': 0
        }

        try:
            # 日线ADX计算
            if len(df_daily) >= 20:
                high = df_daily['high'].values
                low = df_daily['low'].values
                close = df_daily['close'].values

                adx = talib.ADX(high, low, close, timeperiod=14)
                result['daily_adx'] = adx[-1] if not np.isnan(adx[-1]) else 0

            # 30分钟EMA多头排列检查
            if len(df_30m) >= 72:
                close_30m = df_30m['close'].values
                ema12 = talib.EMA(close_30m, timeperiod=12)
                ema36 = talib.EMA(close_30m, timeperiod=36)
                ema72 = talib.EMA(close_30m, timeperiod=72)

                if not (np.isnan(ema12[-1]) or np.isnan(ema36[-1]) or np.isnan(ema72[-1])):
                    result['ema_alignment'] = ema12[-1] > ema36[-1] > ema72[-1]

            # 30分钟DI方向判断
            if len(df_30m) >= 20:
                high_30m = df_30m['high'].values
                low_30m = df_30m['low'].values
                close_30m = df_30m['close'].values

                plus_di = talib.PLUS_DI(high_30m, low_30m, close_30m, timeperiod=14)
                minus_di = talib.MINUS_DI(high_30m, low_30m, close_30m, timeperiod=14)

                if not (np.isnan(plus_di[-1]) or np.isnan(minus_di[-1])):
                    if plus_di[-1] > minus_di[-1]:
                        result['di_direction'] = 1  # 多头
                    elif plus_di[-1] < minus_di[-1]:
                        result['di_direction'] = -1  # 空头

            # 计算趋势综合评分
            score = 0
            if result['daily_adx'] > self.config['adx_threshold']:
                score += 30
            if result['ema_alignment']:
                score += 40
            if abs(result['di_direction']) == 1:
                score += 30

            result['trend_score'] = score

        except Exception as e:
            print(f"计算趋势指标失败: {e}")

        return result

    def calculate_volatility_indicators(self, df_15m: pd.DataFrame, df_30m: pd.DataFrame, df_5m: pd.DataFrame) -> Dict:
        """计算波动扩张指标

        Args:
            df_15m: 15分钟数据
            df_30m: 30分钟数据
            df_5m: 5分钟数据

        Returns:
            Dict: 波动指标结果
        """
        result = {
            'atr_expansion': 0,
            'bb_expansion': 0,
            'vix_score': 0,
            'volatility_score': 0
        }

        try:
            # 15分钟ATR扩张检测
            if len(df_15m) >= 24:
                high_15m = df_15m['high'].values
                low_15m = df_15m['low'].values
                close_15m = df_15m['close'].values

                atr = talib.ATR(high_15m, low_15m, close_15m, timeperiod=14)
                if len(atr) >= 11:
                    current_atr = atr[-1]
                    avg_atr = np.mean(atr[-11:-1])  # 过去10根ATR均值

                    if avg_atr > 0:
                        atr_ratio = current_atr / avg_atr
                        result['atr_expansion'] = max(0, (atr_ratio - 1) * 100)

            # 30分钟布林带扩张检测
            if len(df_30m) >= 30:
                close_30m = df_30m['close'].values

                bb_upper, _, bb_lower = talib.BBANDS(close_30m, timeperiod=20, nbdevup=2, nbdevdn=2)

                if len(bb_upper) >= 11:
                    current_width = bb_upper[-1] - bb_lower[-1]
                    avg_width = np.mean((bb_upper[-11:-1] - bb_lower[-11:-1]))

                    if avg_width > 0:
                        width_change = ((current_width - avg_width) / avg_width) * 100
                        result['bb_expansion'] = max(0, width_change)

            # 5分钟波动率指数计算
            if len(df_5m) >= 20:
                close_5m = df_5m['close'].values
                returns = np.diff(np.log(close_5m))

                if len(returns) >= 20:
                    current_vol = np.std(returns[-20:]) * np.sqrt(288)  # 年化波动率
                    avg_vol = np.std(returns) * np.sqrt(288)

                    if avg_vol > 0:
                        vix_ratio = current_vol / avg_vol
                        result['vix_score'] = max(0, (vix_ratio - 1) * 100)

            # 计算波动率综合评分
            weights = self.config['score_weights']
            score = (result['atr_expansion'] * weights['atr_expansion'] +
                    result['bb_expansion'] * weights['bb_expansion'] +
                    result['vix_score'] * weights['volume_change'])

            result['volatility_score'] = min(100, score)

        except Exception as e:
            print(f"计算波动指标失败: {e}")

        return result

    def calculate_breakout_signals(self, df_5m: pd.DataFrame, df_15m: pd.DataFrame) -> Dict:
        """计算突破信号确认指标

        Args:
            df_5m: 5分钟数据
            df_15m: 15分钟数据

        Returns:
            Dict: 突破信号结果
        """
        result = {
            'donchian_breakout': 0,  # 1=向上突破, -1=向下突破, 0=无突破
            'macd_signal': 0,        # 1=金叉, -1=死叉, 0=无信号
            'kdj_signal': 0,         # 1=超卖反弹, -1=超买回落, 0=无信号
            'body_strength': 0,      # 实体强度评分
            'breakout_score': 0
        }

        try:
            # 5分钟Donchian通道突破检测
            if len(df_5m) >= 21:
                high_5m = df_5m['high'].values
                low_5m = df_5m['low'].values
                close_5m = df_5m['close'].values

                # 计算Donchian通道
                donchian_high = talib.MAX(high_5m, timeperiod=20)
                donchian_low = talib.MIN(low_5m, timeperiod=20)

                if len(donchian_high) >= 2:
                    # 检查突破
                    if close_5m[-1] > donchian_high[-2]:
                        result['donchian_breakout'] = 1
                    elif close_5m[-1] < donchian_low[-2]:
                        result['donchian_breakout'] = -1

            # 15分钟MACD金叉死叉检测
            if len(df_15m) >= 35:
                close_15m = df_15m['close'].values

                macd_line, macd_signal, _ = talib.MACD(close_15m,
                                                      fastperiod=12,
                                                      slowperiod=26,
                                                      signalperiod=9)

                if len(macd_line) >= 2:
                    # 检查金叉死叉
                    if (macd_line[-1] > macd_signal[-1] and
                        macd_line[-2] <= macd_signal[-2]):
                        result['macd_signal'] = 1  # 金叉
                    elif (macd_line[-1] < macd_signal[-1] and
                          macd_line[-2] >= macd_signal[-2]):
                        result['macd_signal'] = -1  # 死叉

            # 15分钟KDJ超买超卖检测
            if len(df_15m) >= 20:
                high_15m = df_15m['high'].values
                low_15m = df_15m['low'].values
                close_15m = df_15m['close'].values

                k_values = talib.STOCH(high_15m, low_15m, close_15m,
                                      fastk_period=9, slowk_period=3, slowd_period=3)[0]

                if len(k_values) >= 1:
                    k_value = k_values[-1]
                    if k_value < 20:  # 超卖
                        result['kdj_signal'] = 1
                    elif k_value > 80:  # 超买
                        result['kdj_signal'] = -1

            # 最新K线实体强度计算
            if len(df_5m) >= 15:
                latest_bar = df_5m.iloc[-1]
                body_size = abs(latest_bar['close'] - latest_bar['open'])

                # 计算ATR作为参考
                high_5m = df_5m['high'].values
                low_5m = df_5m['low'].values
                close_5m = df_5m['close'].values
                atr = talib.ATR(high_5m, low_5m, close_5m, timeperiod=14)

                if not np.isnan(atr[-1]) and atr[-1] > 0:
                    body_ratio = body_size / atr[-1]
                    result['body_strength'] = min(100, body_ratio * 50)

            # 计算突破综合评分
            score = 0
            if abs(result['donchian_breakout']) == 1:
                score += 40
            if abs(result['macd_signal']) == 1:
                score += 30
            if abs(result['kdj_signal']) == 1:
                score += 20
            score += result['body_strength'] * 0.1

            result['breakout_score'] = min(100, score)

        except Exception as e:
            print(f"计算突破信号失败: {e}")

        return result

    def evaluate_symbol(self, symbol: str) -> Dict:
        """评估单个品种

        Args:
            symbol: 品种代码

        Returns:
            Dict: 评估结果
        """
        result = {
            '品种': symbol,
            '趋势方向': '无',
            '最佳周期': '无',
            '突破强度': 0,
            '波动率评分': 0,
            '综合评分': 0,
            '状态': '排除',
            '排除原因': ''
        }

        try:
            # 获取多周期数据
            df_daily = self.fetch_data(symbol, 'D')
            df_30m = self.fetch_data(symbol, '30')
            df_15m = self.fetch_data(symbol, '15')
            df_5m = self.fetch_data(symbol, '5')

            # 检查数据有效性
            if any(df is None or df.empty for df in [df_daily, df_30m, df_15m, df_5m]):
                result['排除原因'] = '数据不足'
                return result

            # 计算各类指标
            trend_indicators = self.calculate_trend_indicators(df_daily, df_30m)
            volatility_indicators = self.calculate_volatility_indicators(df_15m, df_30m, df_5m)
            breakout_signals = self.calculate_breakout_signals(df_5m, df_15m)

            # 应用排除条件
            exclude_reason = self._check_exclusion_conditions(df_30m, df_15m, trend_indicators, df_daily)
            if exclude_reason:
                result['排除原因'] = exclude_reason
                return result

            # 确定趋势方向
            if trend_indicators['di_direction'] == 1 and trend_indicators['ema_alignment']:
                result['趋势方向'] = '多头'
            elif trend_indicators['di_direction'] == -1:
                result['趋势方向'] = '空头'
            else:
                result['趋势方向'] = '震荡'

            # 确定最佳周期
            result['最佳周期'] = self._determine_best_period(breakout_signals, volatility_indicators)

            # 计算各项评分
            result['突破强度'] = round(breakout_signals['breakout_score'], 1)
            result['波动率评分'] = round(volatility_indicators['volatility_score'], 1)

            # 计算综合评分
            综合评分 = (trend_indicators['trend_score'] * 0.3 +
                      volatility_indicators['volatility_score'] * 0.4 +
                      breakout_signals['breakout_score'] * 0.3)
            result['综合评分'] = round(综合评分, 1)

            # 确定状态 (使用配置中的阈值)
            rating_config = self.config['rating_criteria']
            if result['综合评分'] >= rating_config['excellent_threshold']:
                result['状态'] = '重点关注'
            elif result['综合评分'] >= rating_config['good_threshold']:
                result['状态'] = '准备入场'
            elif result['综合评分'] >= rating_config['fair_threshold']:
                result['状态'] = '观察'
            else:
                result['状态'] = '排除'
                result['排除原因'] = '评分过低'

        except Exception as e:
            result['排除原因'] = f'计算错误: {str(e)}'
            print(f"评估品种 {symbol} 失败: {e}")

        return result

    def _check_exclusion_conditions(self, df_30m: pd.DataFrame, df_15m: pd.DataFrame,
                                   trend_indicators: Dict, df_daily: pd.DataFrame = None) -> str:
        """检查排除条件

        Returns:
            str: 排除原因，空字符串表示不排除
        """
        try:
            # 检查ADX最小阈值
            if trend_indicators['daily_adx'] < self.config['adx_min_threshold']:
                return f"ADX过低({trend_indicators['daily_adx']:.1f}<{self.config['adx_min_threshold']})"

            # 检查当日振幅 (使用日线数据)
            if df_daily is not None and len(df_daily) >= 2:
                # 使用最近一日的振幅
                latest_daily = df_daily.iloc[-1]
                daily_range = latest_daily['high'] - latest_daily['low']

                # 计算15分钟ATR作为参考
                if len(df_15m) >= 15:
                    high_15m = df_15m['high'].values
                    low_15m = df_15m['low'].values
                    close_15m = df_15m['close'].values
                    atr_15m = talib.ATR(high_15m, low_15m, close_15m, timeperiod=14)

                    if not np.isnan(atr_15m[-1]) and atr_15m[-1] > 0:
                        amplitude_ratio = daily_range / atr_15m[-1]
                        if amplitude_ratio < self.config['min_amplitude_multiplier']:
                            return f"振幅不足({amplitude_ratio:.1f}<{self.config['min_amplitude_multiplier']})"

            # 检查成交量（使用最后5根K线成交量平均值）
            if len(df_30m) >= 5:
                # 取最后5根K线的成交量平均值
                last_5_volumes = df_30m.tail(5)['volume']
                avg_volume = last_5_volumes.mean()
                min_avg_volume = self.config['min_volume']  # 直接使用配置值
                if avg_volume < min_avg_volume:
                    return f"成交量不足(最近5根K线平均:{avg_volume:.1f}<{min_avg_volume})"
            elif len(df_30m) >= 1:
                # 如果不足5根K线，使用现有K线的平均值
                avg_volume = df_30m['volume'].mean()
                min_avg_volume = self.config['min_volume']  # 直接使用配置值
                if avg_volume < min_avg_volume:
                    return f"成交量不足(平均:{avg_volume:.1f}<{min_avg_volume})"

        except Exception as e:
            return f"排除条件检查失败: {str(e)}"

        return ""

    def _determine_best_period(self, breakout_signals: Dict, volatility_indicators: Dict) -> str:
        """确定最佳交易周期"""
        scores = {
            '5分钟': 0,
            '15分钟': 0,
            '30分钟': 0
        }

        # 根据突破信号强度分配周期偏好
        if breakout_signals['donchian_breakout'] != 0:
            scores['5分钟'] += 40
        if breakout_signals['macd_signal'] != 0:
            scores['15分钟'] += 30
        if breakout_signals['kdj_signal'] != 0:
            scores['15分钟'] += 20

        # 根据波动率分配周期偏好
        if volatility_indicators['atr_expansion'] > 50:
            scores['15分钟'] += 30
        if volatility_indicators['bb_expansion'] > 30:
            scores['30分钟'] += 25
        if volatility_indicators['vix_score'] > 40:
            scores['5分钟'] += 20

        # 返回得分最高的周期
        best_period = max(scores, key=scores.get)
        return best_period if scores[best_period] > 0 else '无'

    def run_scan(self) -> pd.DataFrame:
        """执行全品种扫描"""
        print(f"开始扫描 {len(self.symbols)} 个期货品种...")
        print("=" * 80)

        # 创建数据库备份（如果可用）
        if DATABASE_BACKUP_AVAILABLE:
            print("正在创建数据库备份...")
            backup_file = create_screening_backup()
            if backup_file:
                print(f"数据库备份创建成功: {backup_file}")
            else:
                print("数据库备份创建失败，将使用原始数据库")
        else:
            print("数据库备份功能不可用，使用原始数据库")

        results = []

        # 使用多线程并发处理 (减少线程数避免重复获取数据)
        with ThreadPoolExecutor(max_workers=4) as executor:
            # 提交所有任务
            future_to_symbol = {
                executor.submit(self.evaluate_symbol, symbol): symbol
                for symbol in self.symbols
            }

            # 收集结果
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    result = future.result()
                    results.append(result)

                    # 实时显示进度
                    status = result['状态']
                    score = result['综合评分']
                    if result['排除原因']:
                        print(f"✓ {symbol:8} | {status:8} | 评分: {score:5.1f} | 原因: {result['排除原因']}")
                    else:
                        print(f"✓ {symbol:8} | {status:8} | 评分: {score:5.1f}")

                except Exception as e:
                    print(f"✗ {symbol:8} | 处理失败: {e}")
                    results.append({
                        '品种': symbol,
                        '趋势方向': '错误',
                        '最佳周期': '无',
                        '突破强度': 0,
                        '波动率评分': 0,
                        '综合评分': 0,
                        '状态': '排除',
                        '排除原因': str(e)
                    })

        # 转换为DataFrame并排序
        df_results = pd.DataFrame(results)
        df_results = df_results.sort_values('综合评分', ascending=False)

        return df_results

    def save_results(self, df_results: pd.DataFrame, filename: str = None):
        """保存结果到CSV文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"期货品种筛选结果_{timestamp}.csv"

        # 创建增强版CSV数据，包含更多评分细节
        enhanced_df = df_results.copy()

        # 按评分排序
        enhanced_df = enhanced_df.sort_values('综合评分', ascending=False)

        # 添加评分等级列
        def get_score_grade(score):
            if score >= 80:
                return 'A级-优秀'
            elif score >= 60:
                return 'B级-良好'
            elif score >= 40:
                return 'C级-一般'
            elif score >= 25:
                return 'D级-较差'
            else:
                return 'E级-很差'

        enhanced_df['评分等级'] = enhanced_df['综合评分'].apply(get_score_grade)

        # 添加推荐操作列
        def get_recommendation(row):
            if row['状态'] == '重点关注':
                return '立即关注'
            elif row['状态'] == '准备入场':
                return '准备建仓'
            elif row['状态'] == '观察':
                return '持续观察'
            else:
                return '暂不操作'

        enhanced_df['操作建议'] = enhanced_df.apply(get_recommendation, axis=1)

        # 重新排列列顺序，突出重要信息
        column_order = [
            '品种', '综合评分', '评分等级', '状态', '操作建议',
            '趋势方向', '最佳周期', '突破强度', '波动率评分', '排除原因'
        ]

        # 确保所有列都存在
        available_columns = [col for col in column_order if col in enhanced_df.columns]
        enhanced_df = enhanced_df[available_columns]

        # 保存为CSV文件，使用中文编码
        enhanced_df.to_csv(filename, index=False, encoding='utf-8-sig')

        # 显示保存信息
        print(f"\n💾 评分结果已保存到: {filename}")
        print(f"   包含 {len(enhanced_df)} 个品种的详细评分信息")

        # 显示文件内容概览
        qualified_count = len(enhanced_df[enhanced_df['状态'] != '排除'])
        focus_count = len(enhanced_df[enhanced_df['状态'] == '重点关注'])
        ready_count = len(enhanced_df[enhanced_df['状态'] == '准备入场'])

        print(f"   其中: 重点关注 {focus_count}个, 准备入场 {ready_count}个, 合格品种 {qualified_count}个")

        return filename

    def display_results(self, df_results: pd.DataFrame):
        """显示筛选结果"""
        print("\n" + "=" * 120)
        print("期货品种筛选结果汇总".center(120))
        print("=" * 120)

        # 显示统计信息
        total_count = len(df_results)
        qualified_count = len(df_results[df_results['状态'] != '排除'])
        focus_count = len(df_results[df_results['状态'] == '重点关注'])
        ready_count = len(df_results[df_results['状态'] == '准备入场'])
        observe_count = len(df_results[df_results['状态'] == '观察'])
        excluded_count = len(df_results[df_results['状态'] == '排除'])

        print(f"📊 评分统计:")
        print(f"   总品种数: {total_count}")
        print(f"   合格品种: {qualified_count} ({qualified_count/total_count:.1%})")
        print(f"   重点关注: {focus_count} (评分≥60)")
        print(f"   准备入场: {ready_count} (评分40-59)")
        print(f"   观察品种: {observe_count} (评分25-39)")
        print(f"   排除品种: {excluded_count} (评分<25)")
        print("-" * 120)

        # 显示评分分布
        if not df_results.empty:
            avg_score = df_results['综合评分'].mean()
            max_score = df_results['综合评分'].max()
            min_score = df_results['综合评分'].min()
            print(f"📈 评分分布:")
            print(f"   平均评分: {avg_score:.1f}")
            print(f"   最高评分: {max_score:.1f}")
            print(f"   最低评分: {min_score:.1f}")
            print("-" * 120)

        # 按评分排序显示详细结果
        df_sorted = df_results.sort_values('综合评分', ascending=False)

        print("📋 详细评分结果:")
        print(f"{'品种':<12} {'评分':<6} {'状态':<8} {'趋势':<6} {'周期':<8} {'突破':<6} {'波动':<6} {'排除原因':<15}")
        print("-" * 120)

        for _, row in df_sorted.iterrows():
            status_icon = {
                '重点关注': '🔥',
                '准备入场': '⚡',
                '观察': '👀',
                '排除': '❌'
            }.get(row['状态'], '  ')

            print(f"{status_icon} {row['品种']:<10} {row['综合评分']:<6.1f} {row['状态']:<8} "
                  f"{row['趋势方向']:<6} {row['最佳周期']:<8} {row['突破强度']:<6.1f} "
                  f"{row['波动率评分']:<6.1f} {row['排除原因']:<15}")

        # 显示重点关注品种详情
        focus_symbols = df_results[df_results['状态'] == '重点关注']
        if not focus_symbols.empty:
            print("\n" + "=" * 80)
            print("🔥 重点关注品种详情:")
            print("-" * 80)
            for _, row in focus_symbols.iterrows():
                print(f"  {row['品种']:12} | 评分: {row['综合评分']:5.1f} | {row['趋势方向']:4}趋势 | "
                      f"最佳周期: {row['最佳周期']:8} | 突破强度: {row['突破强度']:5.1f}")

        # 显示准备入场品种
        ready_symbols = df_results[df_results['状态'] == '准备入场']
        if not ready_symbols.empty:
            print("\n" + "=" * 80)
            print("⚡ 准备入场品种:")
            print("-" * 80)
            for _, row in ready_symbols.iterrows():
                print(f"  {row['品种']:12} | 评分: {row['综合评分']:5.1f} | {row['趋势方向']:4}趋势 | "
                      f"最佳周期: {row['最佳周期']:8}")

        print("=" * 120)


def main():
    """主程序入口"""
    print("期货分钟级K线品种筛选器")
    print("=" * 50)
    print("专为突破策略设计，支持多周期验证和实时波动分析")
    print("=" * 50)

    try:
        # 创建筛选器实例
        scanner = FuturesMinuteScanner()

        # 执行扫描
        start_time = time.time()
        results = scanner.run_scan()
        end_time = time.time()

        # 显示结果
        scanner.display_results(results)

        # 保存结果
        filename = scanner.save_results(results)

        print(f"\n扫描完成，耗时: {end_time - start_time:.2f} 秒")
        print(f"结果文件: {filename}")

        # 显示使用建议
        print("\n" + "=" * 60)
        print("📊 使用建议:")
        print("1. 重点关注评分 >= 80 的品种")
        print("2. 结合实际市场情况进行最终决策")
        print("3. 建议每30分钟重新扫描一次")
        print("4. 注意风险管理，控制仓位")
        print()
        print("💡 配套工具:")
        print("   python capital_allocation.py  # 资金配置工具")
        print("   选择'8万元'配置可启用激进模式:")
        print("   - 120%保证金使用率")
        print("   - 全仓入场策略")
        print("   - 固定+浮动止盈")
        print("=" * 60)

    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
