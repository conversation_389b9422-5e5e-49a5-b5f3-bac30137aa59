int reqUserLogin(const dict &req, int reqid);

int reqUserLogout(const dict &req, int reqid);

int reqOrderAction(const dict &req, int reqid);

int reqTradingAccount(const dict &req, int reqid);

int reqQryOrder(const dict &req, int reqid);

int reqQryTrade(const dict &req, int reqid);

int reqQryInvestorPosition(const dict &req, int reqid);

int reqChangePwd(const dict &req, int reqid);

int reqQryTest(const dict &req, int reqid);

