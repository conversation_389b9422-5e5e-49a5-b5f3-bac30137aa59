int reqSubscribeTopic(const dict &req, int reqid);

int reqUserLogin(const dict &req, int reqid);

int reqUserLogout(const dict &req, int reqid);

int reqUserPasswordUpdate(const dict &req, int reqid);

int reqStockInsert(const dict &req, int reqid);

int reqStockCancel(const dict &req, int reqid);

int reqStockLock(const dict &req, int reqid);

int reqOptionsInsert(const dict &req, int reqid);

int reqOptionsCancel(const dict &req, int reqid);

int reqQuoteInsert(const dict &req, int reqid);

int reqQuoteCancel(const dict &req, int reqid);

int reqForQuote(const dict &req, int reqid);

int reqExercise(const dict &req, int reqid);

int reqExerciseCancel(const dict &req, int reqid);

int reqQryPartAccount(const dict &req, int reqid);

int reqQryStockOrder(const dict &req, int reqid);

int reqQryOptionsOrder(const dict &req, int reqid);

int reqQryQuoteOrder(const dict &req, int reqid);

int reqQryStockTrade(const dict &req, int reqid);

int reqQryOptionsTrade(const dict &req, int reqid);

int reqQryPosition(const dict &req, int reqid);

int reqQryTopic(const dict &req, int reqid);

int reqQryStock(const dict &req, int reqid);

int reqQryOptions(const dict &req, int reqid);

int reqQryRate(const dict &req, int reqid);

int reqQryClient(const dict &req, int reqid);

int reqQryClientMargin(const dict &req, int reqid);

int reqQryExercise(const dict &req, int reqid);

int reqMarginCombAction(const dict &req, int reqid);

int reqQrySseCombPosition(const dict &req, int reqid);

int reqCombExercise(const dict &req, int reqid);

