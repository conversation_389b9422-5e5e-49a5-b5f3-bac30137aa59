"""
系统性能测试
测试系统各组件的性能指标和资源使用情况
"""

import unittest
import os
import sys
import time
import threading
import psutil
from datetime import datetime, timedelta

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from main_controller import IntelligentTradingSystem
from modules.scheduler.event_manager import EventType


class TestSystemPerformance(unittest.TestCase):
    """系统性能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.trading_system = None
        self.start_time = time.time()
        self.start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
    
    def tearDown(self):
        """测试后清理"""
        if self.trading_system:
            try:
                self.trading_system.stop_system()
            except:
                pass
        
        # 显示性能统计
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        execution_time = end_time - self.start_time
        memory_usage = end_memory - self.start_memory
        
        print(f"  执行时间: {execution_time:.3f}秒")
        print(f"  内存使用: {memory_usage:.2f}MB")
    
    def test_system_initialization_performance(self):
        """测试系统初始化性能"""
        start_time = time.time()
        
        self.trading_system = IntelligentTradingSystem()
        
        # 测试交易组件初始化性能
        init_start = time.time()
        success = self.trading_system._initialize_trading_components()
        init_time = time.time() - init_start
        
        self.assertTrue(success)
        self.assertLess(init_time, 5.0, "交易组件初始化时间过长")
        
        # 测试调度组件初始化性能
        scheduler_start = time.time()
        success = self.trading_system._initialize_scheduler_components()
        scheduler_time = time.time() - scheduler_start
        
        self.assertTrue(success)
        self.assertLess(scheduler_time, 3.0, "调度组件初始化时间过长")
        
        total_time = time.time() - start_time
        self.assertLess(total_time, 10.0, "系统总初始化时间过长")
        
        print(f"✓ 系统初始化性能测试通过 (总时间: {total_time:.3f}秒)")
    
    def test_event_system_performance(self):
        """测试事件系统性能"""
        self.trading_system = IntelligentTradingSystem()
        self.trading_system._initialize_scheduler_components()
        
        # 启动事件管理器
        self.trading_system.event_manager.start()
        
        # 性能测试参数
        event_count = 1000
        received_events = []
        
        def event_handler(event):
            received_events.append(event)
        
        # 注册事件处理器
        self.trading_system.event_manager.register_handler(EventType.SYSTEM_START, event_handler)
        
        # 测试事件发射性能
        start_time = time.time()
        
        for i in range(event_count):
            self.trading_system.event_manager.emit_event(
                EventType.SYSTEM_START, 
                f"测试事件 {i}", 
                "PerformanceTest"
            )
        
        emit_time = time.time() - start_time
        
        # 等待事件处理完成
        time.sleep(1)
        
        # 验证性能指标
        events_per_second = event_count / emit_time
        self.assertGreater(events_per_second, 100, "事件发射速度过慢")
        self.assertEqual(len(received_events), event_count, "事件处理不完整")
        
        # 停止事件管理器
        self.trading_system.event_manager.stop()
        
        print(f"✓ 事件系统性能测试通过 (速度: {events_per_second:.0f} 事件/秒)")
    
    def test_monitoring_system_performance(self):
        """测试监控系统性能"""
        self.trading_system = IntelligentTradingSystem()
        self.trading_system._initialize_scheduler_components()
        
        # 启动监控
        self.trading_system.system_monitor.start_monitoring()
        
        # 测试监控数据收集性能
        start_time = time.time()
        
        # 连续获取监控数据
        for _ in range(100):
            metrics = self.trading_system.system_monitor.get_current_system_metrics()
            self.assertIsNotNone(metrics)
        
        collection_time = time.time() - start_time
        
        # 验证性能指标
        collections_per_second = 100 / collection_time
        self.assertGreater(collections_per_second, 10, "监控数据收集速度过慢")
        
        # 停止监控
        self.trading_system.system_monitor.stop_monitoring()
        
        print(f"✓ 监控系统性能测试通过 (速度: {collections_per_second:.1f} 次/秒)")
    
    def test_scheduler_performance(self):
        """测试调度器性能"""
        self.trading_system = IntelligentTradingSystem()
        self.trading_system._initialize_trading_components()
        self.trading_system._initialize_scheduler_components()
        
        # 添加大量测试任务
        task_count = 50
        executed_tasks = []
        
        def test_task():
            executed_tasks.append(time.time())
            return True
        
        # 添加任务
        start_time = time.time()
        
        for i in range(task_count):
            self.trading_system.task_scheduler.add_task(
                f"test_task_{i}",
                test_task,
                "*/1 * * * * *",  # 每秒执行
                f"测试任务 {i}"
            )
        
        add_time = time.time() - start_time
        
        # 验证任务添加性能
        tasks_per_second = task_count / add_time
        self.assertGreater(tasks_per_second, 10, "任务添加速度过慢")
        
        # 获取所有任务
        all_tasks = self.trading_system.task_scheduler.get_all_tasks()
        self.assertGreaterEqual(len(all_tasks), task_count)
        
        print(f"✓ 调度器性能测试通过 (任务添加速度: {tasks_per_second:.1f} 任务/秒)")
    
    def test_memory_usage_performance(self):
        """测试内存使用性能"""
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        self.trading_system = IntelligentTradingSystem()
        self.trading_system._initialize_trading_components()
        self.trading_system._initialize_scheduler_components()
        
        after_init_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        init_memory_usage = after_init_memory - initial_memory
        
        # 启动系统组件
        self.trading_system.event_manager.start()
        self.trading_system.system_monitor.start_monitoring()
        
        after_start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        start_memory_usage = after_start_memory - after_init_memory
        
        # 运行一段时间
        time.sleep(2)
        
        after_run_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        run_memory_usage = after_run_memory - after_start_memory
        
        # 停止系统组件
        self.trading_system.event_manager.stop()
        self.trading_system.system_monitor.stop_monitoring()
        
        # 验证内存使用
        self.assertLess(init_memory_usage, 100, "初始化内存使用过多")
        self.assertLess(start_memory_usage, 50, "启动内存使用过多")
        self.assertLess(run_memory_usage, 20, "运行时内存增长过多")
        
        print(f"✓ 内存使用性能测试通过")
        print(f"  初始化: {init_memory_usage:.2f}MB")
        print(f"  启动: {start_memory_usage:.2f}MB")
        print(f"  运行: {run_memory_usage:.2f}MB")
    
    def test_concurrent_performance(self):
        """测试并发性能"""
        self.trading_system = IntelligentTradingSystem()
        self.trading_system._initialize_scheduler_components()
        
        # 启动事件管理器
        self.trading_system.event_manager.start()
        
        # 并发测试参数
        thread_count = 10
        events_per_thread = 100
        received_events = []
        
        def event_handler(event):
            received_events.append(event)
        
        # 注册事件处理器
        self.trading_system.event_manager.register_handler(EventType.SYSTEM_START, event_handler)
        
        # 并发事件发射函数
        def emit_events(thread_id):
            for i in range(events_per_thread):
                self.trading_system.event_manager.emit_event(
                    EventType.SYSTEM_START,
                    f"线程{thread_id}_事件{i}",
                    f"Thread{thread_id}"
                )
        
        # 启动并发测试
        start_time = time.time()
        threads = []
        
        for i in range(thread_count):
            thread = threading.Thread(target=emit_events, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        emit_time = time.time() - start_time
        
        # 等待事件处理完成
        time.sleep(2)
        
        # 验证并发性能
        total_events = thread_count * events_per_thread
        events_per_second = total_events / emit_time
        
        self.assertGreater(events_per_second, 50, "并发事件处理速度过慢")
        self.assertEqual(len(received_events), total_events, "并发事件处理不完整")
        
        # 停止事件管理器
        self.trading_system.event_manager.stop()
        
        print(f"✓ 并发性能测试通过 (速度: {events_per_second:.0f} 事件/秒)")
    
    def test_cpu_usage_performance(self):
        """测试CPU使用性能"""
        self.trading_system = IntelligentTradingSystem()
        self.trading_system._initialize_trading_components()
        self.trading_system._initialize_scheduler_components()
        
        # 启动系统组件
        self.trading_system.event_manager.start()
        self.trading_system.system_monitor.start_monitoring()
        
        # 监控CPU使用率
        cpu_samples = []
        
        def monitor_cpu():
            for _ in range(10):
                cpu_percent = psutil.cpu_percent(interval=0.1)
                cpu_samples.append(cpu_percent)
        
        # 启动CPU监控
        monitor_thread = threading.Thread(target=monitor_cpu)
        monitor_thread.start()
        
        # 执行一些系统操作
        for i in range(100):
            self.trading_system.event_manager.emit_event(
                EventType.SYSTEM_START,
                f"CPU测试事件 {i}",
                "CPUTest"
            )
            time.sleep(0.01)
        
        # 等待监控完成
        monitor_thread.join()
        
        # 停止系统组件
        self.trading_system.event_manager.stop()
        self.trading_system.system_monitor.stop_monitoring()
        
        # 验证CPU使用率
        if cpu_samples:
            avg_cpu = sum(cpu_samples) / len(cpu_samples)
            max_cpu = max(cpu_samples)
            
            self.assertLess(avg_cpu, 50, "平均CPU使用率过高")
            self.assertLess(max_cpu, 80, "峰值CPU使用率过高")
            
            print(f"✓ CPU使用性能测试通过")
            print(f"  平均CPU: {avg_cpu:.1f}%")
            print(f"  峰值CPU: {max_cpu:.1f}%")
        else:
            print("✓ CPU使用性能测试通过 (无法获取CPU数据)")
    
    def test_system_stability_performance(self):
        """测试系统稳定性性能"""
        self.trading_system = IntelligentTradingSystem()
        self.trading_system._initialize_trading_components()
        self.trading_system._initialize_scheduler_components()
        
        # 启动系统组件
        self.trading_system.event_manager.start()
        self.trading_system.system_monitor.start_monitoring()
        
        # 长时间运行测试
        start_time = time.time()
        error_count = 0
        operation_count = 0
        
        # 运行5秒的稳定性测试
        while time.time() - start_time < 5:
            try:
                # 执行各种操作
                self.trading_system.event_manager.emit_event(
                    EventType.SYSTEM_START,
                    f"稳定性测试 {operation_count}",
                    "StabilityTest"
                )
                
                metrics = self.trading_system.system_monitor.get_current_system_metrics()
                self.assertIsNotNone(metrics)
                
                operation_count += 1
                time.sleep(0.01)
                
            except Exception as e:
                error_count += 1
                print(f"稳定性测试错误: {e}")
        
        # 停止系统组件
        self.trading_system.event_manager.stop()
        self.trading_system.system_monitor.stop_monitoring()
        
        # 验证稳定性
        error_rate = error_count / operation_count if operation_count > 0 else 1
        self.assertLess(error_rate, 0.01, "系统错误率过高")
        self.assertGreater(operation_count, 100, "操作数量过少")
        
        print(f"✓ 系统稳定性性能测试通过")
        print(f"  操作数量: {operation_count}")
        print(f"  错误数量: {error_count}")
        print(f"  错误率: {error_rate:.4f}")


if __name__ == "__main__":
    unittest.main()
