virtual void nResult() {};

virtual void nReason() {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void bIsLast(const dict &data, const dict &error, int reqid, bool last) {};

virtual void pDepthMarketData(const dict &data) {};

