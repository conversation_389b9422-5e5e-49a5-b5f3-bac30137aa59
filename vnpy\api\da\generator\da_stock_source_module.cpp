.def("reqUserLogin", &StockApi::reqUserLogin)
.def("reqUserLogout", &StockApi::reqUserLogout)
.def("reqSafeVerify", &StockApi::reqSafeVerify)
.def("reqVerifyCode", &StockApi::reqVerifyCode)
.def("reqSetVerifyQA", &StockApi::reqSetVerifyQA)
.def("reqGetQuestion", &StockApi::reqGetQuestion)
.def("reqOrderInsert", &StockApi::reqOrderInsert)
.def("reqOrderModify", &StockApi::reqOrderModify)
.def("reqOrderCancel", &StockApi::reqOrderCancel)
.def("reqPasswordUpdate", &StockApi::reqPasswordUpdate)
.def("reqQryTick", &StockApi::reqQryTick)
.def("reqQryOrder", &StockApi::reqQryOrder)
.def("reqQryTrade", &StockApi::reqQryTrade)
.def("reqQryCapital", &StockApi::reqQryCapital)
.def("reqQryVersion", &StockApi::reqQryVersion)
.def("reqQryPosition", &StockApi::reqQryPosition)
.def("reqQryCurrency", &StockApi::reqQryCurrency)
.def("reqQryExchange", &StockApi::reqQryExchange)
.def("reqQryInstrument", &StockApi::reqQryInstrument)

.def("onFrontConnected", &StockApi::onFrontConnected)
.def("onFrontDisconnected", &StockApi::onFrontDisconnected)
.def("onHeartBeatWarning", &StockApi::onHeartBeatWarning)
.def("onRspNeedVerify", &StockApi::onRspNeedVerify)
.def("onRspUserLogin", &StockApi::onRspUserLogin)
.def("onRspUserLogout", &StockApi::onRspUserLogout)
.def("onRspVerifyCode", &StockApi::onRspVerifyCode)
.def("onRspSafeVerify", &StockApi::onRspSafeVerify)
.def("onRspSetVerifyQA", &StockApi::onRspSetVerifyQA)
.def("onRspAccount", &StockApi::onRspAccount)
.def("onRspQuestion", &StockApi::onRspQuestion)
.def("onRspOrderInsert", &StockApi::onRspOrderInsert)
.def("onRspOrderModify", &StockApi::onRspOrderModify)
.def("onRspOrderCancel", &StockApi::onRspOrderCancel)
.def("onRspPasswordUpdate", &StockApi::onRspPasswordUpdate)
.def("onRtnTrade", &StockApi::onRtnTrade)
.def("onRtnOrder", &StockApi::onRtnOrder)
.def("onRtnCapital", &StockApi::onRtnCapital)
.def("onRtnPosition", &StockApi::onRtnPosition)
.def("onRspQryTick", &StockApi::onRspQryTick)
.def("onRspQryOrder", &StockApi::onRspQryOrder)
.def("onRspQryTrade", &StockApi::onRspQryTrade)
.def("onRspQryCapital", &StockApi::onRspQryCapital)
.def("onRspQryVersion", &StockApi::onRspQryVersion)
.def("onRspQryPosition", &StockApi::onRspQryPosition)
.def("onRspQryCurrency", &StockApi::onRspQryCurrency)
.def("onRspQryExchange", &StockApi::onRspQryExchange)
.def("onRspQryInstrument", &StockApi::onRspQryInstrument)
;
