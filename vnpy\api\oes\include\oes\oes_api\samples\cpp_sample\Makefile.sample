## ==> make -f Makefile.sample

TARGET_STK := 01_oes_client_stock_sample
TARGET_OPT := 02_oes_client_option_sample
TARGETS := $(TARGET_STK) $(TARGET_OPT)

SRCS := $(wildcard *.cpp)  
OBJS := $(patsubst %cpp,%o,$(SRCS))

INC_DIR   = ../../../../include
LIB_DIR   = ../../../../linux64
#LIB_DIR   = ../../../../macos64
#LIB_DIR   = ../../../../win32
#LIB_DIR   = ../../../../win64

CC_CFLAGS = -g -Wall
CC_LFLAGS = -loes_api -lpthread -lm


all : $(TARGETS) cleanObjs

$(TARGETS) : $(OBJS)
	g++ -L$(LIB_DIR) -o $(TARGET_STK) $(filter-out $(TARGET_OPT).o, $(OBJS)) $(CC_LFLAGS)
	g++ -L$(LIB_DIR) -o $(TARGET_OPT) $(filter-out $(TARGET_STK).o, $(OBJS)) $(CC_LFLAGS)

%.o : %.cpp
	g++ $(CC_CFLAGS) -I$(INC_DIR) -o $@ -c $< 

.PHONY : cleanObjs
cleanObjs:
	rm -f $(OBJS)

.PHONY : clean
clean:
	rm -f $(OBJS) $(TARGET)
