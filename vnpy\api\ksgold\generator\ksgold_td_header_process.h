void processFrontConnected(Task *task);

void processFrontDisconnected(Task *task);

void processRspUserLogin(Task *task);

void processRspUserLogout(Task *task);

void processNtyMktStatus(Task *task);

void processRtnInstrumentStatus(Task *task);

void processRspQryInstrument(Task *task);

void processRspReqQryVarietyCode(Task *task);

void processRspOrderInsert(Task *task);

void processRspETFSubscriptionOrderInsert(Task *task);

void processRspETFPurchaseOrderInsert(Task *task);

void processRspETFRedeemInsert(Task *task);

void processRspETFAccountBinding(Task *task);

void processRspETFAccountUnbinding(Task *task);

void processRtnOrder(Task *task);

void processForceLogout(Task *task);

void processRtnETFAccountBindingStatus(Task *task);

void processRtnETFOrder(Task *task);

void processRspOrderAction(Task *task);

void processRspError(Task *task);

void processRtnTrade(Task *task);

void processRspQryTradingAccount(Task *task);

void processRspQryHisCapital(Task *task);

void processRspQryOrder(Task *task);

void processRspQryTrade(Task *task);

void processRspQryInvestorPosition(Task *task);

void processRspQryClientStorage(Task *task);

void processRspQryCostMarginFeeRate(Task *task);

void processRspConditionOrderInsert(Task *task);

void processRspConditionOrderAction(Task *task);

void processRspQryConditionOrder(Task *task);

void processRspQryConditionOrderTrade(Task *task);

void processRspQryClientSessionInfo(Task *task);

void processRspQryQuotation(Task *task);

void processRspQryInvestorPositionDetail(Task *task);

void processRspQryETFradeDetail(Task *task);

void processRspQryETFPcfDetail(Task *task);

void processRspModifyPassword(Task *task);

void processRspB0CMoneyIO(Task *task);

