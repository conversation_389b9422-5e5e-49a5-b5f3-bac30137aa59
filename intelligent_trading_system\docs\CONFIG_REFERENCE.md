# 智能量化交易系统配置参考

## 目录
1. [配置概述](#配置概述)
2. [系统配置](#系统配置)
3. [交易配置](#交易配置)
4. [风险配置](#风险配置)
5. [配置验证](#配置验证)
6. [配置示例](#配置示例)
7. [常见配置问题](#常见配置问题)

## 配置概述

智能量化交易系统使用三个主要配置文件：

- `system_config.py`: 系统基础配置
- `trading_config.py`: 交易相关配置
- `risk_config.py`: 风险控制配置

所有配置都使用Python dataclass定义，提供类型安全和默认值。

## 系统配置

### SystemConfig 类

位置: `config/system_config.py`

```python
@dataclass
class DatabaseConfig:
    """数据库配置"""
    path: str = "vnpy_data.db"
    driver: str = "sqlite"
    host: str = "localhost"
    port: int = 3306
    username: str = ""
    password: str = ""
    database: str = "vnpy"

@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    file_enabled: bool = True
    console_enabled: bool = True
    max_file_size: int = 10  # MB
    backup_count: int = 5
    log_dir: str = "logs"

@dataclass
class SchedulerConfig:
    """调度器配置"""
    timezone: str = "Asia/Shanghai"
    max_workers: int = 4
    task_timeout: int = 300  # 秒
    retry_count: int = 3
    retry_delay: int = 60  # 秒

@dataclass
class MonitoringConfig:
    """监控配置"""
    enabled: bool = True
    interval: int = 60  # 秒
    cpu_threshold: float = 80.0  # %
    memory_threshold: float = 80.0  # %
    disk_threshold: float = 90.0  # %
    alert_enabled: bool = True

@dataclass
class SystemConfig:
    """系统配置"""
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    scheduler: SchedulerConfig = field(default_factory=SchedulerConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    debug_mode: bool = False
    test_mode: bool = False
```

### 配置参数说明

#### 数据库配置 (DatabaseConfig)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| path | str | "vnpy_data.db" | SQLite数据库文件路径 |
| driver | str | "sqlite" | 数据库驱动类型 |
| host | str | "localhost" | 数据库主机地址 |
| port | int | 3306 | 数据库端口 |
| username | str | "" | 数据库用户名 |
| password | str | "" | 数据库密码 |
| database | str | "vnpy" | 数据库名称 |

#### 日志配置 (LoggingConfig)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| level | str | "INFO" | 日志级别 (DEBUG/INFO/WARNING/ERROR) |
| file_enabled | bool | True | 是否启用文件日志 |
| console_enabled | bool | True | 是否启用控制台日志 |
| max_file_size | int | 10 | 单个日志文件最大大小(MB) |
| backup_count | int | 5 | 日志文件备份数量 |
| log_dir | str | "logs" | 日志文件目录 |

#### 调度器配置 (SchedulerConfig)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| timezone | str | "Asia/Shanghai" | 时区设置 |
| max_workers | int | 4 | 最大工作线程数 |
| task_timeout | int | 300 | 任务超时时间(秒) |
| retry_count | int | 3 | 任务重试次数 |
| retry_delay | int | 60 | 重试延迟时间(秒) |

#### 监控配置 (MonitoringConfig)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| enabled | bool | True | 是否启用监控 |
| interval | int | 60 | 监控间隔(秒) |
| cpu_threshold | float | 80.0 | CPU使用率告警阈值(%) |
| memory_threshold | float | 80.0 | 内存使用率告警阈值(%) |
| disk_threshold | float | 90.0 | 磁盘使用率告警阈值(%) |
| alert_enabled | bool | True | 是否启用告警 |

## 交易配置

### TradingConfig 类

位置: `config/trading_config.py`

```python
@dataclass
class ScreeningConfig:
    """筛选配置"""
    enabled: bool = True
    trend_threshold: float = 0.65
    volatility_min: float = 1.8
    volatility_max: float = 3.5
    liquidity_peak_max: float = 5.0
    cost_ratio_max: float = 0.3
    min_volume: int = 1000
    lookback_days: int = 20

@dataclass
class StrategyConfig:
    """策略配置"""
    default_strategy: str = "fenzhouqiplus_strategy"
    default_size: int = 1
    max_positions: int = 10
    position_multiplier: float = 2.0
    profit_target: float = 0.02
    stop_loss: float = 0.01

@dataclass
class OptimizationConfig:
    """优化配置"""
    enabled: bool = True
    method: str = "genetic"  # genetic, grid, random
    population_size: int = 50
    generations: int = 20
    mutation_rate: float = 0.1
    crossover_rate: float = 0.8

@dataclass
class TradingConfig:
    """交易配置"""
    screening: ScreeningConfig = field(default_factory=ScreeningConfig)
    strategy: StrategyConfig = field(default_factory=StrategyConfig)
    optimization: OptimizationConfig = field(default_factory=OptimizationConfig)
    blacklist_symbols: List[str] = field(default_factory=lambda: ["JR", "LR", "RI", "PM", "WH", "RS", "CJ"])
    trading_sessions: Dict[str, List[str]] = field(default_factory=lambda: {
        "day": ["09:00-11:30", "13:30-15:00"],
        "night": ["21:00-02:30"]
    })
```

### 配置参数说明

#### 筛选配置 (ScreeningConfig)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| enabled | bool | True | 是否启用筛选功能 |
| trend_threshold | float | 0.65 | 趋势强度阈值 |
| volatility_min | float | 1.8 | 最小波动率 |
| volatility_max | float | 3.5 | 最大波动率 |
| liquidity_peak_max | float | 5.0 | 最大流动性峰值 |
| cost_ratio_max | float | 0.3 | 最大成本比率 |
| min_volume | int | 1000 | 最小成交量 |
| lookback_days | int | 20 | 回看天数 |

#### 策略配置 (StrategyConfig)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| default_strategy | str | "fenzhouqiplus_strategy" | 默认策略名称 |
| default_size | int | 1 | 默认手数 |
| max_positions | int | 10 | 最大持仓数 |
| position_multiplier | float | 2.0 | 仓位倍数 |
| profit_target | float | 0.02 | 止盈目标 |
| stop_loss | float | 0.01 | 止损比例 |

#### 优化配置 (OptimizationConfig)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| enabled | bool | True | 是否启用参数优化 |
| method | str | "genetic" | 优化方法 |
| population_size | int | 50 | 种群大小 |
| generations | int | 20 | 进化代数 |
| mutation_rate | float | 0.1 | 变异率 |
| crossover_rate | float | 0.8 | 交叉率 |

## 风险配置

### RiskConfig 类

位置: `config/risk_config.py`

```python
@dataclass
class PositionLimits:
    """仓位限制"""
    max_total_position: float = 0.8
    max_single_position: float = 0.1
    max_sector_position: float = 0.3
    max_correlation: float = 0.7

@dataclass
class RiskMetrics:
    """风险指标"""
    max_drawdown_limit: float = 0.15
    var_confidence: float = 0.95
    var_period: int = 252
    sharpe_min: float = 1.0
    calmar_min: float = 1.5

@dataclass
class AllocationPreset:
    """分配预设"""
    risk_ratio: float
    margin_ratio: float
    risk_level: str
    risk_multiplier: float

@dataclass
class RiskConfig:
    """风险配置"""
    position_limits: PositionLimits = field(default_factory=PositionLimits)
    risk_metrics: RiskMetrics = field(default_factory=RiskMetrics)
    capital_allocation: Dict[str, AllocationPreset] = field(default_factory=lambda: {
        "conservative": AllocationPreset(0.02, 0.15, "低风险", 1.0),
        "moderate": AllocationPreset(0.03, 0.20, "中风险", 1.5),
        "aggressive": AllocationPreset(0.05, 0.30, "高风险", 2.0)
    })
    emergency_stop: bool = True
    emergency_drawdown: float = 0.20
```

### 配置参数说明

#### 仓位限制 (PositionLimits)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| max_total_position | float | 0.8 | 最大总仓位比例 |
| max_single_position | float | 0.1 | 单品种最大仓位比例 |
| max_sector_position | float | 0.3 | 单板块最大仓位比例 |
| max_correlation | float | 0.7 | 最大相关性 |

#### 风险指标 (RiskMetrics)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| max_drawdown_limit | float | 0.15 | 最大回撤限制 |
| var_confidence | float | 0.95 | VaR置信度 |
| var_period | int | 252 | VaR计算周期 |
| sharpe_min | float | 1.0 | 最小夏普比率 |
| calmar_min | float | 1.5 | 最小卡玛比率 |

#### 分配预设 (AllocationPreset)

| 参数 | 类型 | 说明 |
|------|------|------|
| risk_ratio | float | 风险比率 |
| margin_ratio | float | 保证金比率 |
| risk_level | str | 风险等级描述 |
| risk_multiplier | float | 风险倍数 |

## 配置验证

系统提供配置验证功能，确保配置参数的有效性：

```python
def validate_config():
    """验证配置"""
    try:
        system_config = SystemConfig()
        trading_config = TradingConfig()
        risk_config = RiskConfig()
        
        # 验证系统配置
        assert system_config.logging.level in ["DEBUG", "INFO", "WARNING", "ERROR"]
        assert system_config.scheduler.max_workers > 0
        assert system_config.monitoring.cpu_threshold > 0
        
        # 验证交易配置
        assert 0 < trading_config.screening.trend_threshold < 1
        assert trading_config.screening.volatility_min < trading_config.screening.volatility_max
        assert trading_config.strategy.max_positions > 0
        
        # 验证风险配置
        assert 0 < risk_config.position_limits.max_total_position <= 1
        assert 0 < risk_config.position_limits.max_single_position <= 1
        assert 0 < risk_config.risk_metrics.max_drawdown_limit < 1
        
        print("✓ 配置验证通过")
        return True
        
    except Exception as e:
        print(f"✗ 配置验证失败: {e}")
        return False
```

## 配置示例

### 保守型配置示例

```python
# 保守型交易配置
conservative_config = TradingConfig(
    screening=ScreeningConfig(
        trend_threshold=0.7,      # 更高的趋势要求
        volatility_min=1.5,       # 更低的波动率要求
        volatility_max=2.5,
        liquidity_peak_max=3.0,   # 更严格的流动性要求
        cost_ratio_max=0.2        # 更低的成本要求
    ),
    strategy=StrategyConfig(
        max_positions=5,          # 更少的持仓数
        position_multiplier=1.5,  # 更小的仓位倍数
        profit_target=0.015,      # 更保守的止盈
        stop_loss=0.008           # 更严格的止损
    )
)

# 保守型风险配置
conservative_risk = RiskConfig(
    position_limits=PositionLimits(
        max_total_position=0.6,   # 更低的总仓位
        max_single_position=0.08, # 更小的单品种仓位
        max_sector_position=0.2   # 更小的板块仓位
    ),
    risk_metrics=RiskMetrics(
        max_drawdown_limit=0.1,   # 更严格的回撤限制
        sharpe_min=1.2,           # 更高的夏普比率要求
        calmar_min=2.0            # 更高的卡玛比率要求
    )
)
```

### 激进型配置示例

```python
# 激进型交易配置
aggressive_config = TradingConfig(
    screening=ScreeningConfig(
        trend_threshold=0.6,      # 更宽松的趋势要求
        volatility_min=2.0,       # 更高的波动率要求
        volatility_max=4.0,
        liquidity_peak_max=6.0,   # 更宽松的流动性要求
        cost_ratio_max=0.4        # 更高的成本容忍度
    ),
    strategy=StrategyConfig(
        max_positions=15,         # 更多的持仓数
        position_multiplier=2.5,  # 更大的仓位倍数
        profit_target=0.03,       # 更激进的止盈
        stop_loss=0.015           # 更宽松的止损
    )
)

# 激进型风险配置
aggressive_risk = RiskConfig(
    position_limits=PositionLimits(
        max_total_position=0.9,   # 更高的总仓位
        max_single_position=0.15, # 更大的单品种仓位
        max_sector_position=0.4   # 更大的板块仓位
    ),
    risk_metrics=RiskMetrics(
        max_drawdown_limit=0.2,   # 更宽松的回撤限制
        sharpe_min=0.8,           # 更低的夏普比率要求
        calmar_min=1.0            # 更低的卡玛比率要求
    )
)
```

## 常见配置问题

### Q1: 如何修改交易时间？

A1: 修改 `trading_config.py` 中的 `trading_sessions`：

```python
trading_sessions = {
    "day": ["09:00-11:30", "13:30-15:00"],
    "night": ["21:00-02:30"]
}
```

### Q2: 如何添加新的黑名单品种？

A2: 修改 `trading_config.py` 中的 `blacklist_symbols`：

```python
blacklist_symbols = ["JR", "LR", "RI", "PM", "WH", "RS", "CJ", "NEW_SYMBOL"]
```

### Q3: 如何调整日志级别？

A3: 修改 `system_config.py` 中的日志配置：

```python
logging = LoggingConfig(
    level="DEBUG",  # 改为DEBUG级别
    file_enabled=True,
    console_enabled=True
)
```

### Q4: 如何禁用某个功能模块？

A4: 在相应配置中设置 `enabled=False`：

```python
screening = ScreeningConfig(
    enabled=False,  # 禁用筛选功能
    # 其他参数...
)
```

### Q5: 如何设置数据库连接？

A5: 修改 `system_config.py` 中的数据库配置：

```python
database = DatabaseConfig(
    driver="mysql",
    host="*************",
    port=3306,
    username="trader",
    password="password123",
    database="trading_db"
)
```

通过合理配置这些参数，可以根据不同的交易需求和风险偏好定制系统行为。建议在修改配置后运行配置验证，确保参数的有效性。
