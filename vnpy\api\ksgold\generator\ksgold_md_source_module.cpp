.def("release", &MdApi::release)
.def("init", &MdApi::init)
.def("join", &MdApi::join)
.def("registerFront", &MdApi::registerFront)
.def("registerSpi", &MdApi::registerSpi)
.def("subscribeMarketData", &MdApi::subscribeMarketData)
.def("unSubscribeMarketData", &MdApi::unSubscribeMarketData)
.def("reqUserLogin", &MdApi::reqUserLogin)
.def("reqUserLogout", &MdApi::reqUserLogout)

.def("onFrontConnected", &MdApi::onFrontConnected)
.def("onFrontDisconnected", &MdApi::onFrontDisconnected)
.def("onRspUserLogin", &MdApi::onRspUserLogin)
.def("onRspUserLogout", &MdApi::onRspUserLogout)
.def("onRspError", &MdApi::onRspError)
.def("onRspSubMarketData", &MdApi::onRspSubMarketData)
.def("onRspUnSubMarketData", &MdApi::onRspUnSubMarketData)
.def("onRtnDepthMarketData", &MdApi::onRtnDepthMarketData)
;
