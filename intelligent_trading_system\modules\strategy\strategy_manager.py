"""
策略管理器
提供CTA策略的动态管理、启停控制、参数调整等功能
"""

import json
import os
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum

try:
    from vnpy.app.cta_strategy import Cta<PERSON>ngine
    from vnpy.app.cta_strategy.base import CtaTemplate
except ImportError:
    CtaEngine = None
    CtaTemplate = None

from vnpy.trader.object import PositionData, TickData
from vnpy.trader.constant import Direction

from ...config import get_trading_config
from ...utils.logger import get_logger
from ...utils.helpers import safe_float, format_number


class StrategyStatus(Enum):
    """策略状态"""
    STOPPED = "已停止"
    RUNNING = "运行中"
    INITIALIZING = "初始化中"
    ERROR = "错误"


@dataclass
class StrategyInfo:
    """策略信息"""
    name: str
    class_name: str
    vt_symbol: str
    status: StrategyStatus
    position: int = 0
    pnl: float = 0.0
    parameters: Dict[str, Any] = None
    last_update: datetime = None
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}
        if self.last_update is None:
            self.last_update = datetime.now()


@dataclass
class StrategyPerformance:
    """策略绩效"""
    strategy_name: str
    total_pnl: float = 0.0
    realized_pnl: float = 0.0
    unrealized_pnl: float = 0.0
    total_trades: int = 0
    win_trades: int = 0
    lose_trades: int = 0
    win_rate: float = 0.0
    max_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    start_time: datetime = None
    end_time: datetime = None


class StrategyManager:
    """策略管理器"""
    
    def __init__(self, cta_engine=None):
        """初始化策略管理器"""
        self.logger = get_logger("StrategyManager")
        self.trading_config = get_trading_config()
        self.cta_engine = cta_engine
        
        # 策略信息
        self.strategies: Dict[str, StrategyInfo] = {}
        self.strategy_performances: Dict[str, StrategyPerformance] = {}
        
        # 配置文件路径
        self.strategy_settings_file = "cta_strategy_setting.json"
        self.strategy_data_file = "cta_strategy_data.json"
        self.performance_file = "strategy_performance.json"
        
        # 加载现有策略信息
        self._load_strategy_info()
        
        self.logger.info("策略管理器初始化完成")
    
    def refresh_strategy_info(self) -> bool:
        """刷新策略信息"""
        try:
            if self.cta_engine:
                # 从CTA引擎获取策略信息
                for strategy_name, strategy in self.cta_engine.strategies.items():
                    status = self._get_strategy_status(strategy)
                    
                    strategy_info = StrategyInfo(
                        name=strategy_name,
                        class_name=strategy.__class__.__name__,
                        vt_symbol=strategy.vt_symbol,
                        status=status,
                        position=getattr(strategy, 'pos', 0),
                        pnl=getattr(strategy, 'pnl', 0.0),
                        parameters=self._get_strategy_parameters(strategy),
                        last_update=datetime.now()
                    )
                    
                    self.strategies[strategy_name] = strategy_info
            else:
                # 从配置文件加载策略信息
                self._load_from_files()
            
            self.logger.info(f"刷新策略信息完成，共 {len(self.strategies)} 个策略")
            return True
        
        except Exception as e:
            self.logger.error(f"刷新策略信息失败: {e}")
            return False
    
    def start_strategy(self, strategy_name: str) -> bool:
        """启动策略"""
        try:
            if not self.cta_engine:
                self.logger.error("CTA引擎未初始化")
                return False
            
            if strategy_name not in self.cta_engine.strategies:
                self.logger.error(f"策略 {strategy_name} 不存在")
                return False
            
            strategy = self.cta_engine.strategies[strategy_name]
            
            # 检查策略是否已初始化
            if not strategy.inited:
                self.logger.info(f"策略 {strategy_name} 未初始化，先执行初始化")
                self.cta_engine.init_strategy(strategy_name)
                
                # 等待初始化完成
                import time
                time.sleep(2)
                
                if not strategy.inited:
                    self.logger.error(f"策略 {strategy_name} 初始化失败")
                    return False
            
            # 启动策略
            if not strategy.trading:
                self.cta_engine.start_strategy(strategy_name)
                self.logger.info(f"策略 {strategy_name} 启动成功")
                
                # 更新策略信息
                if strategy_name in self.strategies:
                    self.strategies[strategy_name].status = StrategyStatus.RUNNING
                    self.strategies[strategy_name].last_update = datetime.now()
                
                return True
            else:
                self.logger.warning(f"策略 {strategy_name} 已在运行中")
                return True
        
        except Exception as e:
            self.logger.error(f"启动策略 {strategy_name} 失败: {e}")
            return False
    
    def stop_strategy(self, strategy_name: str) -> bool:
        """停止策略"""
        try:
            if not self.cta_engine:
                self.logger.error("CTA引擎未初始化")
                return False
            
            if strategy_name not in self.cta_engine.strategies:
                self.logger.error(f"策略 {strategy_name} 不存在")
                return False
            
            strategy = self.cta_engine.strategies[strategy_name]
            
            if strategy.trading:
                self.cta_engine.stop_strategy(strategy_name)
                self.logger.info(f"策略 {strategy_name} 停止成功")
                
                # 更新策略信息
                if strategy_name in self.strategies:
                    self.strategies[strategy_name].status = StrategyStatus.STOPPED
                    self.strategies[strategy_name].last_update = datetime.now()
                
                return True
            else:
                self.logger.warning(f"策略 {strategy_name} 已停止")
                return True
        
        except Exception as e:
            self.logger.error(f"停止策略 {strategy_name} 失败: {e}")
            return False
    
    def restart_strategy(self, strategy_name: str) -> bool:
        """重启策略"""
        self.logger.info(f"重启策略 {strategy_name}")
        
        # 先停止
        if not self.stop_strategy(strategy_name):
            return False
        
        # 等待一段时间
        import time
        time.sleep(1)
        
        # 再启动
        return self.start_strategy(strategy_name)
    
    def update_strategy_parameters(self, strategy_name: str, parameters: Dict[str, Any]) -> bool:
        """更新策略参数"""
        try:
            if not self.cta_engine:
                self.logger.error("CTA引擎未初始化")
                return False
            
            if strategy_name not in self.cta_engine.strategies:
                self.logger.error(f"策略 {strategy_name} 不存在")
                return False
            
            # 更新策略参数
            self.cta_engine.edit_strategy(strategy_name, parameters)
            
            # 更新本地记录
            if strategy_name in self.strategies:
                self.strategies[strategy_name].parameters.update(parameters)
                self.strategies[strategy_name].last_update = datetime.now()
            
            self.logger.info(f"策略 {strategy_name} 参数更新成功")
            return True
        
        except Exception as e:
            self.logger.error(f"更新策略 {strategy_name} 参数失败: {e}")
            return False
    
    def get_strategy_status(self, strategy_name: str) -> Optional[StrategyInfo]:
        """获取策略状态"""
        return self.strategies.get(strategy_name)
    
    def get_all_strategies(self) -> Dict[str, StrategyInfo]:
        """获取所有策略信息"""
        return self.strategies.copy()
    
    def get_running_strategies(self) -> List[str]:
        """获取运行中的策略列表"""
        return [name for name, info in self.strategies.items() 
                if info.status == StrategyStatus.RUNNING]
    
    def get_stopped_strategies(self) -> List[str]:
        """获取已停止的策略列表"""
        return [name for name, info in self.strategies.items() 
                if info.status == StrategyStatus.STOPPED]
    
    def start_all_strategies(self) -> Dict[str, bool]:
        """启动所有策略"""
        results = {}
        for strategy_name in self.strategies.keys():
            results[strategy_name] = self.start_strategy(strategy_name)
        return results
    
    def stop_all_strategies(self) -> Dict[str, bool]:
        """停止所有策略"""
        results = {}
        for strategy_name in self.strategies.keys():
            results[strategy_name] = self.stop_strategy(strategy_name)
        return results
    
    def _get_strategy_status(self, strategy) -> StrategyStatus:
        """获取策略状态"""
        if not hasattr(strategy, 'inited') or not strategy.inited:
            return StrategyStatus.INITIALIZING
        elif hasattr(strategy, 'trading') and strategy.trading:
            return StrategyStatus.RUNNING
        else:
            return StrategyStatus.STOPPED
    
    def _get_strategy_parameters(self, strategy) -> Dict[str, Any]:
        """获取策略参数"""
        parameters = {}
        
        # 获取策略的参数列表
        if hasattr(strategy, 'parameters'):
            for param_name in strategy.parameters:
                if hasattr(strategy, param_name):
                    parameters[param_name] = getattr(strategy, param_name)
        
        return parameters
    
    def _load_strategy_info(self):
        """加载策略信息"""
        try:
            if self.cta_engine:
                self.refresh_strategy_info()
            else:
                self._load_from_files()
        except Exception as e:
            self.logger.error(f"加载策略信息失败: {e}")
    
    def _load_from_files(self):
        """从文件加载策略信息"""
        try:
            # 加载策略配置
            if os.path.exists(self.strategy_settings_file):
                with open(self.strategy_settings_file, 'r', encoding='utf-8') as f:
                    strategy_settings = json.load(f)
                
                for strategy_name, config in strategy_settings.items():
                    strategy_info = StrategyInfo(
                        name=strategy_name,
                        class_name=config.get('class_name', ''),
                        vt_symbol=config.get('vt_symbol', ''),
                        status=StrategyStatus.STOPPED,
                        parameters=config.get('setting', {})
                    )
                    self.strategies[strategy_name] = strategy_info
        
        except Exception as e:
            self.logger.error(f"从文件加载策略信息失败: {e}")
    
    def display_strategy_summary(self):
        """显示策略摘要"""
        if not self.strategies:
            self.logger.info("暂无策略信息")
            return
        
        running_count = len(self.get_running_strategies())
        stopped_count = len(self.get_stopped_strategies())
        total_count = len(self.strategies)
        
        self.logger.info("=" * 80)
        self.logger.info("策略管理摘要")
        self.logger.info("=" * 80)
        self.logger.info(f"总策略数: {total_count}")
        self.logger.info(f"运行中: {running_count}, 已停止: {stopped_count}")
        
        self.logger.info("\n策略详情:")
        self.logger.info(f"{'策略名称':20} | {'状态':10} | {'品种':15} | {'持仓':8} | {'盈亏':12}")
        self.logger.info("-" * 80)
        
        for strategy_name, info in self.strategies.items():
            self.logger.info(f"{strategy_name:20} | {info.status.value:10} | {info.vt_symbol:15} | "
                           f"{info.position:8} | {info.pnl:12.2f}")


if __name__ == "__main__":
    # 测试策略管理器
    manager = StrategyManager()
    
    # 显示策略摘要
    manager.display_strategy_summary()
    
    # 刷新策略信息
    manager.refresh_strategy_info()
    
    print("策略管理器测试完成")
