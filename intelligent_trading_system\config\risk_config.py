"""
风险控制配置模块
管理风险控制参数、资金管理、止损止盈等配置
"""

import os
import json
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict, field
from pathlib import Path


@dataclass
class CapitalConfig:
    """资金配置"""
    total_capital: float = 100000.0     # 总资金
    max_symbols: int = 8                # 最大交易品种数
    max_risk_per_symbol: float = 0.02   # 单品种最大风险比例
    max_margin_ratio: float = 0.8       # 最大保证金使用比例
    reserve_ratio: float = 0.2          # 资金储备比例
    max_total_risk: float = 0.15        # 总体最大风险比例
    
    # 仓位控制
    max_lots_per_symbol: int = 4        # 单品种最大手数
    opening_multiplier: int = 2         # 开仓倍数
    fixed_profit_ratio: float = 0.5     # 固定止盈比例
    trailing_profit_ratio: float = 0.5  # 浮动止盈比例
    
    # 风险估算
    estimated_risk_ratio: float = 0.1   # 预估风险比例


@dataclass
class RiskLimitConfig:
    """风险限制配置"""
    # 订单风险控制
    order_flow_limit: int = 50          # 流控限制
    order_flow_clear: int = 1           # 流控清除时间(秒)
    order_size_limit: int = 100         # 单笔委托数量限制
    active_order_limit: int = 50        # 活跃订单限制
    order_limit: int = 4000             # 总订单限制
    order_cancel_limit: int = 500       # 撤单限制
    
    # 交易风险控制
    trade_limit: int = 1000             # 成交限制
    daily_loss_limit: float = 5000.0    # 每日亏损限制
    max_drawdown_limit: float = 0.1     # 最大回撤限制
    
    # 持仓风险控制
    max_position_ratio: float = 0.3     # 最大持仓比例
    concentration_limit: float = 0.2    # 集中度限制
    correlation_limit: float = 0.7      # 相关性限制


@dataclass
class StopLossConfig:
    """止损配置"""
    # ATR止损
    atr_multiplier: float = 2.0         # ATR倍数
    atr_window: int = 20                # ATR周期
    
    # 固定止损
    fixed_stop_loss: float = 0.02       # 固定止损比例
    
    # 时间止损
    max_holding_days: int = 5           # 最大持仓天数
    
    # 浮动止损
    trailing_stop_ratio: float = 0.5    # 浮动止损比例
    trailing_start_ratio: float = 0.3   # 浮动止损启动比例


@dataclass
class TakeProfitConfig:
    """止盈配置"""
    # 固定止盈
    fixed_take_profit: float = 0.05     # 固定止盈比例
    
    # 分批止盈
    partial_profit_ratio: float = 0.5   # 分批止盈比例
    profit_levels: list = field(default_factory=lambda: [0.02, 0.04, 0.06])
    
    # 浮动止盈
    trailing_profit: bool = True        # 是否启用浮动止盈
    AF: float = 0.002                   # 加速因子
    AF_max: float = 0.2                 # 最大加速因子


@dataclass
class RiskConfig:
    """风险控制配置管理类"""

    # 风险控制开关
    risk_management_enabled: bool = True

    # 基础风险属性
    risk_level: str = "B"                    # 风险等级 A/B/C
    max_position_ratio: float = 0.3          # 最大持仓比例
    max_drawdown: float = 0.1                # 最大回撤限制

    # 资金配置
    capital: CapitalConfig = field(default_factory=CapitalConfig)

    # 风险限制
    limits: RiskLimitConfig = field(default_factory=RiskLimitConfig)
    
    # 止损配置
    stop_loss: StopLossConfig = field(default_factory=StopLossConfig)
    
    # 止盈配置
    take_profit: TakeProfitConfig = field(default_factory=TakeProfitConfig)
    
    # 品种风险等级配置
    symbol_risk_levels: Dict[str, Dict[str, Any]] = field(default_factory=lambda: {
        'A': {  # 低风险品种
            'risk_multiplier': 0.8,
            'max_lots': 6,
            'margin_ratio': 0.08
        },
        'B': {  # 中等风险品种
            'risk_multiplier': 1.0,
            'max_lots': 4,
            'margin_ratio': 0.10
        },
        'C': {  # 高风险品种
            'risk_multiplier': 1.5,
            'max_lots': 2,
            'margin_ratio': 0.15
        }
    })
    
    # 资金预设配置
    capital_presets: Dict[str, Dict[str, Any]] = field(default_factory=lambda: {
        '5万元': {
            'total_capital': 50000,
            'max_symbols': 3,
            'max_risk_per_symbol': 0.03,
            'max_margin_ratio': 0.7,
            'max_lots_per_symbol': 2
        },
        '10万元': {
            'total_capital': 100000,
            'max_symbols': 5,
            'max_risk_per_symbol': 0.02,
            'max_margin_ratio': 0.8,
            'max_lots_per_symbol': 3
        },
        '20万元': {
            'total_capital': 200000,
            'max_symbols': 8,
            'max_risk_per_symbol': 0.02,
            'max_margin_ratio': 0.85,
            'max_lots_per_symbol': 4
        },
        '50万元': {
            'total_capital': 500000,
            'max_symbols': 12,
            'max_risk_per_symbol': 0.015,
            'max_margin_ratio': 0.85,
            'max_lots_per_symbol': 6
        }
    })
    
    def get_capital_preset(self, preset_name: str) -> Optional[Dict[str, Any]]:
        """获取资金预设配置"""
        return self.capital_presets.get(preset_name)
    
    def apply_capital_preset(self, preset_name: str) -> bool:
        """应用资金预设配置"""
        preset = self.get_capital_preset(preset_name)
        if not preset:
            return False
        
        # 更新资金配置
        for key, value in preset.items():
            if hasattr(self.capital, key):
                setattr(self.capital, key, value)
        
        return True
    
    def get_symbol_risk_level(self, symbol: str) -> str:
        """获取品种风险等级"""
        # 这里可以根据品种特性返回风险等级
        # 暂时返回默认的中等风险
        return 'B'
    
    def get_symbol_risk_params(self, symbol: str) -> Dict[str, Any]:
        """获取品种风险参数"""
        risk_level = self.get_symbol_risk_level(symbol)
        return self.symbol_risk_levels.get(risk_level, self.symbol_risk_levels['B'])
    
    def calculate_max_lots(self, symbol: str, price: float, margin_ratio: float) -> int:
        """计算最大可开仓手数"""
        # 获取品种风险参数
        risk_params = self.get_symbol_risk_params(symbol)
        
        # 基于资金的最大手数
        available_capital = self.capital.total_capital * self.capital.max_margin_ratio
        margin_per_lot = price * margin_ratio
        max_lots_by_capital = int(available_capital / margin_per_lot) if margin_per_lot > 0 else 0
        
        # 基于风险的最大手数
        risk_capital = self.capital.total_capital * self.capital.max_risk_per_symbol
        estimated_risk_per_lot = margin_per_lot * self.capital.estimated_risk_ratio
        max_lots_by_risk = int(risk_capital / estimated_risk_per_lot) if estimated_risk_per_lot > 0 else 0
        
        # 取最小值
        max_lots = min(
            max_lots_by_capital,
            max_lots_by_risk,
            risk_params['max_lots'],
            self.capital.max_lots_per_symbol
        )
        
        return max(0, max_lots)
    
    def check_risk_limits(self, symbol: str, lots: int, price: float) -> Dict[str, Any]:
        """检查风险限制"""
        result = {
            'passed': True,
            'warnings': [],
            'errors': []
        }
        
        # 检查手数限制
        risk_params = self.get_symbol_risk_params(symbol)
        if lots > risk_params['max_lots']:
            result['errors'].append(f"手数{lots}超过品种最大限制{risk_params['max_lots']}")
            result['passed'] = False
        
        # 检查资金限制
        if lots > self.capital.max_lots_per_symbol:
            result['errors'].append(f"手数{lots}超过单品种最大限制{self.capital.max_lots_per_symbol}")
            result['passed'] = False
        
        return result
    
    @classmethod
    def load_from_file(cls, config_file: str) -> 'RiskConfig':
        """从配置文件加载配置"""
        if not os.path.exists(config_file):
            config = cls()
            config.save_to_file(config_file)
            return config
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            config = cls()
            config._update_from_dict(data)
            return config
            
        except Exception as e:
            print(f"加载风险配置文件失败: {e}")
            return cls()
    
    def save_to_file(self, config_file: str):
        """保存配置到文件"""
        try:
            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(self), f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存风险配置文件失败: {e}")
    
    def _update_from_dict(self, data: Dict[str, Any]):
        """从字典更新配置"""
        for key, value in data.items():
            if key == 'capital' and isinstance(value, dict):
                self.capital = CapitalConfig(**value)
            elif key == 'limits' and isinstance(value, dict):
                self.limits = RiskLimitConfig(**value)
            elif key == 'stop_loss' and isinstance(value, dict):
                self.stop_loss = StopLossConfig(**value)
            elif key == 'take_profit' and isinstance(value, dict):
                self.take_profit = TakeProfitConfig(**value)
            elif hasattr(self, key):
                setattr(self, key, value)

    def validate(self) -> bool:
        """验证配置有效性"""
        try:
            # 验证基础属性
            if self.risk_level not in ['A', 'B', 'C']:
                return False

            if not (0 < self.max_position_ratio <= 1):
                return False

            if not (0 < self.max_drawdown <= 1):
                return False

            # 验证资金配置
            if self.capital.total_capital <= 0:
                return False

            if self.capital.max_symbols <= 0:
                return False

            # 验证风险比例
            if not (0 < self.capital.max_risk_per_symbol <= 1):
                return False

            if not (0 < self.capital.max_margin_ratio <= 1):
                return False

            return True
        except Exception:
            return False

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


# 全局配置实例
_risk_config: Optional[RiskConfig] = None


def get_risk_config() -> RiskConfig:
    """获取风险配置实例"""
    global _risk_config
    if _risk_config is None:
        config_file = os.path.join(
            Path(__file__).parent.parent,
            "config",
            "risk_config.json"
        )
        _risk_config = RiskConfig.load_from_file(config_file)
    return _risk_config


def reload_risk_config():
    """重新加载风险配置"""
    global _risk_config
    _risk_config = None
    return get_risk_config()


if __name__ == "__main__":
    # 测试风险配置模块
    config = RiskConfig()
    
    print("风险配置:")
    print(f"风险管理启用: {config.risk_management_enabled}")
    print(f"总资金: {config.capital.total_capital}")
    print(f"最大品种数: {config.capital.max_symbols}")
    print(f"单品种最大风险: {config.capital.max_risk_per_symbol}")
    
    # 测试资金预设
    print(f"\n可用资金预设: {list(config.capital_presets.keys())}")
    
    # 测试风险计算
    max_lots = config.calculate_max_lots("RB888", 4000, 0.1)
    print(f"RB888最大可开仓手数: {max_lots}")
    
    # 保存配置文件
    config_file = os.path.join(
        Path(__file__).parent.parent,
        "config",
        "risk_config.json"
    )
    config.save_to_file(config_file)
    print(f"配置已保存到: {config_file}")
