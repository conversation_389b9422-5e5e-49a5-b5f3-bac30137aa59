"""
资金分配器
基于品种筛选结果和风险配置进行资金分配
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import json

from ...config import get_trading_config, get_risk_config
from ...utils.logger import get_logger
from ...utils.helpers import safe_float, format_number, format_percentage


class CapitalAllocator:
    """资金分配器"""
    
    def __init__(self, capital_preset: str = "10万元"):
        """初始化资金分配器"""
        self.logger = get_logger("CapitalAllocator")
        self.trading_config = get_trading_config()
        self.risk_config = get_risk_config()
        
        # 获取资金配置
        self.capital_config = self.risk_config.get_capital_preset(capital_preset)
        if not self.capital_config:
            self.logger.warning(f"未找到资金预设 {capital_preset}，使用默认配置")
            self.capital_config = self.risk_config.get_capital_preset("10万元")
        
        self.total_capital = self.capital_config.total_capital
        self.max_symbols = self.capital_config.max_symbols
        self.max_risk_per_symbol = self.capital_config.max_risk_per_symbol
        self.max_margin_ratio = self.capital_config.max_margin_ratio
        self.reserve_ratio = self.capital_config.reserve_ratio
        
        # 激进模式配置
        self.aggressive_mode = self.capital_config.aggressive_mode
        self.opening_multiplier = self.capital_config.opening_multiplier
        self.total_margin_limit = self.capital_config.total_margin_limit
        
        self.logger.info(f"资金分配器初始化完成，总资金: {format_number(self.total_capital)}元")
    
    def calculate_position_size(self, symbol: str, current_price: float, 
                              score: float, risk_level: str = "中等风险") -> Dict[str, Any]:
        """计算品种仓位配置"""
        try:
            # 获取品种基础信息
            margin_ratio = self._get_margin_ratio(symbol)
            contract_size = self._get_contract_size(symbol)
            
            if margin_ratio <= 0 or contract_size <= 0 or current_price <= 0:
                return self._create_empty_allocation(symbol, "缺少基础数据")
            
            # 计算每手保证金
            margin_per_lot = current_price * contract_size * margin_ratio
            
            # 获取风险系数
            risk_multiplier = self._get_risk_multiplier(symbol, risk_level)
            
            # 计算各种限制下的最大手数
            max_lots_by_margin = self._calculate_max_lots_by_margin(margin_per_lot)
            max_lots_by_risk = self._calculate_max_lots_by_risk(margin_per_lot, risk_multiplier)
            max_lots_by_position = self._calculate_max_lots_by_position(margin_per_lot, score)
            
            # 取最小值作为推荐手数
            recommended_lots = min(max_lots_by_margin, max_lots_by_risk, max_lots_by_position)
            recommended_lots = max(0, recommended_lots)  # 确保非负
            
            # 计算开仓手数（考虑开仓倍数）
            opening_lots = recommended_lots * self.opening_multiplier if recommended_lots > 0 else 0
            
            # 计算资金使用情况
            total_margin_needed = margin_per_lot * opening_lots
            estimated_risk = self._calculate_estimated_risk(margin_per_lot, opening_lots, risk_multiplier)
            
            # 计算比例
            risk_ratio = (estimated_risk / self.total_capital * 100) if self.total_capital > 0 else 0
            margin_ratio_pct = (total_margin_needed / self.total_capital * 100) if self.total_capital > 0 else 0
            
            # 确定限制因素
            limiting_factors = self._identify_limiting_factors(
                max_lots_by_margin, max_lots_by_risk, max_lots_by_position, recommended_lots
            )
            
            return {
                '品种': symbol,
                '推荐手数': recommended_lots,
                '开仓手数': opening_lots,
                '每手保证金': margin_per_lot,
                '所需保证金': total_margin_needed,
                '预估风险': estimated_risk,
                '风险比例(%)': risk_ratio,
                '资金使用比例(%)': margin_ratio_pct,
                '风险等级': risk_level,
                '风险系数': risk_multiplier,
                '限制因素': limiting_factors,
                # 内部使用的英文键名
                'symbol': symbol,
                'recommended_lots': recommended_lots,
                'opening_lots': opening_lots,
                'margin_per_lot': margin_per_lot,
                'total_margin_needed': total_margin_needed,
                'estimated_risk': estimated_risk,
                'risk_ratio': risk_ratio,
                'margin_usage_ratio': margin_ratio_pct,
                'calculation_details': {
                    'max_lots_by_margin': max_lots_by_margin,
                    'max_lots_by_risk': max_lots_by_risk,
                    'max_lots_by_position': max_lots_by_position,
                    'margin_ratio': margin_ratio,
                    'contract_size': contract_size,
                    'current_price': current_price
                }
            }
        
        except Exception as e:
            self.logger.error(f"计算品种 {symbol} 仓位配置失败: {e}")
            return self._create_empty_allocation(symbol, f"计算错误: {str(e)}")
    
    def _create_empty_allocation(self, symbol: str, reason: str) -> Dict[str, Any]:
        """创建空的分配结果"""
        return {
            '品种': symbol,
            '推荐手数': 0,
            '开仓手数': 0,
            '每手保证金': 0,
            '所需保证金': 0,
            '预估风险': 0,
            '风险比例(%)': 0,
            '资金使用比例(%)': 0,
            '风险等级': '未知',
            '风险系数': 1.0,
            '限制因素': [reason],
            'symbol': symbol,
            'recommended_lots': 0,
            'opening_lots': 0,
            'margin_per_lot': 0,
            'total_margin_needed': 0,
            'estimated_risk': 0,
            'risk_ratio': 0,
            'margin_usage_ratio': 0
        }
    
    def _get_margin_ratio(self, symbol: str) -> float:
        """获取保证金比例"""
        # 从配置获取或使用默认值
        default_margins = {
            'RB': 0.08, 'HC': 0.08, 'I': 0.08, 'J': 0.08, 'JM': 0.08,
            'CU': 0.07, 'AL': 0.07, 'ZN': 0.07, 'PB': 0.07, 'NI': 0.07,
            'AU': 0.06, 'AG': 0.08, 'RU': 0.09, 'BU': 0.08, 'FU': 0.10,
            'C': 0.05, 'CS': 0.05, 'A': 0.05, 'M': 0.05, 'Y': 0.05,
            'CF': 0.05, 'SR': 0.06, 'TA': 0.06, 'MA': 0.06, 'RM': 0.06
        }
        
        product_code = self._extract_product_code(symbol)
        return default_margins.get(product_code, 0.08)
    
    def _get_contract_size(self, symbol: str) -> int:
        """获取合约乘数"""
        default_sizes = {
            'RB': 10, 'HC': 10, 'I': 100, 'J': 100, 'JM': 60,
            'CU': 5, 'AL': 5, 'ZN': 5, 'PB': 5, 'NI': 1,
            'AU': 1000, 'AG': 15, 'RU': 10, 'BU': 10, 'FU': 50,
            'C': 10, 'CS': 10, 'A': 10, 'M': 10, 'Y': 10,
            'CF': 5, 'SR': 10, 'TA': 5, 'MA': 10, 'RM': 10
        }
        
        product_code = self._extract_product_code(symbol)
        return default_sizes.get(product_code, 10)
    
    def _extract_product_code(self, symbol: str) -> str:
        """提取品种代码"""
        if '.' in symbol:
            symbol = symbol.split('.')[0]
        
        # 移除数字部分
        import re
        return re.sub(r'\d+', '', symbol).upper()
    
    def _get_risk_multiplier(self, symbol: str, risk_level: str) -> float:
        """获取风险系数"""
        # 基础风险系数
        base_multipliers = {
            '低风险': 0.8,
            '中低风险': 1.0,
            '中等风险': 1.2,
            '中高风险': 1.5,
            '高风险': 2.0
        }
        
        base_multiplier = base_multipliers.get(risk_level, 1.2)
        
        # 品种特定调整
        product_code = self._extract_product_code(symbol)
        high_risk_products = ['RB', 'HC', 'I', 'J', 'JM']  # 黑色系
        
        if product_code in high_risk_products:
            return base_multiplier * 1.2
        else:
            return base_multiplier
    
    def _calculate_max_lots_by_margin(self, margin_per_lot: float) -> int:
        """基于保证金限制计算最大手数"""
        available_margin = self.total_capital * (1 - self.reserve_ratio)
        if self.aggressive_mode:
            available_margin = self.total_capital * self.total_margin_limit
        
        return int(available_margin / margin_per_lot / self.opening_multiplier) if margin_per_lot > 0 else 0
    
    def _calculate_max_lots_by_risk(self, margin_per_lot: float, risk_multiplier: float) -> int:
        """基于风险限制计算最大手数"""
        max_risk_capital = self.total_capital * self.max_risk_per_symbol
        estimated_risk_per_lot = margin_per_lot * 0.1 * risk_multiplier  # 假设10%的风险比例
        
        return int(max_risk_capital / estimated_risk_per_lot / self.opening_multiplier) if estimated_risk_per_lot > 0 else 0
    
    def _calculate_max_lots_by_position(self, margin_per_lot: float, score: float) -> int:
        """基于仓位限制计算最大手数"""
        # 根据评分调整最大仓位比例
        if score >= 80:
            max_position_ratio = 0.4  # 40%
        elif score >= 60:
            max_position_ratio = 0.3  # 30%
        elif score >= 40:
            max_position_ratio = 0.2  # 20%
        else:
            max_position_ratio = 0.1  # 10%
        
        max_position_capital = self.total_capital * max_position_ratio
        return int(max_position_capital / margin_per_lot / self.opening_multiplier) if margin_per_lot > 0 else 0
    
    def _calculate_estimated_risk(self, margin_per_lot: float, opening_lots: int, risk_multiplier: float) -> float:
        """计算预估风险"""
        return margin_per_lot * opening_lots * 0.1 * risk_multiplier
    
    def _identify_limiting_factors(self, max_by_margin: int, max_by_risk: int, 
                                 max_by_position: int, final_lots: int) -> List[str]:
        """识别限制因素"""
        factors = []
        
        if final_lots == max_by_margin and max_by_margin <= max_by_risk and max_by_margin <= max_by_position:
            factors.append("保证金限制")
        if final_lots == max_by_risk and max_by_risk <= max_by_margin and max_by_risk <= max_by_position:
            factors.append("风险限制")
        if final_lots == max_by_position and max_by_position <= max_by_margin and max_by_position <= max_by_risk:
            factors.append("仓位限制")
        
        if not factors:
            factors.append("综合限制")
        
        return factors

    def allocate_capital(self, screening_results: List[Dict[str, Any]],
                        current_prices: Dict[str, float] = None) -> pd.DataFrame:
        """基于筛选结果进行资金分配"""
        self.logger.info("开始资金分配...")

        if not screening_results:
            self.logger.warning("没有筛选结果，无法进行资金分配")
            return pd.DataFrame()

        allocation_results = []

        # 过滤出可交易的品种
        tradeable_results = [
            result for result in screening_results
            if result.get('状态') in ['重点关注', '准备入场'] and not result.get('排除原因')
        ]

        self.logger.info(f"可交易品种数量: {len(tradeable_results)}")

        # 限制品种数量
        if len(tradeable_results) > self.max_symbols:
            tradeable_results = tradeable_results[:self.max_symbols]
            self.logger.info(f"限制品种数量为: {self.max_symbols}")

        for result in tradeable_results:
            symbol = result['品种']
            score = result.get('综合评分', 0)

            # 获取当前价格
            if current_prices and symbol in current_prices:
                current_price = current_prices[symbol]
            else:
                current_price = self._get_estimated_price(symbol)

            # 确定风险等级
            volatility_score = result.get('波动率评分', 50)
            trend_score = result.get('突破强度', 50)
            risk_level = self._determine_risk_level(volatility_score, trend_score)

            # 计算仓位配置
            allocation = self.calculate_position_size(symbol, current_price, score, risk_level)

            # 添加筛选信息
            allocation.update({
                '综合评分': score,
                '状态': result.get('状态', ''),
                '趋势方向': result.get('趋势方向', ''),
                '最佳周期': result.get('最佳周期', ''),
                '波动率评分': volatility_score,
                '突破强度': trend_score
            })

            allocation_results.append(allocation)

        # 转换为DataFrame
        if allocation_results:
            df_allocation = pd.DataFrame(allocation_results)

            # 只保留可交易的品种
            df_allocation = df_allocation[df_allocation['recommended_lots'] > 0]

            # 按综合评分排序
            df_allocation = df_allocation.sort_values('综合评分', ascending=False)

            # 激进模式下调整保证金
            if self.aggressive_mode:
                df_allocation = self._adjust_positions_for_margin(df_allocation)

            self.logger.info(f"资金分配完成，共配置 {len(df_allocation)} 个品种")
            return df_allocation
        else:
            self.logger.warning("没有可配置的品种")
            return pd.DataFrame()

    def _get_estimated_price(self, symbol: str) -> float:
        """获取估算价格"""
        default_prices = {
            'RB': 3500, 'HC': 3200, 'I': 800, 'J': 2000, 'JM': 1500,
            'CU': 70000, 'AL': 18000, 'ZN': 25000, 'PB': 15000, 'NI': 130000,
            'AU': 500, 'AG': 5000, 'RU': 12000, 'BU': 3000, 'FU': 3000,
            'C': 2500, 'CS': 2800, 'A': 4000, 'M': 3500, 'Y': 8500,
            'CF': 15000, 'SR': 6000, 'TA': 6000, 'MA': 2800, 'RM': 2500
        }

        product_code = self._extract_product_code(symbol)
        return default_prices.get(product_code, 3000)

    def _determine_risk_level(self, volatility_score: float, trend_score: float) -> str:
        """确定风险等级"""
        # 综合波动率和趋势强度确定风险等级
        risk_score = volatility_score * 0.6 + (100 - trend_score) * 0.4

        if risk_score >= 80:
            return '高风险'
        elif risk_score >= 60:
            return '中高风险'
        elif risk_score >= 40:
            return '中等风险'
        elif risk_score >= 20:
            return '中低风险'
        else:
            return '低风险'

    def _adjust_positions_for_margin(self, df_allocation: pd.DataFrame) -> pd.DataFrame:
        """调整仓位以满足保证金限制"""
        if df_allocation.empty:
            return df_allocation

        # 计算总保证金需求
        total_margin = df_allocation['total_margin_needed'].sum()
        margin_limit = self.total_capital * self.total_margin_limit

        self.logger.info(f"保证金检查: 需求 {format_number(total_margin)}元, 限制 {format_number(margin_limit)}元")

        if total_margin <= margin_limit:
            return df_allocation

        self.logger.warning("保证金超限，开始调整仓位...")

        # 按评分排序，优先保留高分品种
        df_sorted = df_allocation.sort_values('综合评分', ascending=False).copy()

        adjusted_results = []
        cumulative_margin = 0

        for _, row in df_sorted.iterrows():
            symbol = row['symbol']
            original_lots = row['recommended_lots']
            margin_per_lot = row['margin_per_lot']

            # 计算剩余可用保证金
            remaining_margin = margin_limit - cumulative_margin

            # 计算可分配的手数
            max_affordable_lots = int(remaining_margin / (margin_per_lot * self.opening_multiplier))
            adjusted_lots = min(original_lots, max_affordable_lots)

            if adjusted_lots > 0:
                # 重新计算相关数值
                opening_lots = adjusted_lots * self.opening_multiplier
                total_margin_needed = margin_per_lot * opening_lots
                estimated_risk = self._calculate_estimated_risk(
                    margin_per_lot, opening_lots, row.get('风险系数', 1.2)
                )

                # 更新行数据
                updated_row = row.copy()
                updated_row['推荐手数'] = adjusted_lots
                updated_row['recommended_lots'] = adjusted_lots
                updated_row['开仓手数'] = opening_lots
                updated_row['opening_lots'] = opening_lots
                updated_row['所需保证金'] = total_margin_needed
                updated_row['total_margin_needed'] = total_margin_needed
                updated_row['预估风险'] = estimated_risk
                updated_row['estimated_risk'] = estimated_risk
                updated_row['风险比例(%)'] = (estimated_risk / self.total_capital * 100)
                updated_row['risk_ratio'] = updated_row['风险比例(%)']
                updated_row['资金使用比例(%)'] = (total_margin_needed / self.total_capital * 100)
                updated_row['margin_usage_ratio'] = updated_row['资金使用比例(%)']

                adjusted_results.append(updated_row)
                cumulative_margin += total_margin_needed

                if adjusted_lots < original_lots:
                    self.logger.info(f"{symbol}: 调整手数 {original_lots} -> {adjusted_lots}")
            else:
                self.logger.info(f"{symbol}: 保证金不足，移除")

        if adjusted_results:
            return pd.DataFrame(adjusted_results)
        else:
            return pd.DataFrame()

    def generate_allocation_report(self, df_allocation: pd.DataFrame) -> Dict[str, Any]:
        """生成分配报告"""
        if df_allocation.empty:
            return {'error': '没有分配结果'}

        try:
            # 统计信息
            total_symbols = len(df_allocation)
            total_lots = df_allocation['recommended_lots'].sum()
            total_opening_lots = df_allocation['opening_lots'].sum()
            total_margin = df_allocation['total_margin_needed'].sum()
            total_risk = df_allocation['estimated_risk'].sum()

            # 计算使用率
            margin_utilization = (total_margin / self.total_capital * 100) if self.total_capital > 0 else 0
            risk_utilization = (total_risk / self.total_capital * 100) if self.total_capital > 0 else 0

            # 风险等级分布
            risk_distribution = df_allocation['风险等级'].value_counts().to_dict()

            # 状态分布
            status_distribution = df_allocation['状态'].value_counts().to_dict()

            report = {
                'timestamp': datetime.now().isoformat(),
                'capital_config': {
                    'total_capital': self.total_capital,
                    'max_symbols': self.max_symbols,
                    'aggressive_mode': self.aggressive_mode,
                    'opening_multiplier': self.opening_multiplier
                },
                'allocation_summary': {
                    'total_symbols': total_symbols,
                    'total_lots': total_lots,
                    'total_opening_lots': total_opening_lots,
                    'total_margin': total_margin,
                    'total_risk': total_risk,
                    'margin_utilization': margin_utilization,
                    'risk_utilization': risk_utilization
                },
                'distributions': {
                    'risk_levels': risk_distribution,
                    'status': status_distribution
                },
                'top_allocations': df_allocation.head(10).to_dict('records')
            }

            return report

        except Exception as e:
            self.logger.error(f"生成分配报告失败: {e}")
            return {'error': str(e)}

    def display_allocation_results(self, df_allocation: pd.DataFrame):
        """显示分配结果"""
        if df_allocation.empty:
            self.logger.warning("没有分配结果可显示")
            return

        # 计算汇总信息
        total_symbols = len(df_allocation)
        total_lots = df_allocation['recommended_lots'].sum()
        total_opening_lots = df_allocation['opening_lots'].sum()
        total_margin = df_allocation['total_margin_needed'].sum()
        total_risk = df_allocation['estimated_risk'].sum()

        margin_utilization = (total_margin / self.total_capital * 100) if self.total_capital > 0 else 0
        risk_utilization = (total_risk / self.total_capital * 100) if self.total_capital > 0 else 0

        self.logger.info("=" * 120)
        self.logger.info("资金分配结果")
        self.logger.info("=" * 120)
        self.logger.info(f"总资金: {format_number(self.total_capital)}元 | 配置品种: {total_symbols}个")
        self.logger.info(f"推荐手数: {total_lots}手 | 开仓手数: {total_opening_lots}手")
        self.logger.info(f"保证金需求: {format_number(total_margin)}元 ({margin_utilization:.1f}%)")
        self.logger.info(f"预估风险: {format_number(total_risk)}元 ({risk_utilization:.1f}%)")

        if self.aggressive_mode:
            self.logger.info("⚡ 激进模式已启用")

        self.logger.info("=" * 120)

        # 显示详细结果
        self.logger.info(f"{'品种':12} | {'评分':6} | {'状态':8} | {'推荐':6} | {'开仓':6} | {'保证金':12} | {'风险':12} | {'风险%':8}")
        self.logger.info("-" * 120)

        for _, row in df_allocation.iterrows():
            symbol = row['品种']
            score = row['综合评分']
            status = row['状态']
            recommended = row['推荐手数']
            opening = row['开仓手数']
            margin = row['所需保证金']
            risk = row['预估风险']
            risk_pct = row['风险比例(%)']

            self.logger.info(f"{symbol:12} | {score:6.1f} | {status:8} | {recommended:6} | {opening:6} | "
                           f"{margin:12,.0f} | {risk:12,.0f} | {risk_pct:8.1f}%")

        self.logger.info("-" * 120)
        self.logger.info(f"{'合计':12} | {'':<6} | {'':<8} | {total_lots:6} | {total_opening_lots:6} | "
                        f"{total_margin:12,.0f} | {total_risk:12,.0f} | {risk_utilization:8.1f}%")

        # 策略说明
        if self.aggressive_mode:
            self.logger.info("\n⚡ 激进模式策略说明:")
            self.logger.info("1. 全仓入场: 一次性建立全部仓位")
            self.logger.info("2. 固定止盈: 盈利达2倍ATR时，平仓50%仓位")
            self.logger.info("3. 浮动止盈: 剩余仓位使用1.5倍ATR移动止损")
            self.logger.info("4. 保证金释放: 固定止盈后保证金减少40-50%")
            self.logger.info("5. 隔夜控制: 波动率>70品种在14:55前平仓")

    def save_allocation_results(self, df_allocation: pd.DataFrame, filename: str = None) -> str:
        """保存分配结果到CSV文件"""
        if df_allocation.empty:
            self.logger.warning("没有结果可保存")
            return ""

        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"capital_allocation_results_{timestamp}.csv"

            # 选择要保存的列
            columns_to_save = [
                '品种', '综合评分', '状态', '趋势方向', '最佳周期',
                '推荐手数', '开仓手数', '每手保证金', '所需保证金', '预估风险',
                '风险比例(%)', '资金使用比例(%)', '风险等级', '限制因素'
            ]

            # 确保所有列都存在
            available_columns = [col for col in columns_to_save if col in df_allocation.columns]
            df_to_save = df_allocation[available_columns].copy()

            # 保存到CSV
            df_to_save.to_csv(filename, index=False, encoding='utf-8-sig')

            self.logger.info(f"分配结果已保存到: {filename}")
            return filename

        except Exception as e:
            self.logger.error(f"保存分配结果失败: {e}")
            return ""

    def save_allocation_report(self, report: Dict[str, Any], filename: str = None) -> str:
        """保存分配报告"""
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"allocation_report_{timestamp}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)

            self.logger.info(f"分配报告已保存到: {filename}")
            return filename

        except Exception as e:
            self.logger.error(f"保存分配报告失败: {e}")
            return ""

    def get_allocation_summary(self, df_allocation: pd.DataFrame) -> Dict[str, Any]:
        """获取分配摘要"""
        if df_allocation.empty:
            return {'total_symbols': 0, 'total_margin': 0, 'total_risk': 0}

        return {
            'total_symbols': len(df_allocation),
            'total_lots': df_allocation['recommended_lots'].sum(),
            'total_opening_lots': df_allocation['opening_lots'].sum(),
            'total_margin': df_allocation['total_margin_needed'].sum(),
            'total_risk': df_allocation['estimated_risk'].sum(),
            'margin_utilization': (df_allocation['total_margin_needed'].sum() / self.total_capital * 100),
            'risk_utilization': (df_allocation['estimated_risk'].sum() / self.total_capital * 100),
            'avg_score': df_allocation['综合评分'].mean(),
            'risk_distribution': df_allocation['风险等级'].value_counts().to_dict()
        }


if __name__ == "__main__":
    # 测试资金分配器
    allocator = CapitalAllocator("10万元")

    # 模拟筛选结果
    mock_results = [
        {
            '品种': 'RB888.SHFE',
            '综合评分': 85.0,
            '状态': '重点关注',
            '趋势方向': '多头',
            '最佳周期': '15分钟',
            '波动率评分': 75.0,
            '突破强度': 80.0
        },
        {
            '品种': 'CU888.SHFE',
            '综合评分': 72.0,
            '状态': '准备入场',
            '趋势方向': '多头',
            '最佳周期': '30分钟',
            '波动率评分': 65.0,
            '突破强度': 70.0
        }
    ]

    # 执行资金分配
    df_allocation = allocator.allocate_capital(mock_results)

    # 显示结果
    allocator.display_allocation_results(df_allocation)

    # 生成报告
    report = allocator.generate_allocation_report(df_allocation)
    print(f"\n分配报告: {report}")

    print("资金分配器测试完成")
