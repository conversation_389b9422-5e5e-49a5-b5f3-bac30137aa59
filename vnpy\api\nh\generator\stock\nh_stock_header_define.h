#define ONFRONTCONNECTED 0
#define ONFRONTDISCONNECTED 1
#define ONHEARTBEATWARNING 2
#define ONPACKAGESTART 3
#define ONPACKAGEEND 4
#define ONRSPSUBSCRIBETOPIC 5
#define ONRSPUSERLOGIN 6
#define ONRSPUSERLOGOUT 7
#define ONRSPUSERPASSWORDUPDATE 8
#define ONRSPSTOCKINSERT 9
#define ONRSPSTOCKCANCEL 10
#define ONRSPOPTIONSINSERT 11
#define ONRSPOPTIONSCANCEL 12
#define ONRSPQUOTEINSERT 13
#define ONRSPFORQUOTE 14
#define ONRSPQUOTECANCEL 15
#define ONRSPSTOCKLOCK 16
#define ONRSPEXERCISE 17
#define ONRSPEXERCISECANCEL 18
#define ONRSPQRYPARTACCOUNT 19
#define ONRSPQRYSTOCKORDER 20
#define ONRSPQRYOPTIONSORDER 21
#define ONRSPQRYQUOTEORDER 22
#define ONRSPQRYSTOCKTRAD<PERSON> 23
#define ONRSPQRYOPTIONSTRADE 24
#define ONRSPQRYPOSITION 25
#define ONRSPQRYTOPIC 26
#define ONRSPQRYSTOCK 27
#define ONRSPQRYOPTIONS 28
#define ONRTNOPTIONSORDER 29
#define ONRTNSTOCKORDER 30
#define ONRTNQUOTEORDER 31
#define ONRTNOPTIONSTRADE 32
#define ONRTNSTOCKTRADE 33
#define ONRTNEXERCISE 34
#define ONRSPQRYRATE 35
#define ONRSPQRYCLIENT 36
#define ONRSPQRYCLIENTMARGIN 37
#define ONRSPQRYEXERCISE 38
#define ONRTNWITHDRAWDEPOSIT 39
#define ONRSPMARGINCOMBACTION 40
#define ONRTNMARGINCOMBACTION 41
#define ONRSPQRYSSECOMBPOSITION 42
#define ONRSPCOMBEXERCISE 43
