void processFrontConnected(Task *task);

void processFrontDisConnected(Task *task);

void processHeartBeatWarning(Task *task);

void processRspError(Task *task);

void processRtnMarketData(Task *task);

void processRspUtpLogin(Task *task);

void processRspUtpLogout(Task *task);

void processRspSubscribe(Task *task);

void processRspUnSubscribe(Task *task);

void processRspQryExchange(Task *task);

void processRspQryInstrument(Task *task);

