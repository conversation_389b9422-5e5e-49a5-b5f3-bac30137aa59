#
# OES API接口库的配置文件样例
#

##############################################
# 客户端配置
##############################################

[oes_client]
ordServer = 1 tcp://106.15.58.119:6101, 2 tcp://106.15.58.119:6102, 1 tcp://************:6101
rptServer = 1 tcp://106.15.58.119:6301, 2 tcp://106.15.58.119:6302, 1 tcp://************:6301
qryServer = 1 tcp://106.15.58.119:6401, 2 tcp://106.15.58.119:6402, 1 tcp://************:6401

username = customer1
# 密码支持明文和MD5两种格式 (如 txt:XXX 或 md5:XXX..., 不带前缀则默认为明文)
password = 123456
heartBtInt = 30

# 客户端硬盘序列号
driverId =

# 客户端环境号, 用于区分不同客户端实例上报的委托申报, 取值由客户端自行分配
# 取值范围 [0~99] ([100~127] 为保留区间，客户端应避免使用)
clEnvId = 0

# 待订阅的客户端环境号
# - 大于0, 区分环境号, 仅订阅环境号对应的回报数据
# - 小于等于0, 不区分环境号, 订阅该客户下的所有回报数据
rpt.subcribeEnvId = 0

# 待订阅的回报消息类型集合
# - 0:默认 (等价于: 1,2,4,8,0x10,0x20,0x40), 0xFFFF:所有
# - 1:OES业务拒绝, 2:OES委托已生成, 4:交易所委托回报, 8:交易所成交回报, 0x10:出入金委托执行报告,
# - 0x20:资金变动信息, 0x40:持仓变动信息, 0x80:市场状态信息, 0x100:通知消息回报
# 要订阅多个数据种类, 可以用逗号或空格分隔, 或者设置为并集值, 如:
# - rpt.subcribeRptTypes = 1,4,8,0x10
# - 或等价的: rpt.subcribeRptTypes = 0x1D
rpt.subcribeRptTypes = 0

# 服务器集群的集群类型 (1: 基于复制集的高可用集群, 2: 基于对等节点的服务器集群, 0: 默认为基于复制集的高可用集群)
clusterType = 0

# 套接字参数配置 (可选配置)
soRcvbuf = 8192
soSndbuf = 8192
connTimeoutMs = 5000
tcpNodelay = 1
quickAck = 1
keepalive = 1
keepIdle = 60
keepIntvl = 5
keepCnt = 9
#localSendingIp = ************          # 本地绑定的网络设备接口的IP地址 (适用于发送端)
#localSendingPort = 7001                # 本地绑定的端口地址 (适用于发送端)


##############################################
# 异步API相关的扩展配置
##############################################

[oes_client.async_api]
# 异步队列的大小 (可缓存的消息数量)
#asyncQueueSize = 100000
# 是否启动独立的回调线程来执行回调处理 (否则将直接在通信线程下执行回调处理)
isAsyncCallbackAble = no
# 是否使用忙等待模式 (TRUE:延迟更低但CPU会被100%占用; FALSE:延迟和CPU使用率相对均衡)
isBusyPollAble = yes
# 是否在启动前预创建并校验所有的连接
isPreconnectAble = no

# 异步I/O线程配置 (可选配置)
ioThread.enable = no                    # I/O线程的使能标志
ioThread.isOutputSimplify = no          # 是否采用精简模式输出数据
ioThread.dataOutputFormat = json        # 数据输出格式 (json, binary, none)
# 数据文件路径 (为空:不输出数据; stdout/stderr:标准输出)
ioThread.dataOutputPath = ./rptdata.txt
# 统计信息文件路径 (为空:不输出数据; stdout/stderr:标准输出)
ioThread.statsOutputPath = ./stats.txt


##############################################
# 日志配置
##############################################

[log]
log.root_category = DEBUG, console_log
log.mode=file
log.threshold=TRACE
log.file=./api.log
log.file.max_file_length=300
log.file.max_backup_index=3

[console_log]
log.mode=console
log.threshold=ERROR


############################################################
# CPU亲和性设置
#
# 配置说明:
# - CPU编号从1开始, CPU编号列表以逗号或空格分割
# - 使能标志 (cpuset.enable), 若未设置则默认启用亲和性设置
# - 默认值 (cpuset.default), CPU亲和性配置的默认值 (默认的CPU绑定配置)
############################################################

[cpuset]
enable = no
default = 1

# 异步API线程的CPU绑定配置
oesapi_report = 5
#oesapi_callback = 5
oesapi_io_thread = 4

# 委托线程和查询线程的CPU绑定配置示例 (API不会自动加载, 需要由应用层负责处理)
order = 3
query = 2
