# 智能量化交易系统用户指南

## 目录
1. [系统概述](#系统概述)
2. [快速开始](#快速开始)
3. [系统配置](#系统配置)
4. [功能模块](#功能模块)
5. [日常操作](#日常操作)
6. [故障排除](#故障排除)
7. [常见问题](#常见问题)
8. [系统架构](#系统架构)

## 系统概述

智能量化交易系统是基于vnpy框架开发的全自动化期货交易系统，提供以下核心功能：

- **自动化品种筛选**: 基于技术指标和市场数据的智能品种筛选
- **动态资金分配**: 根据风险评估和市场条件的智能资金分配
- **策略管理**: CTA策略的动态启停和参数优化
- **风险控制**: 多层次风险管理和实时监控
- **主力合约切换**: 自动识别和切换主力合约
- **定时调度**: 按交易时间自动执行各项任务

## 快速开始

### 1. 环境准备

确保已安装以下依赖：
```bash
pip install vnpy
pip install ta-lib
pip install psutil
pip install schedule
```

### 2. 配置文件设置

复制并修改配置文件：
```bash
cd intelligent_trading_system/config
# 根据实际情况修改配置文件
```

### 3. 启动系统

```bash
# 正常运行模式（推荐）
python start_system.py

# 测试模式
python start_system.py --mode test

# 单进程调试模式
python start_system.py --mode single

# 查看配置
python start_system.py --mode config

# 查看状态
python start_system.py --mode status
```

## 系统配置

### 系统配置 (system_config.py)

```python
# 数据库配置
database:
  path: "vnpy_data.db"          # 数据库文件路径
  driver: "sqlite"              # 数据库驱动

# 日志配置
logging:
  level: "INFO"                 # 日志级别
  file_enabled: true            # 是否启用文件日志
  max_file_size: 10             # 最大文件大小(MB)
  backup_count: 5               # 备份文件数量

# 调度器配置
scheduler:
  timezone: "Asia/Shanghai"     # 时区设置
  max_workers: 4                # 最大工作线程数
```

### 交易配置 (trading_config.py)

```python
# 基本配置
default_strategy: "fenzhouqiplus_strategy"  # 默认策略
blacklist_symbols: ["JR", "LR", "RI", "PM", "WH", "RS", "CJ"]  # 黑名单品种

# 筛选配置
screening:
  trend_threshold: 0.65         # 趋势强度阈值
  volatility_min: 1.8          # 最小波动率
  volatility_max: 3.5          # 最大波动率
  liquidity_peak_max: 5.0      # 最大流动性峰值
  cost_ratio_max: 0.3          # 最大成本比率

# 策略配置
strategy:
  default_size: 1               # 默认手数
  max_positions: 10             # 最大持仓数
```

### 风险配置 (risk_config.py)

```python
# 基本风险控制
max_total_position: 0.8         # 最大总仓位比例
max_single_position: 0.1        # 单品种最大仓位比例
max_drawdown_limit: 0.15        # 最大回撤限制

# 资金分配预设
capital_allocation:
  conservative:                 # 保守型
    risk_ratio: 0.02
    margin_ratio: 0.15
  moderate:                     # 稳健型
    risk_ratio: 0.03
    margin_ratio: 0.20
  aggressive:                   # 激进型
    risk_ratio: 0.05
    margin_ratio: 0.30
```

## 功能模块

### 1. 品种筛选模块

**执行时间**: 每日08:00
**功能**: 
- 获取市场数据
- 计算技术指标
- 评估品种质量
- 生成筛选结果

**配置参数**:
- 趋势强度阈值
- 波动率范围
- 流动性要求
- 成本控制

### 2. 资金分配模块

**执行时间**: 每日10:15
**功能**:
- 基于筛选结果分配资金
- 计算仓位大小
- 风险评估
- 生成分配方案

**分配策略**:
- 保守型: 低风险低收益
- 稳健型: 平衡风险收益
- 激进型: 高风险高收益

### 3. 仓位调整模块

**执行时间**: 每日11:30
**功能**:
- 检查当前仓位
- 评估风险水平
- 执行仓位调整
- 风险控制

### 4. 合约切换模块

**执行时间**: 每日15:25
**功能**:
- 检测主力合约变化
- 评估切换条件
- 执行合约切换
- 更新策略配置

### 5. 夜盘准备模块

**执行时间**: 每日20:00
**功能**:
- 系统状态检查
- 账户连接验证
- 数据同步
- 准备夜盘交易

## 日常操作

### 启动系统

```bash
# 正常启动（推荐）
python start_system.py

# 后台运行
nohup python start_system.py > system.log 2>&1 &
```

### 查看系统状态

```bash
# 查看系统状态
python start_system.py --mode status

# 查看日志
tail -f logs/system.log
tail -f logs/trading.log
```

### 运行测试

```bash
# 运行所有测试
python run_tests.py

# 快速测试
python run_tests.py --mode quick

# 性能测试
python run_tests.py --mode performance
```

### 配置修改

1. 停止系统
2. 修改配置文件
3. 验证配置: `python start_system.py --mode config`
4. 重启系统

## 故障排除

### 常见问题及解决方案

#### 1. 系统无法启动

**症状**: 启动时报错或卡住
**可能原因**:
- 配置文件错误
- 数据库连接失败
- 依赖包缺失

**解决方案**:
```bash
# 检查配置
python start_system.py --mode config

# 测试模式启动
python start_system.py --mode test

# 检查依赖
pip list | grep vnpy
```

#### 2. CTP连接失败

**症状**: 无法连接到CTP接口
**可能原因**:
- 网络连接问题
- 账户信息错误
- 交易时间限制

**解决方案**:
- 检查网络连接
- 验证账户信息
- 确认交易时间

#### 3. 策略无法启动

**症状**: 策略初始化失败
**可能原因**:
- 策略文件缺失
- 参数配置错误
- 合约信息错误

**解决方案**:
- 检查策略文件
- 验证参数配置
- 更新合约信息

#### 4. 数据获取失败

**症状**: 无法获取市场数据
**可能原因**:
- 数据库连接问题
- 数据源异常
- 权限不足

**解决方案**:
- 检查数据库连接
- 验证数据源状态
- 确认数据权限

### 日志分析

系统日志位于 `logs/` 目录下：

- `system.log`: 系统运行日志
- `trading.log`: 交易相关日志
- `error.log`: 错误日志
- `performance.log`: 性能监控日志

### 性能监控

系统提供实时性能监控：

```bash
# 查看系统指标
python -c "
from main_controller import IntelligentTradingSystem
system = IntelligentTradingSystem()
system._initialize_scheduler_components()
system.system_monitor.start_monitoring()
import time; time.sleep(2)
metrics = system.system_monitor.get_current_system_metrics()
print(f'CPU: {metrics.cpu_percent:.1f}%')
print(f'Memory: {metrics.memory_percent:.1f}%')
print(f'Disk: {metrics.disk_percent:.1f}%')
"
```

## 常见问题

### Q1: 如何修改交易时间？

A1: 修改 `main_controller.py` 中的时间常量：
```python
DAY_START = time(8, 57)
DAY_END = time(15, 16)
NIGHT_START = time(20, 57)
NIGHT_END = time(2, 35)
```

### Q2: 如何添加新的筛选指标？

A2: 在 `modules/screening/evaluation_engine.py` 中添加新的评估方法，并在配置文件中添加相应参数。

### Q3: 如何调整风险控制参数？

A3: 修改 `config/risk_config.py` 中的风险控制参数，重启系统生效。

### Q4: 如何查看策略运行状态？

A4: 查看交易日志或使用vnpy的图形界面监控策略状态。

### Q5: 系统支持哪些期货品种？

A5: 系统支持所有在vnpy数据库中的期货品种，可通过黑名单配置排除特定品种。

## 技术支持

如遇到技术问题，请：

1. 查看系统日志
2. 运行诊断测试
3. 检查配置文件
4. 参考故障排除指南

更多技术细节请参考：
- [开发者文档](DEVELOPER_GUIDE.md)
- [API文档](API_REFERENCE.md)
- [配置参考](CONFIG_REFERENCE.md)

## 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                    智能量化交易系统                              │
├─────────────────────────────────────────────────────────────────┤
│  主控程序 (main_controller.py)                                  │
│  ├─ 进程管理 (基于zdrun.py)                                     │
│  ├─ 交易时间检测                                                │
│  └─ 系统生命周期管理                                            │
├─────────────────────────────────────────────────────────────────┤
│  核心模块                                                       │
│  ├─ 账户管理 (AccountManager)                                   │
│  ├─ 品种筛选 (FuturesScanner + EvaluationEngine)               │
│  ├─ 资金分配 (CapitalAllocator + PositionManager)              │
│  ├─ 合约切换 (MainContractDetector + ContractSwitcher)         │
│  ├─ 策略管理 (StrategyManager + ParameterOptimizer)            │
│  ├─ 任务调度 (TaskScheduler + EventManager)                    │
│  └─ 系统监控 (SystemMonitor + AlertManager)                    │
├─────────────────────────────────────────────────────────────────┤
│  配置系统                                                       │
│  ├─ 系统配置 (SystemConfig)                                     │
│  ├─ 交易配置 (TradingConfig)                                    │
│  └─ 风险配置 (RiskConfig)                                       │
├─────────────────────────────────────────────────────────────────┤
│  vnpy框架                                                       │
│  ├─ 主引擎 (MainEngine)                                         │
│  ├─ 事件引擎 (EventEngine)                                      │
│  ├─ CTA引擎 (CtaEngine)                                         │
│  └─ 数据库引擎 (DatabaseManager)                                │
└─────────────────────────────────────────────────────────────────┘
```

### 数据流图

```
市场数据 → 数据库 → 筛选引擎 → 评估结果 → 资金分配 → 策略执行
    ↓         ↓         ↓         ↓         ↓         ↓
  实时行情   历史数据   技术指标   品种评分   仓位计算   交易信号
    ↓         ↓         ↓         ↓         ↓         ↓
  风险监控 ← 系统监控 ← 事件管理 ← 任务调度 ← 策略管理 ← 合约切换
```

### 时间调度表

| 时间  | 任务                | 模块                    | 说明                |
|-------|--------------------|-----------------------|-------------------|
| 08:00 | 晨间品种筛选        | FuturesScanner        | 分析昨日数据，筛选品种 |
| 10:15 | 资金分配           | CapitalAllocator      | 基于筛选结果分配资金  |
| 11:30 | 仓位调整           | PositionManager       | 检查并调整当前仓位   |
| 15:25 | 主力合约切换        | ContractSwitcher      | 检测并切换主力合约   |
| 20:00 | 夜盘准备           | SystemMonitor         | 系统检查，准备夜盘   |

### 模块依赖关系

```
main_controller.py
├── core/
│   └── account_manager.py
├── modules/
│   ├── screening/
│   │   ├── futures_scanner.py
│   │   └── evaluation_engine.py
│   ├── allocation/
│   │   ├── capital_allocator.py
│   │   └── position_manager.py
│   ├── switching/
│   │   ├── main_contract_detector.py
│   │   └── contract_switcher.py
│   ├── strategy/
│   │   ├── strategy_manager.py
│   │   └── parameter_optimizer.py
│   ├── scheduler/
│   │   ├── task_scheduler.py
│   │   └── event_manager.py
│   └── monitoring/
│       ├── system_monitor.py
│       └── alert_manager.py
├── config/
│   ├── system_config.py
│   ├── trading_config.py
│   └── risk_config.py
└── utils/
    ├── logger.py
    └── helpers.py
```
