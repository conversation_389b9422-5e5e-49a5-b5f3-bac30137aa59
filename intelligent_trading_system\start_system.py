"""
智能交易系统启动脚本
提供多种启动模式：正常运行、测试模式、配置模式等
"""

import os
import sys
import argparse
from datetime import datetime

# 添加路径
sys.path.append(os.path.dirname(__file__))
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from main_controller import run_parent, run_child, IntelligentTradingSystem
from test_main import main as test_main
from utils.logger import get_logger


def show_banner():
    """显示系统横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════════════════════╗
    ║                          智能量化交易系统 v1.0                                ║
    ║                     Intelligent Quantitative Trading System                  ║
    ║                                                                              ║
    ║  基于vnpy框架的全自动化期货交易系统                                            ║
    ║  功能包括：品种筛选、资金分配、策略管理、风险控制、主力合约切换等                ║
    ║                                                                              ║
    ║  开发时间: 2024年                                                            ║
    ║  技术栈: Python + vnpy + TA-Lib                                             ║
    ╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)


def run_normal_mode():
    """正常运行模式"""
    print("启动智能交易系统 - 正常运行模式")
    print("系统将在交易时间自动启动和停止")
    print("按 Ctrl+C 退出系统")
    print("-" * 80)
    
    try:
        run_parent()
    except KeyboardInterrupt:
        print("\n收到退出信号，正在关闭系统...")
    except Exception as e:
        print(f"系统运行异常: {e}")


def run_test_mode():
    """测试模式"""
    print("启动智能交易系统 - 测试模式")
    print("将执行系统集成测试，验证各模块功能")
    print("-" * 80)
    
    try:
        test_main()
    except Exception as e:
        print(f"测试模式运行异常: {e}")


def run_single_process_mode():
    """单进程模式（用于调试）"""
    print("启动智能交易系统 - 单进程模式")
    print("系统将在当前进程中运行，便于调试")
    print("按 Ctrl+C 退出系统")
    print("-" * 80)
    
    try:
        run_child()
    except KeyboardInterrupt:
        print("\n收到退出信号，正在关闭系统...")
    except Exception as e:
        print(f"单进程模式运行异常: {e}")


def run_config_mode():
    """配置模式"""
    print("启动智能交易系统 - 配置模式")
    print("显示当前系统配置信息")
    print("-" * 80)
    
    try:
        from config import get_system_config, get_trading_config, get_risk_config
        
        # 显示系统配置
        system_config = get_system_config()
        print("系统配置:")
        print(f"  数据库路径: {system_config.database.path}")
        print(f"  日志级别: {system_config.logging.level}")
        print(f"  日志文件: {system_config.logging.file_enabled}")
        
        # 显示交易配置
        trading_config = get_trading_config()
        print(f"\n交易配置:")
        print(f"  默认策略: {trading_config.default_strategy}")
        print(f"  黑名单品种: {trading_config.blacklist_symbols}")
        print(f"  筛选参数: 趋势阈值={trading_config.screening.trend_threshold}")
        
        # 显示风险配置
        risk_config = get_risk_config()
        print(f"\n风险配置:")
        print(f"  最大总仓位: {risk_config.max_total_position}")
        print(f"  单品种最大仓位: {risk_config.max_single_position}")
        print(f"  最大回撤限制: {risk_config.max_drawdown_limit}")
        
    except Exception as e:
        print(f"配置模式运行异常: {e}")


def run_status_mode():
    """状态查看模式"""
    print("智能交易系统 - 状态查看模式")
    print("显示系统运行状态和统计信息")
    print("-" * 80)
    
    try:
        # 创建系统实例（不启动）
        trading_system = IntelligentTradingSystem()
        
        # 初始化基本组件
        trading_system._initialize_trading_components()
        trading_system._initialize_scheduler_components()
        
        # 显示系统状态
        trading_system.display_system_status()
        
        # 显示文件状态
        print(f"\n文件状态检查:")
        config_files = [
            "config/system_config.py",
            "config/trading_config.py", 
            "config/risk_config.py"
        ]
        
        for config_file in config_files:
            file_path = os.path.join(os.path.dirname(__file__), config_file)
            exists = os.path.exists(file_path)
            print(f"  {config_file}: {'存在' if exists else '缺失'}")
        
        # 显示日志文件
        log_dir = "logs"
        log_path = os.path.join(os.path.dirname(__file__), log_dir)
        if os.path.exists(log_path):
            log_files = [f for f in os.listdir(log_path) if f.endswith('.log')]
            print(f"\n日志文件 ({len(log_files)} 个):")
            for log_file in log_files[-5:]:  # 显示最近5个
                print(f"  {log_file}")
        else:
            print(f"\n日志目录不存在: {log_path}")
    
    except Exception as e:
        print(f"状态查看模式运行异常: {e}")


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(
        description="智能量化交易系统启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
运行模式说明:
  normal    - 正常运行模式（默认），多进程守护运行
  test      - 测试模式，执行系统集成测试
  single    - 单进程模式，用于调试
  config    - 配置模式，显示系统配置
  status    - 状态模式，显示系统状态

示例:
  python start_system.py                # 正常运行
  python start_system.py --mode test    # 测试模式
  python start_system.py --mode single  # 单进程调试
  python start_system.py --mode config  # 查看配置
  python start_system.py --mode status  # 查看状态
        """
    )
    
    parser.add_argument(
        '--mode', 
        choices=['normal', 'test', 'single', 'config', 'status'],
        default='normal',
        help='运行模式 (默认: normal)'
    )
    
    parser.add_argument(
        '--no-banner',
        action='store_true',
        help='不显示启动横幅'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别 (默认: INFO)'
    )
    
    args = parser.parse_args()
    
    # 显示横幅
    if not args.no_banner:
        show_banner()
    
    # 显示启动信息
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"运行模式: {args.mode}")
    print(f"日志级别: {args.log_level}")
    print(f"工作目录: {os.getcwd()}")
    print()
    
    # 根据模式运行
    try:
        if args.mode == 'normal':
            run_normal_mode()
        elif args.mode == 'test':
            run_test_mode()
        elif args.mode == 'single':
            run_single_process_mode()
        elif args.mode == 'config':
            run_config_mode()
        elif args.mode == 'status':
            run_status_mode()
        else:
            print(f"未知的运行模式: {args.mode}")
            return 1
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        return 0
    except Exception as e:
        print(f"程序运行异常: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
