"""
系统集成测试
测试各模块之间的集成和协作功能
"""

import unittest
import os
import sys
import time
from unittest.mock import Mock, patch, MagicMock

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from main_controller import IntelligentTradingSystem
from modules.scheduler.event_manager import EventType


class TestSystemIntegration(unittest.TestCase):
    """系统集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.trading_system = None
    
    def tearDown(self):
        """测试后清理"""
        if self.trading_system:
            try:
                self.trading_system.stop_system()
            except:
                pass
    
    def test_system_initialization(self):
        """测试系统初始化集成"""
        try:
            self.trading_system = IntelligentTradingSystem()
            
            # 测试交易组件初始化
            success = self.trading_system._initialize_trading_components()
            self.assertTrue(success, "交易组件初始化失败")
            
            # 测试调度组件初始化
            success = self.trading_system._initialize_scheduler_components()
            self.assertTrue(success, "调度组件初始化失败")
            
            # 测试定时任务设置
            success = self.trading_system._setup_scheduled_tasks()
            self.assertTrue(success, "定时任务设置失败")
            
            # 测试事件处理器设置
            success = self.trading_system._setup_event_handlers()
            self.assertTrue(success, "事件处理器设置失败")
            
            print("✓ 系统初始化集成测试通过")
        
        except Exception as e:
            self.fail(f"系统初始化集成测试失败: {e}")
    
    def test_component_integration(self):
        """测试组件集成"""
        self.trading_system = IntelligentTradingSystem()
        
        # 初始化组件
        self.trading_system._initialize_trading_components()
        self.trading_system._initialize_scheduler_components()
        
        # 验证组件存在
        self.assertIsNotNone(self.trading_system.futures_scanner)
        self.assertIsNotNone(self.trading_system.capital_allocator)
        self.assertIsNotNone(self.trading_system.position_manager)
        self.assertIsNotNone(self.trading_system.contract_detector)
        self.assertIsNotNone(self.trading_system.contract_switcher)
        self.assertIsNotNone(self.trading_system.strategy_manager)
        self.assertIsNotNone(self.trading_system.task_scheduler)
        self.assertIsNotNone(self.trading_system.event_manager)
        self.assertIsNotNone(self.trading_system.system_monitor)
        self.assertIsNotNone(self.trading_system.alert_manager)
        
        print("✓ 组件集成测试通过")
    
    def test_event_system_integration(self):
        """测试事件系统集成"""
        self.trading_system = IntelligentTradingSystem()
        self.trading_system._initialize_scheduler_components()
        
        # 启动事件管理器
        self.trading_system.event_manager.start()
        
        # 测试事件发射和处理
        test_event_received = []
        
        def test_handler(event):
            test_event_received.append(event)
        
        # 注册测试事件处理器
        self.trading_system.event_manager.register_handler(EventType.SYSTEM_START, test_handler)
        
        # 发射测试事件
        self.trading_system.event_manager.emit_event(EventType.SYSTEM_START, "测试数据", "TestSource")
        
        # 等待事件处理
        time.sleep(0.1)
        
        # 验证事件被处理
        self.assertEqual(len(test_event_received), 1)
        self.assertEqual(test_event_received[0].event_type, EventType.SYSTEM_START)
        
        # 停止事件管理器
        self.trading_system.event_manager.stop()
        
        print("✓ 事件系统集成测试通过")
    
    def test_scheduler_integration(self):
        """测试调度器集成"""
        self.trading_system = IntelligentTradingSystem()
        self.trading_system._initialize_trading_components()
        self.trading_system._initialize_scheduler_components()
        self.trading_system._setup_scheduled_tasks()
        
        # 验证任务已添加
        tasks = self.trading_system.task_scheduler.get_all_tasks()
        self.assertGreater(len(tasks), 0, "没有添加定时任务")
        
        # 验证特定任务存在
        task_names = [task.name for task in tasks]
        expected_tasks = ["morning_screening", "capital_allocation", "position_adjustment", 
                         "contract_switching", "night_preparation"]
        
        for expected_task in expected_tasks:
            self.assertIn(expected_task, task_names, f"缺少任务: {expected_task}")
        
        print("✓ 调度器集成测试通过")
    
    def test_monitoring_integration(self):
        """测试监控系统集成"""
        self.trading_system = IntelligentTradingSystem()
        self.trading_system._initialize_scheduler_components()
        
        # 启动监控
        self.trading_system.system_monitor.start_monitoring()
        
        # 等待监控数据收集
        time.sleep(1)
        
        # 获取系统指标
        metrics = self.trading_system.system_monitor.get_current_system_metrics()
        self.assertIsNotNone(metrics, "无法获取系统指标")
        
        # 验证指标数据
        self.assertGreaterEqual(metrics.cpu_percent, 0)
        self.assertGreaterEqual(metrics.memory_percent, 0)
        self.assertGreaterEqual(metrics.disk_percent, 0)
        
        # 停止监控
        self.trading_system.system_monitor.stop_monitoring()
        
        print("✓ 监控系统集成测试通过")
    
    @patch('vnpy.trader.engine.MainEngine')
    @patch('vnpy.event.EventEngine')
    def test_vnpy_integration(self, mock_event_engine, mock_main_engine):
        """测试vnpy集成（使用Mock）"""
        # 设置Mock对象
        mock_event_engine_instance = Mock()
        mock_main_engine_instance = Mock()
        mock_event_engine.return_value = mock_event_engine_instance
        mock_main_engine.return_value = mock_main_engine_instance
        
        # 设置CTA引擎Mock
        mock_cta_engine = Mock()
        mock_main_engine_instance.add_app.return_value = mock_cta_engine
        
        self.trading_system = IntelligentTradingSystem()
        
        # 测试vnpy组件初始化
        success = self.trading_system._initialize_vnpy_components()
        self.assertTrue(success, "vnpy组件初始化失败")
        
        # 验证Mock调用
        mock_event_engine.assert_called_once()
        mock_main_engine.assert_called_once_with(mock_event_engine_instance)
        
        print("✓ vnpy集成测试通过")
    
    def test_task_execution_integration(self):
        """测试任务执行集成"""
        self.trading_system = IntelligentTradingSystem()
        self.trading_system._initialize_trading_components()
        self.trading_system._initialize_scheduler_components()
        
        # 启动事件管理器
        self.trading_system.event_manager.start()
        
        # 测试任务执行
        task_results = []
        
        def capture_task_result(event):
            task_results.append(event)
        
        # 注册任务完成事件处理器
        self.trading_system.event_manager.register_handler(EventType.TASK_COMPLETE, capture_task_result)
        self.trading_system.event_manager.register_handler(EventType.TASK_FAILED, capture_task_result)
        
        # 执行测试任务
        success = self.trading_system._run_morning_screening()
        
        # 等待事件处理
        time.sleep(0.1)
        
        # 验证任务执行结果
        self.assertGreaterEqual(len(task_results), 0, "没有收到任务执行结果事件")
        
        # 停止事件管理器
        self.trading_system.event_manager.stop()
        
        print("✓ 任务执行集成测试通过")
    
    def test_error_handling_integration(self):
        """测试错误处理集成"""
        self.trading_system = IntelligentTradingSystem()
        self.trading_system._initialize_scheduler_components()
        
        # 启动事件管理器
        self.trading_system.event_manager.start()
        
        # 测试错误事件处理
        error_events = []
        
        def capture_error_event(event):
            error_events.append(event)
        
        # 注册错误事件处理器
        self.trading_system.event_manager.register_handler(EventType.SYSTEM_ERROR, capture_error_event)
        
        # 发射错误事件
        self.trading_system.event_manager.emit_event(EventType.SYSTEM_ERROR, "测试错误", "TestSource")
        
        # 等待事件处理
        time.sleep(0.1)
        
        # 验证错误事件被处理
        self.assertEqual(len(error_events), 1)
        
        # 停止事件管理器
        self.trading_system.event_manager.stop()
        
        print("✓ 错误处理集成测试通过")
    
    def test_configuration_integration(self):
        """测试配置集成"""
        self.trading_system = IntelligentTradingSystem()
        
        # 验证配置加载
        self.assertIsNotNone(self.trading_system.system_config)
        self.assertIsNotNone(self.trading_system.trading_config)
        self.assertIsNotNone(self.trading_system.risk_config)
        
        # 验证配置在组件中的使用
        self.trading_system._initialize_trading_components()
        
        # 检查配置是否正确传递给组件
        # 这里可以添加更具体的配置验证逻辑
        
        print("✓ 配置集成测试通过")
    
    def test_system_lifecycle_integration(self):
        """测试系统生命周期集成"""
        self.trading_system = IntelligentTradingSystem()
        
        # 测试初始化
        self.trading_system._initialize_trading_components()
        self.trading_system._initialize_scheduler_components()
        self.trading_system._setup_scheduled_tasks()
        self.trading_system._setup_event_handlers()
        
        # 验证初始化状态
        self.assertTrue(self.trading_system.initialized)
        
        # 测试部分启动（不包含vnpy组件）
        self.trading_system.event_manager.start()
        self.trading_system.system_monitor.start_monitoring()
        
        # 验证运行状态
        self.assertTrue(self.trading_system.event_manager.running)
        self.assertTrue(self.trading_system.system_monitor.monitoring)
        
        # 测试停止
        self.trading_system.event_manager.stop()
        self.trading_system.system_monitor.stop_monitoring()
        
        # 验证停止状态
        self.assertFalse(self.trading_system.event_manager.running)
        self.assertFalse(self.trading_system.system_monitor.monitoring)
        
        print("✓ 系统生命周期集成测试通过")


if __name__ == "__main__":
    unittest.main()
