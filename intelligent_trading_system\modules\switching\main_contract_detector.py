"""
主力合约检测器
基于成交量和持仓量识别主力合约
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
from collections import defaultdict

from vnpy.trader.constant import Exchange, Interval
from vnpy.trader.database import database_manager
from vnpy.trader.object import BarData

from ...config import get_trading_config
from ...utils.logger import get_logger
from ...utils.helpers import extract_product_code, safe_float


class MainContractDetector:
    """主力合约检测器"""
    
    def __init__(self):
        """初始化主力合约检测器"""
        self.logger = get_logger("MainContractDetector")
        self.trading_config = get_trading_config()
        
        # 检测参数
        self.max_days_back = 30  # 最大回溯天数
        self.switch_threshold = 1.1  # 切换阈值（新合约持仓量需超过当前主力合约1.1倍）
        self.min_volume_threshold = 1000  # 最小成交量阈值
        self.min_open_interest_threshold = 1000  # 最小持仓量阈值
        
        # 缓存
        self.main_contract_cache: Dict[str, Dict] = {}
        self.cache_expire_time = timedelta(hours=1)  # 缓存1小时
        
        self.logger.info("主力合约检测器初始化完成")
    
    def get_main_contract_by_volume(self, product_code: str, exchange: Exchange) -> Optional[str]:
        """基于成交量获取主力合约"""
        try:
            cache_key = f"{product_code}.{exchange.value}_volume"
            
            # 检查缓存
            if self._is_cache_valid(cache_key):
                return self.main_contract_cache[cache_key]['contract']
            
            for days_back in range(self.max_days_back):
                # 获取查询时间范围
                end = datetime.now() - timedelta(days=days_back)
                start = end - timedelta(days=1)
                
                # 获取所有合约的成交量数据
                contract_volumes = self._get_contract_volumes(product_code, exchange, start, end)
                
                if not contract_volumes:
                    continue
                
                # 检查是否有合约的成交量大于阈值
                valid_contracts = [(contract, volume) for contract, volume in contract_volumes 
                                 if volume > self.min_volume_threshold]
                
                if valid_contracts:
                    # 按成交量排序，返回成交量最大的合约
                    valid_contracts.sort(key=lambda x: x[1], reverse=True)
                    main_contract = valid_contracts[0][0]
                    
                    # 更新缓存
                    self._update_cache(cache_key, {
                        'contract': main_contract,
                        'volume': valid_contracts[0][1],
                        'date': end.strftime('%Y-%m-%d'),
                        'all_contracts': valid_contracts
                    })
                    
                    self.logger.info(f"基于成交量确定 {product_code} 主力合约: {main_contract} "
                                   f"(成交量: {valid_contracts[0][1]:,})")
                    return main_contract
                else:
                    self.logger.debug(f"日期 {end.strftime('%Y-%m-%d')} 所有合约成交量不足，继续回溯")
            
            self.logger.warning(f"回溯 {self.max_days_back} 天未找到 {product_code} 的有效主力合约")
            return None
        
        except Exception as e:
            self.logger.error(f"获取主力合约失败: {e}")
            return None
    
    def get_main_contract_by_open_interest(self, product_code: str, exchange: Exchange) -> Optional[str]:
        """基于持仓量获取主力合约"""
        try:
            cache_key = f"{product_code}.{exchange.value}_oi"
            
            # 检查缓存
            if self._is_cache_valid(cache_key):
                return self.main_contract_cache[cache_key]['contract']
            
            for days_back in range(self.max_days_back):
                # 获取查询时间范围
                end = datetime.now() - timedelta(days=days_back)
                start = end - timedelta(days=1)
                
                # 获取所有合约的持仓量数据
                contract_open_interests = self._get_contract_open_interests(product_code, exchange, start, end)
                
                if not contract_open_interests:
                    continue
                
                # 检查是否有合约的持仓量大于阈值
                valid_contracts = [(contract, oi) for contract, oi in contract_open_interests 
                                 if oi > self.min_open_interest_threshold]
                
                if valid_contracts:
                    # 按持仓量排序，返回持仓量最大的合约
                    valid_contracts.sort(key=lambda x: x[1], reverse=True)
                    main_contract = valid_contracts[0][0]
                    
                    # 更新缓存
                    self._update_cache(cache_key, {
                        'contract': main_contract,
                        'open_interest': valid_contracts[0][1],
                        'date': end.strftime('%Y-%m-%d'),
                        'all_contracts': valid_contracts
                    })
                    
                    self.logger.info(f"基于持仓量确定 {product_code} 主力合约: {main_contract} "
                                   f"(持仓量: {valid_contracts[0][1]:,})")
                    return main_contract
                else:
                    self.logger.debug(f"日期 {end.strftime('%Y-%m-%d')} 所有合约持仓量不足，继续回溯")
            
            self.logger.warning(f"回溯 {self.max_days_back} 天未找到 {product_code} 的有效主力合约")
            return None
        
        except Exception as e:
            self.logger.error(f"获取主力合约失败: {e}")
            return None
    
    def get_main_contract(self, product_code: str, exchange: Exchange, 
                         method: str = "volume") -> Optional[str]:
        """获取主力合约"""
        if method == "volume":
            return self.get_main_contract_by_volume(product_code, exchange)
        elif method == "open_interest":
            return self.get_main_contract_by_open_interest(product_code, exchange)
        else:
            # 综合方法：优先使用持仓量，如果没有则使用成交量
            main_contract = self.get_main_contract_by_open_interest(product_code, exchange)
            if not main_contract:
                main_contract = self.get_main_contract_by_volume(product_code, exchange)
            return main_contract
    
    def _get_contract_volumes(self, product_code: str, exchange: Exchange, 
                            start: datetime, end: datetime) -> List[Tuple[str, float]]:
        """获取所有合约的成交量数据"""
        try:
            # 从数据库中获取所有合约的K线数据
            contracts = database_manager.get_bar_overview()
            contract_volumes: List[Tuple[str, float]] = []
            
            # 遍历所有合约
            for contract in contracts:
                # 获取合约的品种代码
                contract_product_code = extract_product_code(contract.symbol)
                
                # 确保品种代码完全匹配
                if contract_product_code == product_code and contract.exchange == exchange:
                    # 排除888合约
                    if "888" in contract.symbol:
                        continue
                    
                    # 获取该合约的K线数据
                    bars = database_manager.load_bar_data(
                        symbol=contract.symbol,
                        exchange=contract.exchange,
                        interval=Interval.MINUTE,
                        start=start,
                        end=end
                    )
                    
                    if bars:
                        # 累计当天所有K线的成交量
                        total_volume = sum(bar.volume for bar in bars)
                        contract_volumes.append((contract.symbol, total_volume))
            
            return contract_volumes
        
        except Exception as e:
            self.logger.error(f"获取合约成交量数据失败: {e}")
            return []
    
    def _get_contract_open_interests(self, product_code: str, exchange: Exchange, 
                                   start: datetime, end: datetime) -> List[Tuple[str, float]]:
        """获取所有合约的持仓量数据"""
        try:
            # 从数据库中获取所有合约的K线数据
            contracts = database_manager.get_bar_overview()
            contract_open_interests: List[Tuple[str, float]] = []
            
            # 遍历所有合约
            for contract in contracts:
                # 获取合约的品种代码
                contract_product_code = extract_product_code(contract.symbol)
                
                # 确保品种代码完全匹配
                if contract_product_code == product_code and contract.exchange == exchange:
                    # 排除888合约
                    if "888" in contract.symbol:
                        continue
                    
                    # 获取该合约的K线数据
                    bars = database_manager.load_bar_data(
                        symbol=contract.symbol,
                        exchange=contract.exchange,
                        interval=Interval.MINUTE,
                        start=start,
                        end=end
                    )
                    
                    if bars:
                        # 获取最后一个K线的持仓量
                        last_bar = bars[-1]
                        open_interest = getattr(last_bar, 'open_interest', 0)
                        if open_interest > 0:
                            contract_open_interests.append((contract.symbol, open_interest))
            
            return contract_open_interests
        
        except Exception as e:
            self.logger.error(f"获取合约持仓量数据失败: {e}")
            return []
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.main_contract_cache:
            return False
        
        cache_time = self.main_contract_cache[cache_key].get('timestamp')
        if not cache_time:
            return False
        
        return datetime.now() - cache_time < self.cache_expire_time
    
    def _update_cache(self, cache_key: str, data: Dict[str, Any]):
        """更新缓存"""
        data['timestamp'] = datetime.now()
        self.main_contract_cache[cache_key] = data
    
    def check_switch_needed(self, current_contract: str, product_code: str, 
                          exchange: Exchange) -> Tuple[bool, Optional[str], str]:
        """检查是否需要切换主力合约"""
        try:
            # 获取当前主力合约
            new_main_contract = self.get_main_contract(product_code, exchange, "open_interest")
            
            if not new_main_contract:
                return False, None, "未找到新的主力合约"
            
            if new_main_contract == current_contract:
                return False, None, "当前合约仍为主力合约"
            
            # 获取两个合约的持仓量进行比较
            end = datetime.now()
            start = end - timedelta(days=1)
            
            current_oi = self._get_single_contract_open_interest(current_contract, exchange, start, end)
            new_oi = self._get_single_contract_open_interest(new_main_contract, exchange, start, end)
            
            if new_oi > current_oi * self.switch_threshold:
                ratio = new_oi / current_oi if current_oi > 0 else float('inf')
                reason = f"新合约持仓量 {new_oi:,} 超过当前合约 {current_oi:,} 的 {self.switch_threshold} 倍 (实际倍数: {ratio:.2f})"
                return True, new_main_contract, reason
            else:
                return False, None, f"新合约持仓量 {new_oi:,} 未达到切换阈值 (当前: {current_oi:,})"
        
        except Exception as e:
            self.logger.error(f"检查切换需求失败: {e}")
            return False, None, f"检查失败: {str(e)}"
    
    def _get_single_contract_open_interest(self, contract: str, exchange: Exchange, 
                                         start: datetime, end: datetime) -> float:
        """获取单个合约的持仓量"""
        try:
            bars = database_manager.load_bar_data(
                symbol=contract,
                exchange=exchange,
                interval=Interval.MINUTE,
                start=start,
                end=end
            )
            
            if bars:
                last_bar = bars[-1]
                return getattr(last_bar, 'open_interest', 0)
            else:
                return 0
        
        except Exception as e:
            self.logger.error(f"获取合约 {contract} 持仓量失败: {e}")
            return 0
    
    def get_all_main_contracts(self, product_codes: List[str]) -> Dict[str, str]:
        """批量获取多个品种的主力合约"""
        results = {}
        
        for product_code in product_codes:
            try:
                # 根据品种代码确定交易所
                exchange = self._get_exchange_by_product(product_code)
                if exchange:
                    main_contract = self.get_main_contract(product_code, exchange)
                    if main_contract:
                        results[product_code] = main_contract
                        self.logger.info(f"品种 {product_code} 主力合约: {main_contract}")
                    else:
                        self.logger.warning(f"未找到品种 {product_code} 的主力合约")
                else:
                    self.logger.warning(f"未知品种 {product_code} 的交易所")
            
            except Exception as e:
                self.logger.error(f"获取品种 {product_code} 主力合约失败: {e}")
        
        return results
    
    def _get_exchange_by_product(self, product_code: str) -> Optional[Exchange]:
        """根据品种代码获取交易所"""
        # 交易所映射
        exchange_mapping = {
            # 上期所
            'RB': Exchange.SHFE, 'HC': Exchange.SHFE, 'I': Exchange.SHFE,
            'CU': Exchange.SHFE, 'AL': Exchange.SHFE, 'ZN': Exchange.SHFE,
            'AU': Exchange.SHFE, 'AG': Exchange.SHFE, 'RU': Exchange.SHFE,
            # 大商所
            'J': Exchange.DCE, 'JM': Exchange.DCE, 'C': Exchange.DCE,
            'CS': Exchange.DCE, 'A': Exchange.DCE, 'M': Exchange.DCE,
            # 郑商所
            'CF': Exchange.CZCE, 'SR': Exchange.CZCE, 'TA': Exchange.CZCE,
            'MA': Exchange.CZCE, 'RM': Exchange.CZCE, 'Y': Exchange.CZCE,
            # 中金所
            'IF': Exchange.CFFEX, 'IH': Exchange.CFFEX, 'IC': Exchange.CFFEX,
            # 广期所
            'SI': Exchange.GFEX, 'LC': Exchange.GFEX
        }
        
        return exchange_mapping.get(product_code.upper())


if __name__ == "__main__":
    # 测试主力合约检测器
    detector = MainContractDetector()
    
    # 测试单个品种
    main_contract = detector.get_main_contract("RB", Exchange.SHFE)
    print(f"RB主力合约: {main_contract}")
    
    # 测试切换检查
    if main_contract:
        need_switch, new_contract, reason = detector.check_switch_needed(
            "RB2410.SHFE", "RB", Exchange.SHFE
        )
        print(f"是否需要切换: {need_switch}, 新合约: {new_contract}, 原因: {reason}")
    
    # 测试批量获取
    product_codes = ["RB", "CU", "J", "CF"]
    main_contracts = detector.get_all_main_contracts(product_codes)
    print(f"批量主力合约: {main_contracts}")
    
    print("主力合约检测器测试完成")
