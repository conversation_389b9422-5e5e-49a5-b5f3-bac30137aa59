#define ONFRONTCONNECTED 0
#define ONFRONTDISCONNECTED 1
#define ONHEARTBEATWARNING 2
#define ONRSPAUTHENTICATE 3
#define ONRSPUSERLOGIN 4
#define ONRSPUSERLOGOUT 5
#define ONRSPUSERPASSWORDUPDATE 6
#define ONRSPTRADINGACCOUNTPASSWORDUPDATE 7
#define ONRSPORDERINSERT 8
#define ONRS<PERSON>AR<PERSON><PERSON><PERSON><PERSON>RINSERT 9
#define ONRSPPARKEDORDERACTION 10
#define ONRSPORDERACTION 11
#define ONRSPQUERYMAXORDERVOLUME 12
#define ONRSPSETTLEMENTINFOCONFIRM 13
#define ONRSPREMOVEPARKEDORDER 14
#define ONRSPREMOVEPARKEDORDERACTION 15
#define ONRSPEXECORDERINSERT 16
#define ONRSPEXECORDERACTION 17
#define ONRSPFORQUOTEINSERT 18
#define ONRSPQUOTEINSERT 19
#define ONRSPQUOTEACTION 20
#define ONRSPBATCHORDERACTION 21
#define ONRSPOPTIONSELFCLOSEINSERT 22
#define ONRSPOPTIONSELFCLOSEACTION 23
#define ONRSPCOMBACTIONINSERT 24
#define ONRSPQRYOR<PERSON><PERSON> 25
#define ONRSPQRYTRADE 26
#define ONRSPQRYINVESTORPOSITION 27
#define ONRSPQRYTRADINGACCOUNT 28
#define ONRSPQRYINVESTOR 29
#define ONRSPQRYTRADINGCODE 30
#define ONRSPQRYINSTRUMENTMARGINRATE 31
#define ONRSPQRYINSTRUMENTCOMMISSIONRATE 32
#define ONRSPQRYEXCHANGE 33
#define ONRSPQRYPRODUCT 34
#define ONRSPQRYINSTRUMENT 35
#define ONRSPQRYDEPTHMARKETDATA 36
#define ONRSPQRYSETTLEMENTINFO 37
#define ONRSPQRYTRANSFERBANK 38
#define ONRSPQRYINVESTORPOSITIONDETAIL 39
#define ONRSPQRYNOTICE 40
#define ONRSPQRYSETTLEMENTINFOCONFIRM 41
#define ONRSPQRYINVESTORPOSITIONCOMBINEDETAIL 42
#define ONRSPQRYCFMMCTRADINGACCOUNTKEY 43
#define ONRSPQRYEWARRANTOFFSET 44
#define ONRSPQRYINVESTORPRODUCTGROUPMARGIN 45
#define ONRSPQRYEXCHANGEMARGINRATE 46
#define ONRSPQRYEXCHANGEMARGINRATEADJUST 47
#define ONRSPQRYEXCHANGERATE 48
#define ONRSPQRYSECAGENTACIDMAP 49
#define ONRSPQRYPRODUCTEXCHRATE 50
#define ONRSPQRYPRODUCTGROUP 51
#define ONRSPQRYMMINSTRUMENTCOMMISSIONRATE 52
#define ONRSPQRYMMOPTIONINSTRCOMMRATE 53
#define ONRSPQRYINSTRUMENTORDERCOMMRATE 54
#define ONRSPQRYSECAGENTTRADINGACCOUNT 55
#define ONRSPQRYSECAGENTCHECKMODE 56
#define ONRSPQRYOPTIONINSTRTRADECOST 57
#define ONRSPQRYOPTIONINSTRCOMMRATE 58
#define ONRSPQRYEXECORDER 59
#define ONRSPQRYFORQUOTE 60
#define ONRSPQRYQUOTE 61
#define ONRSPQRYOPTIONSELFCLOSE 62
#define ONRSPQRYINVESTUNIT 63
#define ONRSPQRYCOMBINSTRUMENTGUARD 64
#define ONRSPQRYCOMBACTION 65
#define ONRSPQRYTRANSFERSERIAL 66
#define ONRSPQRYACCOUNTREGISTER 67
#define ONRSPFORQUOTE 68
#define ONRSPERROR 69
#define ONRTNORDER 70
#define ONRTNTRADE 71
#define ONERRRTNORDERINSERT 72
#define ONERRRTNORDERACTION 73
#define ONRTNINSTRUMENTSTATUS 74
#define ONRTNBULLETIN 75
#define ONRTNTRADINGNOTICE 76
#define ONRTNERRORCONDITIONALORDER 77
#define ONRTNEXECORDER 78
#define ONERRRTNEXECORDERINSERT 79
#define ONERRRTNEXECORDERACTION 80
#define ONERRRTNFORQUOTEINSERT 81
#define ONRTNQUOTE 82
#define ONERRRTNQUOTEINSERT 83
#define ONERRRTNQUOTEACTION 84
#define ONRTNFORQUOTE 85
#define ONRTNCFMMCTRADINGACCOUNTTOKEN 86
#define ONERRRTNBATCHORDERACTION 87
#define ONRTNOPTIONSELFCLOSE 88
#define ONERRRTNOPTIONSELFCLOSEINSERT 89
#define ONERRRTNOPTIONSELFCLOSEACTION 90
#define ONRTNCOMBACTION 91
#define ONERRRTNCOMBACTIONINSERT 92
#define ONRSPQRYCONTRACTBANK 93
#define ONRSPQRYPARKEDORDER 94
#define ONRSPQRYPARKEDORDERACTION 95
#define ONRSPQRYTRADINGNOTICE 96
#define ONRSPQRYBROKERTRADINGPARAMS 97
#define ONRSPQRYBROKERTRADINGALGOS 98
#define ONRSPQUERYCFMMCTRADINGACCOUNTTOKEN 99
#define ONRTNFROMBANKTOFUTUREBYBANK 100
#define ONRTNFROMFUTURETOBANKBYBANK 101
#define ONRTNREPEALFROMBANKTOFUTUREBYBANK 102
#define ONRTNREPEALFROMFUTURETOBANKBYBANK 103
#define ONRTNFROMBANKTOFUTUREBYFUTURE 104
#define ONRTNFROMFUTURETOBANKBYFUTURE 105
#define ONRTNREPEALFROMBANKTOFUTUREBYFUTUREMANUAL 106
#define ONRTNREPEALFROMFUTURETOBANKBYFUTUREMANUAL 107
#define ONRTNQUERYBANKBALANCEBYFUTURE 108
#define ONERRRTNBANKTOFUTUREBYFUTURE 109
#define ONERRRTNFUTURETOBANKBYFUTURE 110
#define ONERRRTNREPEALBANKTOFUTUREBYFUTUREMANUAL 111
#define ONERRRTNREPEALFUTURETOBANKBYFUTUREMANUAL 112
#define ONERRRTNQUERYBANKBALANCEBYFUTURE 113
#define ONRTNREPEALFROMBANKTOFUTUREBYFUTURE 114
#define ONRTNREPEALFROMFUTURETOBANKBYFUTURE 115
#define ONRSPFROMBANKTOFUTUREBYFUTURE 116
#define ONRSPFROMFUTURETOBANKBYFUTURE 117
#define ONRSPQUERYBANKACCOUNTMONEYBYFUTURE 118
#define ONRTNOPENACCOUNTBYBANK 119
#define ONRTNCANCELACCOUNTBYBANK 120
#define ONRTNCHANGEACCOUNTBYBANK 121
