"""
参数优化器
提供策略参数优化、遗传算法优化、参数管理等功能
"""

import json
import os
import random
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import numpy as np

try:
    from vnpy.app.cta_backtester import BacktestingEngine, OptimizationSetting
    from vnpy.app.cta_strategy.base import CtaTemplate
except ImportError:
    BacktestingEngine = None
    OptimizationSetting = None
    CtaTemplate = None

from vnpy.trader.constant import Interval
from vnpy.trader.database import database_manager

from ...config import get_trading_config
from ...utils.logger import get_logger
from ...utils.helpers import safe_float, format_number


@dataclass
class OptimizationResult:
    """优化结果"""
    parameters: Dict[str, Any]
    total_return: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    win_rate: float = 0.0
    total_trades: int = 0
    score: float = 0.0  # 综合评分


@dataclass
class ParameterRange:
    """参数范围"""
    name: str
    min_value: float
    max_value: float
    step: float = 1.0
    param_type: str = "int"  # int, float


class ParameterOptimizer:
    """参数优化器"""
    
    def __init__(self):
        """初始化参数优化器"""
        self.logger = get_logger("ParameterOptimizer")
        self.trading_config = get_trading_config()
        
        # 优化配置
        self.max_workers = 4  # 最大并发数
        self.population_size = 50  # 遗传算法种群大小
        self.generations = 20  # 遗传算法代数
        self.mutation_rate = 0.1  # 变异率
        self.crossover_rate = 0.8  # 交叉率
        
        # 优化结果
        self.optimization_results: List[OptimizationResult] = []
        self.best_parameters: Dict[str, Dict[str, Any]] = {}
        
        # 配置文件
        self.optimization_config_file = "optimization_config.json"
        self.optimization_results_file = "optimization_results.json"
        
        self.logger.info("参数优化器初始化完成")
    
    def add_parameter_range(self, param_ranges: List[ParameterRange]) -> Dict[str, Any]:
        """添加参数范围配置"""
        try:
            config = {}
            for param_range in param_ranges:
                config[param_range.name] = {
                    'min_value': param_range.min_value,
                    'max_value': param_range.max_value,
                    'step': param_range.step,
                    'type': param_range.param_type
                }
            
            # 保存配置
            self._save_optimization_config(config)
            
            self.logger.info(f"添加参数范围配置: {list(config.keys())}")
            return config
        
        except Exception as e:
            self.logger.error(f"添加参数范围失败: {e}")
            return {}
    
    def run_grid_optimization(self, strategy_class_name: str, vt_symbol: str,
                            param_ranges: List[ParameterRange],
                            start_date: datetime, end_date: datetime,
                            initial_capital: float = 1000000) -> List[OptimizationResult]:
        """运行网格优化"""
        try:
            self.logger.info(f"开始网格优化: {strategy_class_name} - {vt_symbol}")
            
            # 生成参数组合
            param_combinations = self._generate_grid_combinations(param_ranges)
            self.logger.info(f"生成 {len(param_combinations)} 个参数组合")
            
            # 并行回测
            results = []
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = []
                
                for params in param_combinations:
                    future = executor.submit(
                        self._run_single_backtest,
                        strategy_class_name, vt_symbol, params,
                        start_date, end_date, initial_capital
                    )
                    futures.append(future)
                
                # 收集结果
                for future in as_completed(futures):
                    try:
                        result = future.result()
                        if result:
                            results.append(result)
                    except Exception as e:
                        self.logger.error(f"回测任务失败: {e}")
            
            # 按综合评分排序
            results.sort(key=lambda x: x.score, reverse=True)
            
            self.optimization_results = results
            self._save_optimization_results(results)
            
            self.logger.info(f"网格优化完成，共 {len(results)} 个有效结果")
            return results
        
        except Exception as e:
            self.logger.error(f"网格优化失败: {e}")
            return []
    
    def run_genetic_optimization(self, strategy_class_name: str, vt_symbol: str,
                               param_ranges: List[ParameterRange],
                               start_date: datetime, end_date: datetime,
                               initial_capital: float = 1000000) -> List[OptimizationResult]:
        """运行遗传算法优化"""
        try:
            self.logger.info(f"开始遗传算法优化: {strategy_class_name} - {vt_symbol}")
            
            # 初始化种群
            population = self._initialize_population(param_ranges, self.population_size)
            
            best_results = []
            
            for generation in range(self.generations):
                self.logger.info(f"第 {generation + 1}/{self.generations} 代")
                
                # 评估种群
                fitness_scores = []
                with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                    futures = []
                    
                    for individual in population:
                        future = executor.submit(
                            self._run_single_backtest,
                            strategy_class_name, vt_symbol, individual,
                            start_date, end_date, initial_capital
                        )
                        futures.append(future)
                    
                    # 收集适应度
                    for future in as_completed(futures):
                        try:
                            result = future.result()
                            if result:
                                fitness_scores.append(result.score)
                            else:
                                fitness_scores.append(0.0)
                        except Exception as e:
                            self.logger.error(f"回测任务失败: {e}")
                            fitness_scores.append(0.0)
                
                # 记录最佳个体
                best_idx = np.argmax(fitness_scores)
                best_individual = population[best_idx]
                best_score = fitness_scores[best_idx]
                
                best_result = OptimizationResult(
                    parameters=best_individual.copy(),
                    score=best_score
                )
                best_results.append(best_result)
                
                self.logger.info(f"第 {generation + 1} 代最佳评分: {best_score:.4f}")
                
                # 选择、交叉、变异
                population = self._evolve_population(
                    population, fitness_scores, param_ranges
                )
            
            # 保存结果
            self.optimization_results = best_results
            self._save_optimization_results(best_results)
            
            self.logger.info(f"遗传算法优化完成，共 {len(best_results)} 代结果")
            return best_results
        
        except Exception as e:
            self.logger.error(f"遗传算法优化失败: {e}")
            return []
    
    def get_best_parameters(self, strategy_name: str, symbol: str) -> Optional[Dict[str, Any]]:
        """获取最佳参数"""
        key = f"{strategy_name}_{symbol}"
        return self.best_parameters.get(key)
    
    def save_best_parameters(self, strategy_name: str, symbol: str, parameters: Dict[str, Any]):
        """保存最佳参数"""
        key = f"{strategy_name}_{symbol}"
        self.best_parameters[key] = {
            'parameters': parameters,
            'timestamp': datetime.now().isoformat(),
            'strategy_name': strategy_name,
            'symbol': symbol
        }
        
        # 保存到文件
        try:
            with open("best_parameters.json", 'w', encoding='utf-8') as f:
                json.dump(self.best_parameters, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存最佳参数失败: {e}")
    
    def _generate_grid_combinations(self, param_ranges: List[ParameterRange]) -> List[Dict[str, Any]]:
        """生成网格参数组合"""
        combinations = []
        
        def generate_values(param_range: ParameterRange):
            values = []
            current = param_range.min_value
            while current <= param_range.max_value:
                if param_range.param_type == "int":
                    values.append(int(current))
                else:
                    values.append(float(current))
                current += param_range.step
            return values
        
        # 生成所有参数的值列表
        param_values = {}
        for param_range in param_ranges:
            param_values[param_range.name] = generate_values(param_range)
        
        # 生成笛卡尔积
        import itertools
        param_names = list(param_values.keys())
        value_lists = [param_values[name] for name in param_names]
        
        for combination in itertools.product(*value_lists):
            param_dict = dict(zip(param_names, combination))
            combinations.append(param_dict)
        
        return combinations
    
    def _initialize_population(self, param_ranges: List[ParameterRange], 
                             population_size: int) -> List[Dict[str, Any]]:
        """初始化遗传算法种群"""
        population = []
        
        for _ in range(population_size):
            individual = {}
            for param_range in param_ranges:
                if param_range.param_type == "int":
                    value = random.randint(
                        int(param_range.min_value), 
                        int(param_range.max_value)
                    )
                else:
                    value = random.uniform(
                        param_range.min_value, 
                        param_range.max_value
                    )
                individual[param_range.name] = value
            
            population.append(individual)
        
        return population
    
    def _evolve_population(self, population: List[Dict[str, Any]], 
                          fitness_scores: List[float],
                          param_ranges: List[ParameterRange]) -> List[Dict[str, Any]]:
        """进化种群"""
        new_population = []
        
        # 保留最佳个体（精英策略）
        elite_count = max(1, int(len(population) * 0.1))
        elite_indices = np.argsort(fitness_scores)[-elite_count:]
        
        for idx in elite_indices:
            new_population.append(population[idx].copy())
        
        # 生成新个体
        while len(new_population) < len(population):
            # 选择父母
            parent1 = self._tournament_selection(population, fitness_scores)
            parent2 = self._tournament_selection(population, fitness_scores)
            
            # 交叉
            if random.random() < self.crossover_rate:
                child1, child2 = self._crossover(parent1, parent2)
            else:
                child1, child2 = parent1.copy(), parent2.copy()
            
            # 变异
            child1 = self._mutate(child1, param_ranges)
            child2 = self._mutate(child2, param_ranges)
            
            new_population.extend([child1, child2])
        
        return new_population[:len(population)]
    
    def _tournament_selection(self, population: List[Dict[str, Any]], 
                            fitness_scores: List[float], tournament_size: int = 3) -> Dict[str, Any]:
        """锦标赛选择"""
        tournament_indices = random.sample(range(len(population)), tournament_size)
        tournament_fitness = [fitness_scores[i] for i in tournament_indices]
        winner_idx = tournament_indices[np.argmax(tournament_fitness)]
        return population[winner_idx].copy()
    
    def _crossover(self, parent1: Dict[str, Any], parent2: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """交叉操作"""
        child1, child2 = parent1.copy(), parent2.copy()
        
        for key in parent1.keys():
            if random.random() < 0.5:
                child1[key], child2[key] = child2[key], child1[key]
        
        return child1, child2
    
    def _mutate(self, individual: Dict[str, Any], param_ranges: List[ParameterRange]) -> Dict[str, Any]:
        """变异操作"""
        mutated = individual.copy()
        
        for param_range in param_ranges:
            if random.random() < self.mutation_rate:
                if param_range.param_type == "int":
                    mutated[param_range.name] = random.randint(
                        int(param_range.min_value), 
                        int(param_range.max_value)
                    )
                else:
                    mutated[param_range.name] = random.uniform(
                        param_range.min_value, 
                        param_range.max_value
                    )
        
        return mutated
    
    def _run_single_backtest(self, strategy_class_name: str, vt_symbol: str,
                           parameters: Dict[str, Any], start_date: datetime,
                           end_date: datetime, initial_capital: float) -> Optional[OptimizationResult]:
        """运行单次回测"""
        try:
            # 这里应该调用实际的回测引擎
            # 由于没有具体的回测引擎实现，这里返回模拟结果
            
            # 模拟回测结果
            total_return = random.uniform(-0.2, 0.5)
            sharpe_ratio = random.uniform(-1.0, 3.0)
            max_drawdown = random.uniform(0.05, 0.3)
            win_rate = random.uniform(0.3, 0.7)
            total_trades = random.randint(50, 500)
            
            # 计算综合评分
            score = self._calculate_score(total_return, sharpe_ratio, max_drawdown, win_rate)
            
            return OptimizationResult(
                parameters=parameters,
                total_return=total_return,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                total_trades=total_trades,
                score=score
            )
        
        except Exception as e:
            self.logger.error(f"单次回测失败: {e}")
            return None
    
    def _calculate_score(self, total_return: float, sharpe_ratio: float,
                        max_drawdown: float, win_rate: float) -> float:
        """计算综合评分"""
        # 权重配置
        return_weight = 0.3
        sharpe_weight = 0.4
        drawdown_weight = 0.2
        winrate_weight = 0.1
        
        # 标准化评分
        return_score = max(0, total_return) * 100
        sharpe_score = max(0, sharpe_ratio) * 20
        drawdown_score = max(0, (0.5 - max_drawdown)) * 200
        winrate_score = win_rate * 100
        
        total_score = (return_score * return_weight + 
                      sharpe_score * sharpe_weight + 
                      drawdown_score * drawdown_weight + 
                      winrate_score * winrate_weight)
        
        return total_score
    
    def _save_optimization_config(self, config: Dict[str, Any]):
        """保存优化配置"""
        try:
            with open(self.optimization_config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存优化配置失败: {e}")
    
    def _save_optimization_results(self, results: List[OptimizationResult]):
        """保存优化结果"""
        try:
            results_data = []
            for result in results:
                results_data.append(asdict(result))
            
            with open(self.optimization_results_file, 'w', encoding='utf-8') as f:
                json.dump(results_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存优化结果失败: {e}")
    
    def display_optimization_results(self, top_n: int = 10):
        """显示优化结果"""
        if not self.optimization_results:
            self.logger.info("暂无优化结果")
            return
        
        self.logger.info("=" * 100)
        self.logger.info(f"参数优化结果 (前 {top_n} 名)")
        self.logger.info("=" * 100)
        
        headers = ["排名", "总收益", "夏普比率", "最大回撤", "胜率", "交易次数", "综合评分"]
        self.logger.info(f"{headers[0]:4} | {headers[1]:8} | {headers[2]:8} | "
                        f"{headers[3]:8} | {headers[4]:6} | {headers[5]:8} | {headers[6]:8}")
        self.logger.info("-" * 100)
        
        for i, result in enumerate(self.optimization_results[:top_n]):
            self.logger.info(f"{i+1:4} | {result.total_return:8.2%} | {result.sharpe_ratio:8.2f} | "
                           f"{result.max_drawdown:8.2%} | {result.win_rate:6.2%} | "
                           f"{result.total_trades:8} | {result.score:8.2f}")


if __name__ == "__main__":
    # 测试参数优化器
    optimizer = ParameterOptimizer()
    
    # 定义参数范围
    param_ranges = [
        ParameterRange("fast_window", 5, 20, 1, "int"),
        ParameterRange("slow_window", 20, 60, 5, "int"),
        ParameterRange("atr_length", 10, 30, 2, "int")
    ]
    
    # 运行网格优化
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 12, 31)
    
    results = optimizer.run_grid_optimization(
        "TestStrategy", "RB2410.SHFE", param_ranges, start_date, end_date
    )
    
    # 显示结果
    optimizer.display_optimization_results()
    
    print("参数优化器测试完成")
