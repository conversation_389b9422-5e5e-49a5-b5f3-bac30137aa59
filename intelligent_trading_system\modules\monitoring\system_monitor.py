"""
系统监控器
提供系统性能监控、资源使用监控、交易状态监控等功能
"""

import psutil
import threading
import time
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import json

from ...config import get_system_config
from ...utils.logger import get_logger
from ..scheduler.event_manager import EventManager, EventType, emit_event


class MonitorLevel(Enum):
    """监控级别"""
    INFO = "信息"
    WARNING = "警告"
    ERROR = "错误"
    CRITICAL = "严重"


@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_gb: float
    memory_total_gb: float
    disk_percent: float
    disk_used_gb: float
    disk_total_gb: float
    network_sent_mb: float
    network_recv_mb: float
    process_count: int
    thread_count: int


@dataclass
class TradingMetrics:
    """交易指标"""
    timestamp: datetime
    active_strategies: int
    total_positions: int
    total_pnl: float
    daily_trades: int
    order_success_rate: float
    connection_status: str
    last_tick_time: Optional[datetime] = None


@dataclass
class MonitorAlert:
    """监控告警"""
    timestamp: datetime
    level: MonitorLevel
    category: str
    message: str
    details: Dict[str, Any] = None
    resolved: bool = False


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, event_manager: Optional[EventManager] = None):
        """初始化系统监控器"""
        self.logger = get_logger("SystemMonitor")
        self.system_config = get_system_config()
        self.event_manager = event_manager
        
        # 监控配置
        self.monitor_interval = 30  # 监控间隔（秒）
        self.metrics_history_limit = 1000  # 历史指标保留数量
        
        # 告警阈值
        self.cpu_warning_threshold = 80.0
        self.cpu_critical_threshold = 95.0
        self.memory_warning_threshold = 80.0
        self.memory_critical_threshold = 95.0
        self.disk_warning_threshold = 85.0
        self.disk_critical_threshold = 95.0
        
        # 监控数据
        self.system_metrics_history: List[SystemMetrics] = []
        self.trading_metrics_history: List[TradingMetrics] = []
        self.alerts: List[MonitorAlert] = []
        
        # 监控状态
        self.running = False
        self.monitor_thread: Optional[threading.Thread] = None
        
        # 网络基准值（用于计算增量）
        self.network_baseline = None
        
        # 回调函数
        self.alert_callbacks: List[Callable[[MonitorAlert], None]] = []
        
        self.logger.info("系统监控器初始化完成")
    
    def start_monitoring(self) -> bool:
        """启动监控"""
        try:
            if self.running:
                self.logger.warning("系统监控已在运行中")
                return True
            
            self.running = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            
            # 发射监控启动事件
            if self.event_manager:
                self.event_manager.emit_event(
                    EventType.SYSTEM_START, 
                    "系统监控启动", 
                    "SystemMonitor"
                )
            
            self.logger.info("系统监控启动成功")
            return True
        
        except Exception as e:
            self.logger.error(f"启动系统监控失败: {e}")
            return False
    
    def stop_monitoring(self) -> bool:
        """停止监控"""
        try:
            if not self.running:
                self.logger.warning("系统监控未在运行")
                return True
            
            self.running = False
            
            # 等待监控线程结束
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=5)
            
            # 发射监控停止事件
            if self.event_manager:
                self.event_manager.emit_event(
                    EventType.SYSTEM_STOP, 
                    "系统监控停止", 
                    "SystemMonitor"
                )
            
            self.logger.info("系统监控停止成功")
            return True
        
        except Exception as e:
            self.logger.error(f"停止系统监控失败: {e}")
            return False
    
    def add_alert_callback(self, callback: Callable[[MonitorAlert], None]):
        """添加告警回调函数"""
        self.alert_callbacks.append(callback)
        self.logger.debug("添加告警回调函数")
    
    def get_current_system_metrics(self) -> SystemMetrics:
        """获取当前系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_gb = memory.used / (1024**3)
            memory_total_gb = memory.total / (1024**3)
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            disk_used_gb = disk.used / (1024**3)
            disk_total_gb = disk.total / (1024**3)
            
            # 网络使用情况
            network = psutil.net_io_counters()
            if self.network_baseline is None:
                self.network_baseline = network
                network_sent_mb = 0
                network_recv_mb = 0
            else:
                network_sent_mb = (network.bytes_sent - self.network_baseline.bytes_sent) / (1024**2)
                network_recv_mb = (network.bytes_recv - self.network_baseline.bytes_recv) / (1024**2)
            
            # 进程和线程数
            process_count = len(psutil.pids())
            thread_count = threading.active_count()
            
            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used_gb=memory_used_gb,
                memory_total_gb=memory_total_gb,
                disk_percent=disk_percent,
                disk_used_gb=disk_used_gb,
                disk_total_gb=disk_total_gb,
                network_sent_mb=network_sent_mb,
                network_recv_mb=network_recv_mb,
                process_count=process_count,
                thread_count=thread_count
            )
        
        except Exception as e:
            self.logger.error(f"获取系统指标失败: {e}")
            return None
    
    def get_current_trading_metrics(self) -> TradingMetrics:
        """获取当前交易指标"""
        try:
            # 这里应该从实际的交易系统获取指标
            # 暂时返回模拟数据
            return TradingMetrics(
                timestamp=datetime.now(),
                active_strategies=0,
                total_positions=0,
                total_pnl=0.0,
                daily_trades=0,
                order_success_rate=0.0,
                connection_status="未连接"
            )
        
        except Exception as e:
            self.logger.error(f"获取交易指标失败: {e}")
            return None
    
    def check_system_alerts(self, metrics: SystemMetrics):
        """检查系统告警"""
        alerts = []
        
        # CPU告警
        if metrics.cpu_percent >= self.cpu_critical_threshold:
            alerts.append(MonitorAlert(
                timestamp=datetime.now(),
                level=MonitorLevel.CRITICAL,
                category="系统资源",
                message=f"CPU使用率严重过高: {metrics.cpu_percent:.1f}%",
                details={"cpu_percent": metrics.cpu_percent}
            ))
        elif metrics.cpu_percent >= self.cpu_warning_threshold:
            alerts.append(MonitorAlert(
                timestamp=datetime.now(),
                level=MonitorLevel.WARNING,
                category="系统资源",
                message=f"CPU使用率过高: {metrics.cpu_percent:.1f}%",
                details={"cpu_percent": metrics.cpu_percent}
            ))
        
        # 内存告警
        if metrics.memory_percent >= self.memory_critical_threshold:
            alerts.append(MonitorAlert(
                timestamp=datetime.now(),
                level=MonitorLevel.CRITICAL,
                category="系统资源",
                message=f"内存使用率严重过高: {metrics.memory_percent:.1f}%",
                details={"memory_percent": metrics.memory_percent, "memory_used_gb": metrics.memory_used_gb}
            ))
        elif metrics.memory_percent >= self.memory_warning_threshold:
            alerts.append(MonitorAlert(
                timestamp=datetime.now(),
                level=MonitorLevel.WARNING,
                category="系统资源",
                message=f"内存使用率过高: {metrics.memory_percent:.1f}%",
                details={"memory_percent": metrics.memory_percent, "memory_used_gb": metrics.memory_used_gb}
            ))
        
        # 磁盘告警
        if metrics.disk_percent >= self.disk_critical_threshold:
            alerts.append(MonitorAlert(
                timestamp=datetime.now(),
                level=MonitorLevel.CRITICAL,
                category="系统资源",
                message=f"磁盘使用率严重过高: {metrics.disk_percent:.1f}%",
                details={"disk_percent": metrics.disk_percent, "disk_used_gb": metrics.disk_used_gb}
            ))
        elif metrics.disk_percent >= self.disk_warning_threshold:
            alerts.append(MonitorAlert(
                timestamp=datetime.now(),
                level=MonitorLevel.WARNING,
                category="系统资源",
                message=f"磁盘使用率过高: {metrics.disk_percent:.1f}%",
                details={"disk_percent": metrics.disk_percent, "disk_used_gb": metrics.disk_used_gb}
            ))
        
        # 处理告警
        for alert in alerts:
            self._handle_alert(alert)
    
    def _handle_alert(self, alert: MonitorAlert):
        """处理告警"""
        try:
            # 添加到告警列表
            self.alerts.append(alert)
            
            # 记录日志
            if alert.level == MonitorLevel.CRITICAL:
                self.logger.critical(f"[{alert.category}] {alert.message}")
            elif alert.level == MonitorLevel.ERROR:
                self.logger.error(f"[{alert.category}] {alert.message}")
            elif alert.level == MonitorLevel.WARNING:
                self.logger.warning(f"[{alert.category}] {alert.message}")
            else:
                self.logger.info(f"[{alert.category}] {alert.message}")
            
            # 发射事件
            if self.event_manager:
                if alert.level in [MonitorLevel.CRITICAL, MonitorLevel.ERROR]:
                    self.event_manager.emit_event(
                        EventType.SYSTEM_ERROR, 
                        asdict(alert), 
                        "SystemMonitor"
                    )
                else:
                    self.event_manager.emit_event(
                        EventType.RISK_WARNING, 
                        asdict(alert), 
                        "SystemMonitor"
                    )
            
            # 调用回调函数
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    self.logger.error(f"告警回调函数执行失败: {e}")
        
        except Exception as e:
            self.logger.error(f"处理告警失败: {e}")
    
    def _monitor_loop(self):
        """监控主循环"""
        self.logger.info("系统监控主循环启动")
        
        while self.running:
            try:
                # 获取系统指标
                system_metrics = self.get_current_system_metrics()
                if system_metrics:
                    self.system_metrics_history.append(system_metrics)
                    
                    # 限制历史数据数量
                    if len(self.system_metrics_history) > self.metrics_history_limit:
                        self.system_metrics_history.pop(0)
                    
                    # 检查告警
                    self.check_system_alerts(system_metrics)
                
                # 获取交易指标
                trading_metrics = self.get_current_trading_metrics()
                if trading_metrics:
                    self.trading_metrics_history.append(trading_metrics)
                    
                    # 限制历史数据数量
                    if len(self.trading_metrics_history) > self.metrics_history_limit:
                        self.trading_metrics_history.pop(0)
                
                # 休眠
                time.sleep(self.monitor_interval)
            
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                time.sleep(5)  # 异常时稍长休眠
        
        self.logger.info("系统监控主循环结束")
    
    def get_recent_metrics(self, minutes: int = 60) -> Dict[str, List]:
        """获取最近的指标数据"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        recent_system = [m for m in self.system_metrics_history if m.timestamp >= cutoff_time]
        recent_trading = [m for m in self.trading_metrics_history if m.timestamp >= cutoff_time]
        
        return {
            'system_metrics': [asdict(m) for m in recent_system],
            'trading_metrics': [asdict(m) for m in recent_trading]
        }
    
    def get_recent_alerts(self, hours: int = 24) -> List[MonitorAlert]:
        """获取最近的告警"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [alert for alert in self.alerts if alert.timestamp >= cutoff_time]
    
    def display_monitor_summary(self):
        """显示监控摘要"""
        self.logger.info("=" * 80)
        self.logger.info("系统监控摘要")
        self.logger.info("=" * 80)
        
        # 当前状态
        self.logger.info(f"监控状态: {'运行中' if self.running else '已停止'}")
        self.logger.info(f"监控间隔: {self.monitor_interval} 秒")
        
        # 最新系统指标
        if self.system_metrics_history:
            latest = self.system_metrics_history[-1]
            self.logger.info(f"\n最新系统指标 ({latest.timestamp.strftime('%H:%M:%S')}):")
            self.logger.info(f"CPU使用率: {latest.cpu_percent:.1f}%")
            self.logger.info(f"内存使用率: {latest.memory_percent:.1f}% ({latest.memory_used_gb:.1f}GB/{latest.memory_total_gb:.1f}GB)")
            self.logger.info(f"磁盘使用率: {latest.disk_percent:.1f}% ({latest.disk_used_gb:.1f}GB/{latest.disk_total_gb:.1f}GB)")
            self.logger.info(f"进程数: {latest.process_count}, 线程数: {latest.thread_count}")
        
        # 最近告警
        recent_alerts = self.get_recent_alerts(24)
        if recent_alerts:
            self.logger.info(f"\n最近24小时告警 ({len(recent_alerts)} 条):")
            for alert in recent_alerts[-5:]:  # 显示最近5条
                self.logger.info(f"{alert.timestamp.strftime('%H:%M:%S')} [{alert.level.value}] {alert.message}")
        else:
            self.logger.info("\n最近24小时无告警")
    
    def save_metrics_to_file(self, filename: str = None):
        """保存指标到文件"""
        try:
            if filename is None:
                filename = f"system_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            data = {
                'system_metrics': [asdict(m) for m in self.system_metrics_history],
                'trading_metrics': [asdict(m) for m in self.trading_metrics_history],
                'alerts': [asdict(a) for a in self.alerts],
                'export_time': datetime.now().isoformat()
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"指标数据已保存到: {filename}")
            return True
        
        except Exception as e:
            self.logger.error(f"保存指标数据失败: {e}")
            return False


if __name__ == "__main__":
    # 测试系统监控器
    monitor = SystemMonitor()
    
    # 添加告警回调
    def alert_callback(alert: MonitorAlert):
        print(f"收到告警: {alert.level.value} - {alert.message}")
    
    monitor.add_alert_callback(alert_callback)
    
    # 启动监控
    monitor.start_monitoring()
    
    # 运行一段时间
    time.sleep(5)
    
    # 显示监控摘要
    monitor.display_monitor_summary()
    
    # 停止监控
    monitor.stop_monitoring()
    
    print("系统监控器测试完成")
