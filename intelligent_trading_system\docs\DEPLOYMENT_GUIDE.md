# 智能量化交易系统部署指南

## 目录
1. [部署概述](#部署概述)
2. [环境准备](#环境准备)
3. [系统安装](#系统安装)
4. [配置设置](#配置设置)
5. [服务部署](#服务部署)
6. [监控设置](#监控设置)
7. [备份策略](#备份策略)
8. [故障恢复](#故障恢复)

## 部署概述

智能量化交易系统支持多种部署方式：

- **开发环境**: 单机开发和测试
- **生产环境**: 高可用性生产部署
- **云端部署**: 云服务器部署
- **容器化部署**: Docker容器部署

## 环境准备

### 系统要求

#### 最低配置
- **操作系统**: Windows 10/11, Ubuntu 18.04+, CentOS 7+
- **CPU**: 4核心 2.0GHz
- **内存**: 8GB RAM
- **存储**: 100GB 可用空间
- **网络**: 稳定的互联网连接

#### 推荐配置
- **操作系统**: Ubuntu 20.04 LTS
- **CPU**: 8核心 3.0GHz
- **内存**: 16GB RAM
- **存储**: 500GB SSD
- **网络**: 专线网络连接

### 软件依赖

#### Python环境
```bash
# Python 3.8+
python --version
# 应显示 Python 3.8.x 或更高版本

# pip包管理器
pip --version
```

#### 必需软件包
```bash
# 更新pip
pip install --upgrade pip

# 安装核心依赖
pip install vnpy>=3.0.0
pip install ta-lib
pip install psutil
pip install schedule
pip install dataclasses-json
pip install numpy
pip install pandas
pip install matplotlib

# 安装数据库驱动（根据需要选择）
pip install sqlite3  # SQLite（默认）
pip install pymysql   # MySQL
pip install psycopg2  # PostgreSQL
```

#### TA-Lib安装

**Windows:**
```bash
# 下载预编译包
pip install TA-Lib
```

**Linux:**
```bash
# 安装依赖
sudo apt-get install build-essential
wget http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-src.tar.gz
tar -xzf ta-lib-0.4.0-src.tar.gz
cd ta-lib/
./configure --prefix=/usr
make
sudo make install

# 安装Python包
pip install TA-Lib
```

## 系统安装

### 1. 获取源代码

```bash
# 创建项目目录
mkdir -p /opt/trading
cd /opt/trading

# 复制系统文件
cp -r intelligent_trading_system/ ./
cd intelligent_trading_system/
```

### 2. 创建虚拟环境

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Linux/Mac:
source venv/bin/activate
# Windows:
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 3. 创建requirements.txt

```bash
# 生成依赖文件
cat > requirements.txt << EOF
vnpy>=3.0.0
ta-lib>=0.4.0
psutil>=5.8.0
schedule>=1.1.0
dataclasses-json>=0.5.0
numpy>=1.21.0
pandas>=1.3.0
matplotlib>=3.4.0
pymysql>=1.0.0
EOF
```

### 4. 目录结构设置

```bash
# 创建必要目录
mkdir -p logs
mkdir -p data
mkdir -p backups
mkdir -p temp

# 设置权限
chmod 755 *.py
chmod 755 -R modules/
chmod 755 -R config/
chmod 755 -R utils/
chmod 777 logs/
chmod 777 data/
chmod 777 backups/
chmod 777 temp/
```

## 配置设置

### 1. 基础配置

```bash
# 复制配置模板
cp config/system_config.py.template config/system_config.py
cp config/trading_config.py.template config/trading_config.py
cp config/risk_config.py.template config/risk_config.py

# 编辑配置文件
nano config/system_config.py
```

### 2. 数据库配置

#### SQLite配置（默认）
```python
# config/system_config.py
database = DatabaseConfig(
    path="/opt/trading/intelligent_trading_system/data/vnpy_data.db",
    driver="sqlite"
)
```

#### MySQL配置
```python
# config/system_config.py
database = DatabaseConfig(
    driver="mysql",
    host="localhost",
    port=3306,
    username="trading_user",
    password="secure_password",
    database="trading_db"
)
```

### 3. 日志配置

```python
# config/system_config.py
logging = LoggingConfig(
    level="INFO",
    file_enabled=True,
    console_enabled=False,  # 生产环境建议关闭
    max_file_size=50,       # 50MB
    backup_count=10,
    log_dir="/opt/trading/intelligent_trading_system/logs"
)
```

### 4. CTP接口配置

创建CTP配置文件：
```bash
# 创建CTP配置
cat > config/ctp_config.json << EOF
{
    "用户名": "your_username",
    "密码": "your_password",
    "经纪商代码": "9999",
    "交易服务器": "***************:10130",
    "行情服务器": "***************:10131",
    "产品名称": "simnow_client_test",
    "授权编码": "0000000000000000"
}
EOF
```

## 服务部署

### 1. 系统服务配置

创建systemd服务文件：

```bash
# 创建服务文件
sudo tee /etc/systemd/system/intelligent-trading.service << EOF
[Unit]
Description=Intelligent Trading System
After=network.target

[Service]
Type=simple
User=trading
Group=trading
WorkingDirectory=/opt/trading/intelligent_trading_system
Environment=PATH=/opt/trading/intelligent_trading_system/venv/bin
ExecStart=/opt/trading/intelligent_trading_system/venv/bin/python start_system.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
```

### 2. 用户和权限设置

```bash
# 创建专用用户
sudo useradd -r -s /bin/bash -d /opt/trading trading
sudo chown -R trading:trading /opt/trading

# 设置sudo权限（如需要）
echo "trading ALL=(ALL) NOPASSWD: /bin/systemctl restart intelligent-trading" | sudo tee /etc/sudoers.d/trading
```

### 3. 服务管理

```bash
# 重新加载systemd
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable intelligent-trading

# 启动服务
sudo systemctl start intelligent-trading

# 查看状态
sudo systemctl status intelligent-trading

# 查看日志
sudo journalctl -u intelligent-trading -f
```

### 4. 开机自启动

```bash
# 确保服务开机自启动
sudo systemctl enable intelligent-trading

# 验证自启动状态
sudo systemctl is-enabled intelligent-trading
```

## 监控设置

### 1. 系统监控脚本

创建监控脚本：

```bash
# 创建监控脚本
cat > /opt/trading/monitor.sh << 'EOF'
#!/bin/bash

SYSTEM_DIR="/opt/trading/intelligent_trading_system"
LOG_FILE="$SYSTEM_DIR/logs/monitor.log"
PID_FILE="$SYSTEM_DIR/system.pid"

# 检查系统状态
check_system() {
    echo "$(date): 检查系统状态..." >> $LOG_FILE
    
    # 检查进程
    if [ -f "$PID_FILE" ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null; then
            echo "$(date): 系统运行正常 (PID: $PID)" >> $LOG_FILE
        else
            echo "$(date): 系统进程不存在，尝试重启..." >> $LOG_FILE
            restart_system
        fi
    else
        echo "$(date): PID文件不存在，尝试重启..." >> $LOG_FILE
        restart_system
    fi
    
    # 检查日志文件大小
    check_log_size
    
    # 检查磁盘空间
    check_disk_space
}

# 重启系统
restart_system() {
    echo "$(date): 重启交易系统..." >> $LOG_FILE
    sudo systemctl restart intelligent-trading
    sleep 10
    
    if sudo systemctl is-active --quiet intelligent-trading; then
        echo "$(date): 系统重启成功" >> $LOG_FILE
    else
        echo "$(date): 系统重启失败" >> $LOG_FILE
        # 发送告警
        send_alert "交易系统重启失败"
    fi
}

# 检查日志文件大小
check_log_size() {
    LOG_SIZE=$(du -m $SYSTEM_DIR/logs/ | cut -f1)
    if [ $LOG_SIZE -gt 1000 ]; then  # 1GB
        echo "$(date): 日志文件过大 (${LOG_SIZE}MB)，清理旧日志..." >> $LOG_FILE
        find $SYSTEM_DIR/logs/ -name "*.log.*" -mtime +7 -delete
    fi
}

# 检查磁盘空间
check_disk_space() {
    DISK_USAGE=$(df $SYSTEM_DIR | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ $DISK_USAGE -gt 90 ]; then
        echo "$(date): 磁盘空间不足 (${DISK_USAGE}%)" >> $LOG_FILE
        send_alert "磁盘空间不足: ${DISK_USAGE}%"
    fi
}

# 发送告警
send_alert() {
    MESSAGE="$1"
    echo "$(date): 告警: $MESSAGE" >> $LOG_FILE
    # 这里可以添加邮件、短信等告警方式
}

# 主函数
main() {
    check_system
}

main "$@"
EOF

# 设置执行权限
chmod +x /opt/trading/monitor.sh
```

### 2. 定时监控任务

```bash
# 添加crontab任务
(crontab -l 2>/dev/null; echo "*/5 * * * * /opt/trading/monitor.sh") | crontab -

# 验证crontab
crontab -l
```

### 3. 日志轮转配置

```bash
# 创建logrotate配置
sudo tee /etc/logrotate.d/intelligent-trading << EOF
/opt/trading/intelligent_trading_system/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 trading trading
    postrotate
        sudo systemctl reload intelligent-trading
    endscript
}
EOF
```

## 备份策略

### 1. 数据备份脚本

```bash
# 创建备份脚本
cat > /opt/trading/backup.sh << 'EOF'
#!/bin/bash

SYSTEM_DIR="/opt/trading/intelligent_trading_system"
BACKUP_DIR="/opt/trading/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="trading_backup_$DATE"

# 创建备份目录
mkdir -p $BACKUP_DIR/$BACKUP_NAME

# 备份配置文件
cp -r $SYSTEM_DIR/config/ $BACKUP_DIR/$BACKUP_NAME/

# 备份数据库
if [ -f "$SYSTEM_DIR/data/vnpy_data.db" ]; then
    cp $SYSTEM_DIR/data/vnpy_data.db $BACKUP_DIR/$BACKUP_NAME/
fi

# 备份日志（最近7天）
find $SYSTEM_DIR/logs/ -name "*.log" -mtime -7 -exec cp {} $BACKUP_DIR/$BACKUP_NAME/ \;

# 压缩备份
cd $BACKUP_DIR
tar -czf $BACKUP_NAME.tar.gz $BACKUP_NAME/
rm -rf $BACKUP_NAME/

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "trading_backup_*.tar.gz" -mtime +30 -delete

echo "$(date): 备份完成: $BACKUP_NAME.tar.gz"
EOF

# 设置执行权限
chmod +x /opt/trading/backup.sh
```

### 2. 自动备份任务

```bash
# 添加每日备份任务
(crontab -l 2>/dev/null; echo "0 2 * * * /opt/trading/backup.sh") | crontab -
```

### 3. 远程备份

```bash
# 创建远程备份脚本
cat > /opt/trading/remote_backup.sh << 'EOF'
#!/bin/bash

LOCAL_BACKUP_DIR="/opt/trading/backups"
REMOTE_HOST="backup.example.com"
REMOTE_USER="backup_user"
REMOTE_DIR="/backups/trading"

# 同步到远程服务器
rsync -avz --delete $LOCAL_BACKUP_DIR/ $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/

echo "$(date): 远程备份完成"
EOF

# 设置执行权限
chmod +x /opt/trading/remote_backup.sh

# 添加每周远程备份任务
(crontab -l 2>/dev/null; echo "0 3 * * 0 /opt/trading/remote_backup.sh") | crontab -
```

## 故障恢复

### 1. 系统恢复流程

```bash
# 创建恢复脚本
cat > /opt/trading/restore.sh << 'EOF'
#!/bin/bash

if [ $# -ne 1 ]; then
    echo "用法: $0 <备份文件名>"
    echo "示例: $0 trading_backup_20231201_020000.tar.gz"
    exit 1
fi

BACKUP_FILE="$1"
SYSTEM_DIR="/opt/trading/intelligent_trading_system"
BACKUP_DIR="/opt/trading/backups"

# 检查备份文件是否存在
if [ ! -f "$BACKUP_DIR/$BACKUP_FILE" ]; then
    echo "错误: 备份文件不存在: $BACKUP_DIR/$BACKUP_FILE"
    exit 1
fi

# 停止系统
echo "停止交易系统..."
sudo systemctl stop intelligent-trading

# 备份当前配置
echo "备份当前配置..."
cp -r $SYSTEM_DIR/config/ $SYSTEM_DIR/config_backup_$(date +%Y%m%d_%H%M%S)/

# 解压备份文件
echo "解压备份文件..."
cd $BACKUP_DIR
tar -xzf $BACKUP_FILE

# 恢复配置文件
BACKUP_NAME=$(basename $BACKUP_FILE .tar.gz)
echo "恢复配置文件..."
cp -r $BACKUP_DIR/$BACKUP_NAME/config/* $SYSTEM_DIR/config/

# 恢复数据库
if [ -f "$BACKUP_DIR/$BACKUP_NAME/vnpy_data.db" ]; then
    echo "恢复数据库..."
    cp $BACKUP_DIR/$BACKUP_NAME/vnpy_data.db $SYSTEM_DIR/data/
fi

# 清理临时文件
rm -rf $BACKUP_DIR/$BACKUP_NAME/

# 启动系统
echo "启动交易系统..."
sudo systemctl start intelligent-trading

# 检查状态
sleep 5
if sudo systemctl is-active --quiet intelligent-trading; then
    echo "系统恢复成功"
else
    echo "系统恢复失败，请检查日志"
    exit 1
fi
EOF

# 设置执行权限
chmod +x /opt/trading/restore.sh
```

### 2. 紧急停止脚本

```bash
# 创建紧急停止脚本
cat > /opt/trading/emergency_stop.sh << 'EOF'
#!/bin/bash

echo "执行紧急停止..."

# 停止系统服务
sudo systemctl stop intelligent-trading

# 强制终止相关进程
pkill -f "python.*start_system.py"
pkill -f "python.*main_controller.py"

# 记录紧急停止事件
echo "$(date): 紧急停止执行" >> /opt/trading/intelligent_trading_system/logs/emergency.log

echo "紧急停止完成"
EOF

# 设置执行权限
chmod +x /opt/trading/emergency_stop.sh
```

### 3. 健康检查脚本

```bash
# 创建健康检查脚本
cat > /opt/trading/health_check.sh << 'EOF'
#!/bin/bash

SYSTEM_DIR="/opt/trading/intelligent_trading_system"

echo "=== 交易系统健康检查 ==="
echo "检查时间: $(date)"
echo

# 检查服务状态
echo "1. 服务状态:"
sudo systemctl status intelligent-trading --no-pager -l

echo
echo "2. 进程状态:"
ps aux | grep -E "(python.*start_system|python.*main_controller)" | grep -v grep

echo
echo "3. 端口监听:"
netstat -tlnp | grep python

echo
echo "4. 磁盘空间:"
df -h $SYSTEM_DIR

echo
echo "5. 内存使用:"
free -h

echo
echo "6. 最近日志:"
tail -n 10 $SYSTEM_DIR/logs/system.log

echo
echo "=== 健康检查完成 ==="
EOF

# 设置执行权限
chmod +x /opt/trading/health_check.sh
```

## 部署验证

### 1. 功能测试

```bash
# 运行系统测试
cd /opt/trading/intelligent_trading_system
python run_tests.py --mode quick

# 测试系统启动
python start_system.py --mode test

# 检查配置
python start_system.py --mode config
```

### 2. 性能测试

```bash
# 运行性能测试
python run_tests.py --mode performance

# 监控系统资源
top -p $(pgrep -f "python.*start_system")
```

### 3. 集成测试

```bash
# 运行集成测试
python run_tests.py --mode integration

# 检查系统状态
./health_check.sh
```

通过以上部署指南，可以在生产环境中安全、稳定地部署智能量化交易系统。建议在部署前充分测试，并制定详细的运维计划。
