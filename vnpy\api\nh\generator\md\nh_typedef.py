TErrorCodeType = "int"
TErrorMessageType = "string"
TDateTimeType = "string"
TDateType = "string"
TTimeType = "string"
TTimeListType = "string"
TLongTimeType = "int"
TYearType = "int"
TMonthType = "int"
TMillisecType = "int"
TMdVolumeType = "double"
TVolumeType = "int"
TPriceType = "double"
TMoneyType = "double"
TSaveIntType = "int"
TSaveDoubleType = "double"
TSaveStringType = "string"
TRangeType = "double"
TDeltaType = "double"
TMdSourceType = "string"
TProductDotType = "double"
TUpperTickType = "double"
TLowerTickType = "int"
TMachineInfoType = "string"
TResveredInfoType = "string"
TWorkstationInfoType = "string"
TRouteKeyType = "string"
TIpType = "string"
TMacAddressType = "string"
TSequenceIDType = "int"
TRspCodeType = "int"
TRspStringType = "string"
TServerIDType = "int"
TServerNameType = "string"
TExchangeListType = "string"
TOMSStatusType = "char"
TUserIDType = "string"
TPassWordType = "string"
TOneTimePassWordType = "string"
TCaInfoType = "string"
TCAInfoType = "string"
TServerCDKeyType = "string"
TPublicKeyType = "string"
TDevelopCodeType = "string"
TDevelopLicenseType = "string"
TTradeNoType = "string"
TClientNoType = "string"
TReservedInfoType = "string"
TOperatorNoType = "string"
TOperatorNameType = "string"
TIsEncryptType = "bool"
TIsCaLoginType = "char"
TIsForcePasswordType = "char"
TLoginNoType = "string"
TLoginNameType = "string"
TLoginPasswordType = "string"
TOtpPassType = "string"
TPasswordTypeType = "char"
TQryNotifyTypeType = "char"
TExchangeNoType = "string"
TExchangeNameType = "string"
TExchangeStateType = "char"
TCommodityNoType = "string"
TCommodityNameType = "string"
TCommodityTypeType = "char"
TCommodityAttributeType = "string"
TCommodityStateType = "char"
TDeliveryModeType = "char"
TDeliveryDaysType = "int"
THoldKeyIdType = "int"
TDeliveryKeyIdType = "int"
TDepositCalculateModeType = "char"
TContractNoType = "string"
TContractNameType = "string"
TContractLastDays = "int"
TContractTypeType = "char"
TContractStateType = "char"
TContractTradeStateType = "int"
TOrderIdType = "int"
TLocalNoType = "string"
TSystemNoType = "string"
TMatchNoType = "string"
TFeLocalNoType = "string"
TMatchIdType = "int"
TStreamIdType = "int"
TIsRiskOrderType = "char"
TDirectType = "char"
TOffsetType = "char"
TCoverModeType = "char"
THedgeType = "char"
TManualFeeType = "char"
TOrderTypeType = "char"
TOrderWayType = "char"
TOrderModeType = "char"
TOrderInputType = "char"
TOrderStateType = "char"
TDeletedType = "char"
TMatchWayType = "char"
TMatchModeType = "char"
TAddOneType = "char"
TCurrencyNoType = "string"
TCurrencyNameType = "string"
TIsPrimaryCurrencyType = "char"
TIsShareCurrencyType = "char"
TExchangeRateType = "double"
TContainTotleType = "char"
TBankType = "string"
TBankNameType = "string"
TAccountType = "string"
TLWFlagType = "char"
TDepositModeType = "char"
TCashSerialIdType = "int"
TAdjustSerialIdType = "int"
TCashTypeType = "char"
TCashStateType = "char"
TCashModeType = "char"
TCashRemarkType = "string"
TAdjustStateType = "char"
TAdjustTypeType = "char"
TMoneyChgType = "int"
