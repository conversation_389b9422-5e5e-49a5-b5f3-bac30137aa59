"""
调度器模块测试
测试任务调度和事件管理功能
"""

import unittest
import os
import sys
import time

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from modules.scheduler.task_scheduler import TaskScheduler
from modules.scheduler.event_manager import EventManager, EventType


class TestSchedulerModule(unittest.TestCase):
    """调度器模块测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.task_scheduler = TaskScheduler()
        self.event_manager = EventManager()
    
    def tearDown(self):
        """测试后清理"""
        if self.task_scheduler:
            self.task_scheduler.stop_scheduler()
        if self.event_manager:
            self.event_manager.stop()
    
    def test_task_scheduler_initialization(self):
        """测试任务调度器初始化"""
        self.assertIsNotNone(self.task_scheduler)
        print("✓ 任务调度器初始化测试通过")
    
    def test_event_manager_initialization(self):
        """测试事件管理器初始化"""
        self.assertIsNotNone(self.event_manager)
        print("✓ 事件管理器初始化测试通过")
    
    def test_task_operations(self):
        """测试任务操作"""
        # 测试添加任务
        def test_task():
            return True
        
        self.task_scheduler.add_task("test", test_task, "*/5 * * * * *", "测试任务")
        
        # 测试获取任务
        tasks = self.task_scheduler.get_all_tasks()
        self.assertGreater(len(tasks), 0)
        
        print("✓ 任务操作测试通过")
    
    def test_event_operations(self):
        """测试事件操作"""
        # 启动事件管理器
        self.event_manager.start()
        
        # 测试事件发射
        received_events = []
        
        def event_handler(event):
            received_events.append(event)
        
        self.event_manager.register_handler(EventType.SYSTEM_START, event_handler)
        self.event_manager.emit_event(EventType.SYSTEM_START, "测试数据", "TestSource")
        
        # 等待事件处理
        time.sleep(0.1)
        
        self.assertEqual(len(received_events), 1)
        print("✓ 事件操作测试通过")


if __name__ == "__main__":
    unittest.main()
