{"cells": [{"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["#%%\n", "from datetime import datetime\n", "from importlib import reload\n", "\n", "import vnpy.app.portfolio_strategy\n", "reload(vnpy.app.portfolio_strategy)\n", "\n", "from vnpy.app.portfolio_strategy import BacktestingEngine\n", "from vnpy.trader.constant import Interval\n", "\n", "import vnpy.app.portfolio_strategy.strategies.pair_trading_strategy as stg\n", "reload(stg)\n", "from vnpy.app.portfolio_strategy.strategies.pair_trading_strategy import PairTradingStrategy\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["#%%\n", "engine = BacktestingEngine()\n", "engine.set_parameters(\n", "    vt_symbols=[\"y888.DCE\", \"p888.DCE\"],\n", "    interval=Interval.MINUTE,\n", "    start=datetime(2019, 1, 1),\n", "    end=datetime(2020, 4, 30),\n", "    rates={\n", "        \"y888.DCE\": 0/10000,\n", "        \"p888.DCE\": 0/10000\n", "    },\n", "    slippages={\n", "        \"y888.DCE\": 0,\n", "        \"p888.DCE\": 0\n", "    },\n", "    sizes={\n", "        \"y888.DCE\": 10,\n", "        \"p888.DCE\": 10\n", "    },\n", "    priceticks={\n", "        \"y888.DCE\": 1,\n", "        \"p888.DCE\": 1\n", "    },\n", "    capital=1_000_000,\n", ")\n", "\n", "setting = {\n", "    \"boll_window\": 20,\n", "    \"boll_dev\": 1,\n", "}\n", "engine.add_strategy(PairTradingStrategy, setting)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2020-05-14 22:43:33.533760\t开始加载历史数据\n", "2020-05-14 22:43:33.543498\ty888.DCE加载进度： [6%]\n", "2020-05-14 22:43:33.552295\ty888.DCE加载进度：# [12%]\n", "2020-05-14 22:43:33.563048\ty888.DCE加载进度：# [19%]\n", "2020-05-14 22:43:33.575737\ty888.DCE加载进度：## [25%]\n", "2020-05-14 22:43:33.583561\ty888.DCE加载进度：### [31%]\n", "2020-05-14 22:43:33.596245\ty888.DCE加载进度：### [37%]\n", "2020-05-14 22:43:33.609915\ty888.DCE加载进度：#### [43%]\n", "2020-05-14 22:43:33.620690\ty888.DCE加载进度：#### [49%]\n", "2020-05-14 22:43:33.633346\ty888.DCE加载进度：##### [56%]\n", "2020-05-14 22:43:33.642142\ty888.DCE加载进度：###### [62%]\n", "2020-05-14 22:43:33.652902\ty888.DCE加载进度：###### [68%]\n", "2020-05-14 22:43:33.668505\ty888.DCE加载进度：####### [74%]\n", "2020-05-14 22:43:33.683148\ty888.DCE加载进度：######## [80%]\n", "2020-05-14 22:43:33.690953\ty888.DCE加载进度：######## [87%]\n", "2020-05-14 22:43:33.698781\ty888.DCE加载进度：######### [93%]\n", "2020-05-14 22:43:33.706577\ty888.DCE加载进度：######### [99%]\n", "2020-05-14 22:43:33.708538\ty888.DCE加载进度：########## [100%]\n", "2020-05-14 22:43:33.708538\ty888.<PERSON>E历史数据加载完成，数据量：104370\n", "2020-05-14 22:43:33.720254\tp888.DCE加载进度： [6%]\n", "2020-05-14 22:43:33.730023\tp888.DCE加载进度：# [12%]\n", "2020-05-14 22:43:33.740771\tp888.DCE加载进度：# [19%]\n", "2020-05-14 22:43:33.751501\tp888.DCE加载进度：## [25%]\n", "2020-05-14 22:43:33.762254\tp888.DCE加载进度：### [31%]\n", "2020-05-14 22:43:33.772019\tp888.DCE加载进度：### [37%]\n", "2020-05-14 22:43:33.783738\tp888.DCE加载进度：#### [43%]\n", "2020-05-14 22:43:33.796441\tp888.DCE加载进度：#### [49%]\n", "2020-05-14 22:43:33.807183\tp888.DCE加载进度：##### [56%]\n", "2020-05-14 22:43:33.814980\tp888.DCE加载进度：###### [62%]\n", "2020-05-14 22:43:33.840377\tp888.DCE加载进度：###### [68%]\n", "2020-05-14 22:43:33.852140\tp888.DCE加载进度：####### [74%]\n", "2020-05-14 22:43:33.869683\tp888.DCE加载进度：######## [80%]\n", "2020-05-14 22:43:33.876528\tp888.DCE加载进度：######## [87%]\n", "2020-05-14 22:43:33.889272\tp888.DCE加载进度：######### [93%]\n", "2020-05-14 22:43:33.898000\tp888.DCE加载进度：######### [99%]\n", "2020-05-14 22:43:33.899004\tp888.DCE加载进度：########## [100%]\n", "2020-05-14 22:43:33.899992\tp888.<PERSON>E历史数据加载完成，数据量：104370\n", "2020-05-14 22:43:33.899992\t所有历史数据加载完成\n", "2020-05-14 22:43:33.998678\t策略初始化完成\n", "1 20\n", "2020-05-14 22:43:33.998678\t开始回放历史数据\n", "2020-05-14 22:43:39.985388\t历史数据回放结束\n", "2020-05-14 22:43:39.987367\t开始计算逐日盯市盈亏\n", "2020-05-14 22:43:40.134829\t逐日盯市盈亏计算完成\n", "2020-05-14 22:43:40.134829\t开始计算策略统计指标\n", "2020-05-14 22:43:40.150458\t------------------------------\n", "2020-05-14 22:43:40.150458\t首个交易日：\t2019-01-02\n", "2020-05-14 22:43:40.150458\t最后交易日：\t2020-04-29\n", "2020-05-14 22:43:40.150458\t总交易日：\t322\n", "2020-05-14 22:43:40.150458\t盈利交易日：\t238\n", "2020-05-14 22:43:40.151430\t亏损交易日：\t77\n", "2020-05-14 22:43:40.151430\t起始资金：\t1,000,000.00\n", "2020-05-14 22:43:40.151430\t结束资金：\t1,059,500.00\n", "2020-05-14 22:43:40.151430\t总收益率：\t5.95%\n", "2020-05-14 22:43:40.151430\t年化收益：\t4.43%\n", "2020-05-14 22:43:40.151430\t最大回撤: \t-2,570.00\n", "2020-05-14 22:43:40.153355\t百分比最大回撤: -0.25%\n", "2020-05-14 22:43:40.153355\t最长回撤天数: \t25\n", "2020-05-14 22:43:40.153355\t总盈亏：\t59,500.00\n", "2020-05-14 22:43:40.153355\t总手续费：\t0.00\n", "2020-05-14 22:43:40.153355\t总滑点：\t0.00\n", "2020-05-14 22:43:40.153355\t总成交金额：\t1,875,466,180.00\n", "2020-05-14 22:43:40.153355\t总成交笔数：\t33534\n", "2020-05-14 22:43:40.153355\t日均盈亏：\t184.78\n", "2020-05-14 22:43:40.153355\t日均手续费：\t0.00\n", "2020-05-14 22:43:40.153355\t日均滑点：\t0.00\n", "2020-05-14 22:43:40.153355\t日均成交金额：\t5,824,429.13\n", "2020-05-14 22:43:40.153355\t日均成交笔数：\t104.14285714285714\n", "2020-05-14 22:43:40.153355\t日均收益率：\t0.02%\n", "2020-05-14 22:43:40.153355\t收益标准差：\t0.03%\n", "2020-05-14 22:43:40.153355\tSharpe Ratio：\t8.92\n", "2020-05-14 22:43:40.153355\t收益回撤比：\t23.92\n", "2020-05-14 22:43:40.154344\t策略统计指标计算完成\n"]}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x1152 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#%%\n", "engine.load_data()\n", "engine.run_backtesting()\n", "df = engine.calculate_result()\n", "engine.calculate_statistics()\n", "engine.show_chart()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}}, "nbformat": 4, "nbformat_minor": 2}