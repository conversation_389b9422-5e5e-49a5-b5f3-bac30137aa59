"""
监控模块测试
测试系统监控和告警功能
"""

import unittest
import os
import sys
import time

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from modules.monitoring.system_monitor import SystemMonitor
from modules.monitoring.alert_manager import AlertManager
from modules.scheduler.event_manager import EventManager


class TestMonitoringModule(unittest.TestCase):
    """监控模块测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.event_manager = EventManager()
        self.system_monitor = SystemMonitor(self.event_manager)
        self.alert_manager = AlertManager()
    
    def tearDown(self):
        """测试后清理"""
        if self.system_monitor:
            self.system_monitor.stop_monitoring()
        if self.event_manager:
            self.event_manager.stop()
    
    def test_system_monitor_initialization(self):
        """测试系统监控器初始化"""
        self.assertIsNotNone(self.system_monitor)
        print("✓ 系统监控器初始化测试通过")
    
    def test_alert_manager_initialization(self):
        """测试告警管理器初始化"""
        self.assertIsNotNone(self.alert_manager)
        print("✓ 告警管理器初始化测试通过")
    
    def test_monitoring_operations(self):
        """测试监控操作"""
        # 启动监控
        self.system_monitor.start_monitoring()
        
        # 等待监控数据收集
        time.sleep(1)
        
        # 获取系统指标
        metrics = self.system_monitor.get_current_system_metrics()
        self.assertIsNotNone(metrics)
        
        print("✓ 监控操作测试通过")
    
    def test_alert_operations(self):
        """测试告警操作"""
        # 测试告警处理
        alert_data = {
            'type': 'system_error',
            'message': '测试告警',
            'level': 'warning'
        }
        
        result = self.alert_manager.process_alert(alert_data)
        self.assertTrue(result)
        
        print("✓ 告警操作测试通过")


if __name__ == "__main__":
    unittest.main()
