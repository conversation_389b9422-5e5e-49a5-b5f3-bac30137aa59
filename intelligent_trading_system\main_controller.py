"""
智能量化交易系统主控程序
基于zdrun.py架构，集成所有模块，提供完整的自动化交易功能
"""

import os
import sys
import multiprocessing
import threading
from time import sleep
from datetime import datetime, time
from logging import INFO
from copy import deepcopy

# 添加路径
sys.path.append(os.path.dirname(__file__))
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

# vnpy核心模块
from vnpy.event import EventEngine
from vnpy.trader.setting import SETTINGS
from vnpy.trader.engine import MainEngine
from vnpy.trader.utility import load_json

# vnpy应用模块
from vnpy.gateway.ctp import CtpGateway
from vnpy.app.cta_strategy import CtaStrategyApp
from vnpy.app.data_recorder import DataRecorderApp
from vnpy.app.cta_strategy.base import EVENT_CTA_LOG
from vnpy.app.data_recorder.engine import RecorderEngine
from vnpy.app.risk_manager.engine import RiskManagerEngine

# 智能交易系统模块
from config import get_system_config, get_trading_config, get_risk_config
from core.account_manager import AccountManager
from modules.screening.futures_scanner import FuturesScanner
from modules.allocation.capital_allocator import CapitalAllocator
from modules.allocation.position_manager import PositionManager
from modules.switching.main_contract_detector import MainContractDetector
from modules.switching.contract_switcher import ContractSwitcher
from modules.strategy.strategy_manager import StrategyManager
from modules.scheduler.task_scheduler import TaskScheduler
from modules.scheduler.event_manager import EventManager, EventType
from modules.monitoring.system_monitor import SystemMonitor
from modules.monitoring.alert_manager import AlertManager
from utils.logger import get_logger


# 配置日志
SETTINGS["log.active"] = True
SETTINGS["log.level"] = INFO
SETTINGS["log.console"] = True


class IntelligentTradingSystem:
    """智能交易系统主控制器"""
    
    def __init__(self):
        """初始化智能交易系统"""
        self.logger = get_logger("IntelligentTradingSystem")
        
        # 加载配置
        self.system_config = get_system_config()
        self.trading_config = get_trading_config()
        self.risk_config = get_risk_config()
        
        # vnpy核心组件
        self.event_engine: EventEngine = None
        self.main_engine: MainEngine = None
        self.cta_engine = None
        self.data_recorder = None
        self.risk_manager = None
        
        # 智能交易系统组件
        self.account_manager: AccountManager = None
        self.futures_scanner: FuturesScanner = None
        self.capital_allocator: CapitalAllocator = None
        self.position_manager: PositionManager = None
        self.contract_detector: MainContractDetector = None
        self.contract_switcher: ContractSwitcher = None
        self.strategy_manager: StrategyManager = None
        self.task_scheduler: TaskScheduler = None
        self.event_manager: EventManager = None
        self.system_monitor: SystemMonitor = None
        self.alert_manager: AlertManager = None
        
        # 系统状态
        self.initialized = False
        self.running = False
        
        self.logger.info("智能交易系统初始化开始")
    
    def initialize_system(self) -> bool:
        """初始化系统"""
        try:
            self.logger.info("开始初始化系统组件...")
            
            # 1. 初始化vnpy核心组件
            if not self._initialize_vnpy_components():
                return False
            
            # 2. 初始化智能交易系统组件
            if not self._initialize_trading_components():
                return False
            
            # 3. 初始化调度和监控组件
            if not self._initialize_scheduler_components():
                return False
            
            # 4. 设置定时任务
            if not self._setup_scheduled_tasks():
                return False
            
            # 5. 设置事件监听
            if not self._setup_event_handlers():
                return False
            
            self.initialized = True
            self.logger.info("系统初始化完成")
            return True
        
        except Exception as e:
            self.logger.error(f"系统初始化失败: {e}")
            return False
    
    def _initialize_vnpy_components(self) -> bool:
        """初始化vnpy核心组件"""
        try:
            # 事件引擎
            self.event_engine = EventEngine()
            
            # 主引擎
            self.main_engine = MainEngine(self.event_engine)
            self.main_engine.add_gateway(CtpGateway)
            
            # CTA策略引擎
            self.cta_engine = self.main_engine.add_app(CtaStrategyApp)
            
            # 数据记录引擎
            self.data_recorder = WholeMarketRecorder(self.main_engine, self.event_engine)
            
            # 风险管理引擎
            self.risk_manager = RiskManagerEngine(self.main_engine, self.event_engine)
            
            # 注册日志事件
            log_engine = self.main_engine.get_engine("log")
            self.event_engine.register(EVENT_CTA_LOG, log_engine.process_log_event)
            
            self.logger.info("vnpy核心组件初始化完成")
            return True
        
        except Exception as e:
            self.logger.error(f"vnpy核心组件初始化失败: {e}")
            return False
    
    def _initialize_trading_components(self) -> bool:
        """初始化交易组件"""
        try:
            # 账户管理器
            self.account_manager = AccountManager()
            
            # 期货筛选器
            self.futures_scanner = FuturesScanner()
            
            # 资金分配器
            self.capital_allocator = CapitalAllocator()
            
            # 仓位管理器
            self.position_manager = PositionManager()
            
            # 主力合约检测器
            self.contract_detector = MainContractDetector()
            
            # 合约切换器
            self.contract_switcher = ContractSwitcher(self.contract_detector)
            
            # 策略管理器
            self.strategy_manager = StrategyManager(self.cta_engine)
            
            self.logger.info("交易组件初始化完成")
            return True
        
        except Exception as e:
            self.logger.error(f"交易组件初始化失败: {e}")
            return False
    
    def _initialize_scheduler_components(self) -> bool:
        """初始化调度和监控组件"""
        try:
            # 事件管理器
            self.event_manager = EventManager()
            
            # 任务调度器
            self.task_scheduler = TaskScheduler()
            
            # 系统监控器
            self.system_monitor = SystemMonitor(self.event_manager)
            
            # 告警管理器
            self.alert_manager = AlertManager()
            
            self.logger.info("调度和监控组件初始化完成")
            return True
        
        except Exception as e:
            self.logger.error(f"调度和监控组件初始化失败: {e}")
            return False
    
    def _setup_scheduled_tasks(self) -> bool:
        """设置定时任务"""
        try:
            # 08:00 - 品种筛选
            self.task_scheduler.add_task(
                "morning_screening",
                self._run_morning_screening,
                "08:00",
                "早盘品种筛选和评估"
            )
            
            # 10:15 - 资金分配
            self.task_scheduler.add_task(
                "capital_allocation",
                self._run_capital_allocation,
                "10:15",
                "资金分配和仓位调整"
            )
            
            # 11:30 - 仓位调整
            self.task_scheduler.add_task(
                "position_adjustment",
                self._run_position_adjustment,
                "11:30",
                "午间仓位调整"
            )
            
            # 15:25 - 主力合约切换
            self.task_scheduler.add_task(
                "contract_switching",
                self._run_contract_switching,
                "15:25",
                "主力合约切换检查"
            )
            
            # 20:00 - 夜盘准备
            self.task_scheduler.add_task(
                "night_preparation",
                self._run_night_preparation,
                "20:00",
                "夜盘交易准备"
            )
            
            self.logger.info("定时任务设置完成")
            return True
        
        except Exception as e:
            self.logger.error(f"定时任务设置失败: {e}")
            return False
    
    def _setup_event_handlers(self) -> bool:
        """设置事件处理器"""
        try:
            # 系统监控告警处理
            self.system_monitor.add_alert_callback(self.alert_manager.process_alert)
            
            # 事件管理器事件处理
            self.event_manager.register_handler(EventType.SYSTEM_ERROR, self._handle_system_error)
            self.event_manager.register_handler(EventType.RISK_WARNING, self._handle_risk_warning)
            
            self.logger.info("事件处理器设置完成")
            return True
        
        except Exception as e:
            self.logger.error(f"事件处理器设置失败: {e}")
            return False
    
    def start_system(self) -> bool:
        """启动系统"""
        try:
            if not self.initialized:
                self.logger.error("系统未初始化，无法启动")
                return False
            
            if self.running:
                self.logger.warning("系统已在运行中")
                return True
            
            self.logger.info("开始启动系统...")
            
            # 1. 启动事件管理器
            self.event_manager.start()
            
            # 2. 启动系统监控
            self.system_monitor.start_monitoring()
            
            # 3. 连接账户
            if not self.account_manager.connect_all_accounts():
                self.logger.error("账户连接失败")
                return False
            
            # 4. 连接CTP
            ctp_setting = load_json("connect_ctp.json")
            self.main_engine.connect(ctp_setting, "CTP")
            self.logger.info("连接CTP接口")
            sleep(30)  # 等待连接建立
            
            # 5. 初始化CTA引擎
            self.cta_engine.init_engine()
            self.logger.info("CTA策略引擎初始化完成")
            
            # 6. 初始化所有策略
            self.cta_engine.init_all_strategies()
            while not self.cta_engine.check_all_strategy_init():
                sleep(30)
            self.logger.info("CTA策略全部初始化")
            
            # 7. 启动任务调度器
            self.task_scheduler.start_scheduler()
            
            # 8. 启动所有策略
            self.cta_engine.start_all_strategies()
            self.logger.info("CTA策略全部启动")
            
            self.running = True
            self.logger.info("系统启动完成")
            
            # 发射系统启动事件
            self.event_manager.emit_event(EventType.SYSTEM_START, "智能交易系统启动", "MainController")
            
            return True
        
        except Exception as e:
            self.logger.error(f"系统启动失败: {e}")
            return False
    
    def stop_system(self) -> bool:
        """停止系统"""
        try:
            if not self.running:
                self.logger.warning("系统未在运行")
                return True
            
            self.logger.info("开始停止系统...")
            
            # 1. 停止所有策略
            if self.cta_engine:
                self.cta_engine.stop_all_strategies()
                self.logger.info("CTA策略全部停止")
            
            # 2. 停止任务调度器
            if self.task_scheduler:
                self.task_scheduler.stop_scheduler()
            
            # 3. 停止系统监控
            if self.system_monitor:
                self.system_monitor.stop_monitoring()
            
            # 4. 停止事件管理器
            if self.event_manager:
                self.event_manager.stop()
            
            # 5. 断开账户连接
            if self.account_manager:
                self.account_manager.disconnect_all_accounts()
            
            # 6. 关闭主引擎
            if self.main_engine:
                self.main_engine.close()
            
            self.running = False
            self.logger.info("系统停止完成")
            
            return True
        
        except Exception as e:
            self.logger.error(f"系统停止失败: {e}")
            return False
    
    # 定时任务执行方法
    def _run_morning_screening(self) -> bool:
        """执行早盘筛选"""
        try:
            self.logger.info("开始执行早盘品种筛选...")
            
            # 运行期货筛选
            results = self.futures_scanner.run_scan()
            if results:
                self.logger.info(f"筛选完成，发现 {len(results)} 个符合条件的品种")
                
                # 发射筛选完成事件
                self.event_manager.emit_event(
                    EventType.TASK_COMPLETE, 
                    {"task": "morning_screening", "results": len(results)}, 
                    "MainController"
                )
                return True
            else:
                self.logger.warning("筛选未发现符合条件的品种")
                return False
        
        except Exception as e:
            self.logger.error(f"早盘筛选执行失败: {e}")
            self.event_manager.emit_event(
                EventType.TASK_FAILED, 
                {"task": "morning_screening", "error": str(e)}, 
                "MainController"
            )
            return False
    
    def _run_capital_allocation(self) -> bool:
        """执行资金分配"""
        try:
            self.logger.info("开始执行资金分配...")
            
            # 获取筛选结果
            screening_results = self.futures_scanner.get_latest_results()
            if not screening_results:
                self.logger.warning("无筛选结果，跳过资金分配")
                return False
            
            # 执行资金分配
            allocation_results = self.capital_allocator.allocate_capital(screening_results)
            if allocation_results:
                self.logger.info("资金分配完成")
                
                # 发射资金分配完成事件
                self.event_manager.emit_event(
                    EventType.TASK_COMPLETE, 
                    {"task": "capital_allocation", "allocations": len(allocation_results)}, 
                    "MainController"
                )
                return True
            else:
                self.logger.warning("资金分配失败")
                return False
        
        except Exception as e:
            self.logger.error(f"资金分配执行失败: {e}")
            self.event_manager.emit_event(
                EventType.TASK_FAILED, 
                {"task": "capital_allocation", "error": str(e)}, 
                "MainController"
            )
            return False
    
    def _run_position_adjustment(self) -> bool:
        """执行仓位调整"""
        try:
            self.logger.info("开始执行仓位调整...")
            
            # 检查风险限制
            risk_check = self.position_manager.check_all_risk_limits()
            if not risk_check:
                self.logger.warning("风险检查未通过，执行仓位调整")
                
                # 执行风险控制调整
                adjustment_results = self.position_manager.adjust_positions_for_risk()
                if adjustment_results:
                    self.logger.info("仓位风险调整完成")
                else:
                    self.logger.warning("仓位风险调整失败")
            
            # 发射仓位调整完成事件
            self.event_manager.emit_event(
                EventType.TASK_COMPLETE, 
                {"task": "position_adjustment"}, 
                "MainController"
            )
            return True
        
        except Exception as e:
            self.logger.error(f"仓位调整执行失败: {e}")
            self.event_manager.emit_event(
                EventType.TASK_FAILED, 
                {"task": "position_adjustment", "error": str(e)}, 
                "MainController"
            )
            return False
    
    def _run_contract_switching(self) -> bool:
        """执行合约切换检查"""
        try:
            self.logger.info("开始执行主力合约切换检查...")
            
            # 检查所有策略的合约切换需求
            switch_results = self.contract_switcher.check_all_strategies_for_switch()
            if switch_results:
                self.logger.info(f"发现 {len(switch_results)} 个策略需要切换合约")
                
                # 执行合约切换
                for strategy_name, switch_info in switch_results.items():
                    success = self.contract_switcher.execute_contract_switch(
                        strategy_name, 
                        switch_info['old_symbol'], 
                        switch_info['new_symbol']
                    )
                    if success:
                        self.logger.info(f"策略 {strategy_name} 合约切换成功")
                    else:
                        self.logger.error(f"策略 {strategy_name} 合约切换失败")
            
            # 发射合约切换完成事件
            self.event_manager.emit_event(
                EventType.TASK_COMPLETE, 
                {"task": "contract_switching", "switches": len(switch_results) if switch_results else 0}, 
                "MainController"
            )
            return True
        
        except Exception as e:
            self.logger.error(f"合约切换检查执行失败: {e}")
            self.event_manager.emit_event(
                EventType.TASK_FAILED, 
                {"task": "contract_switching", "error": str(e)}, 
                "MainController"
            )
            return False
    
    def _run_night_preparation(self) -> bool:
        """执行夜盘准备"""
        try:
            self.logger.info("开始执行夜盘交易准备...")
            
            # 检查系统状态
            system_status = self.system_monitor.get_current_system_metrics()
            if system_status:
                self.logger.info("系统状态检查完成")
            
            # 检查账户连接状态
            account_status = self.account_manager.get_all_account_status()
            connected_accounts = sum(1 for status in account_status.values() if status.get('connected', False))
            self.logger.info(f"账户连接状态: {connected_accounts}/{len(account_status)} 个账户已连接")
            
            # 发射夜盘准备完成事件
            self.event_manager.emit_event(
                EventType.TASK_COMPLETE, 
                {"task": "night_preparation", "connected_accounts": connected_accounts}, 
                "MainController"
            )
            return True
        
        except Exception as e:
            self.logger.error(f"夜盘准备执行失败: {e}")
            self.event_manager.emit_event(
                EventType.TASK_FAILED, 
                {"task": "night_preparation", "error": str(e)}, 
                "MainController"
            )
            return False
    
    # 事件处理方法
    def _handle_system_error(self, event):
        """处理系统错误事件"""
        self.logger.error(f"系统错误事件: {event.data}")
        
        # 可以在这里添加错误恢复逻辑
        # 例如重启失败的组件、发送紧急通知等
    
    def _handle_risk_warning(self, event):
        """处理风险警告事件"""
        self.logger.warning(f"风险警告事件: {event.data}")
        
        # 可以在这里添加风险控制逻辑
        # 例如降低仓位、停止交易等
    
    def display_system_status(self):
        """显示系统状态"""
        self.logger.info("=" * 100)
        self.logger.info("智能交易系统状态摘要")
        self.logger.info("=" * 100)
        
        # 系统基本状态
        self.logger.info(f"系统初始化: {'是' if self.initialized else '否'}")
        self.logger.info(f"系统运行: {'是' if self.running else '否'}")
        
        # 各组件状态
        if self.task_scheduler:
            self.task_scheduler.display_schedule_summary()
        
        if self.system_monitor:
            self.system_monitor.display_monitor_summary()
        
        if self.event_manager:
            self.event_manager.display_event_summary()
        
        if self.alert_manager:
            self.alert_manager.display_alert_summary()


class WholeMarketRecorder(RecorderEngine):
    """全市场数据记录器（继承自zdrun.py）"""
    
    def __init__(self, main_engine, event_engine):
        super().__init__(main_engine, event_engine)

    def subscribe_symbol(self):
        """订阅品种数据"""
        # tick数据
        tick = deepcopy(self.tick_recordings)
        bar = deepcopy(self.bar_recordings)

        for symbol in tick:
            self.remove_tick_recording(symbol)
            sleep(2)
            self.add_tick_recording(symbol)

        # bar数据
        for symbol in bar:
            self.remove_bar_recording(symbol)
            sleep(2)
            self.add_bar_recording(symbol)


def run_child():
    """子进程运行函数（基于zdrun.py）"""
    try:
        # 设置日志
        SETTINGS["log.file"] = True
        
        # 创建智能交易系统
        trading_system = IntelligentTradingSystem()
        
        # 初始化系统
        if not trading_system.initialize_system():
            print("系统初始化失败")
            return
        
        # 启动系统
        if not trading_system.start_system():
            print("系统启动失败")
            return
        
        # 检查交易时间
        trade_time = datetime.now().time()
        if (
            (trade_time >= time(15, 0) and trade_time <= time(20, 10))
            or (trade_time >= time(2, 30) and trade_time <= time(8, 35))
        ):
            # 非交易时间，停止策略
            if trading_system.cta_engine:
                for strategy_name in trading_system.cta_engine.strategies.keys():
                    strategy = trading_system.cta_engine.strategies[strategy_name]
                    if not strategy.trading:
                        return
                    else:
                        trading_system.cta_engine.stop_all_strategies()
                        trading_system.main_engine.write_log("CTA策略全部停止")
        
        # 主循环
        while True:
            sleep(1)
            
            # 检查系统状态
            if not trading_system.running:
                break
    
    except Exception as e:
        print(f"子进程运行异常: {e}")
    
    finally:
        # 清理资源
        if 'trading_system' in locals():
            trading_system.stop_system()


def run_parent():
    """父进程运行函数（基于zdrun.py）"""
    print("启动智能交易系统守护父进程")

    # 中国期货市场交易时间
    DAY_START = time(8, 57)
    DAY_END = time(15, 16)
    NIGHT_START = time(20, 57)
    NIGHT_END = time(2, 35)

    child_process = None

    while True:
        current_time = datetime.now().time()
        trading = False

        # 检查是否在交易时间内
        if (
            (current_time >= DAY_START and current_time <= DAY_END)
            or (current_time >= NIGHT_START)
            or (current_time <= NIGHT_END)
        ):
            trading = True

        # 在交易时间启动子进程
        if trading and child_process is None:
            print("启动智能交易系统子进程")
            child_process = multiprocessing.Process(target=run_child)
            child_process.start()
            print("智能交易系统子进程启动成功")

        # 非交易时间关闭子进程
        if not trading and child_process is not None:
            print("关闭智能交易系统子进程")
            child_process.terminate()
            child_process.join()
            child_process = None
            print("智能交易系统子进程关闭成功")

        sleep(5)


if __name__ == "__main__":
    # 设置多进程启动方法
    multiprocessing.set_start_method('spawn', force=True)
    
    # 启动父进程
    run_parent()
