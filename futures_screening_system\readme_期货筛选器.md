# 期货智能筛选与资金配置系统 - vnpy版本

## 📋 项目简介

专为期货突破策略设计的智能筛选与资金配置系统，集成vnpy数据库，通过多周期验证和实时波动分析，自动筛选出最适合交易的期货品种，并根据资金规模智能配置仓位。

## ✅ 最新更新 (2025-07-02)

### 🔥 激进模式升级
- **✅ 8万元激进配置**: 新增专门的激进交易配置
- **✅ 120%保证金使用**: 允许总保证金达到总资金120%
- **✅ 全仓入场策略**: 移除分批建仓，采用一次性全仓入场
- **✅ 黑名单机制**: 自动排除低流动性品种 ['JR', 'LR', 'RI', 'PM', 'WH', 'RS', 'CJ']
- **✅ 智能保证金调整**: 超限时自动降仓，优先保留高评分品种
- **✅ 策略执行说明**: 详细的固定+浮动止盈执行逻辑

### 🎯 基础功能
- **✅ 完成vnpy数据库集成**: 替换akshare数据源，直接使用vnpy数据库
- **✅ 实现数据重采样**: 使用1分钟数据合成5分钟、15分钟、30分钟、日线数据
- **✅ 集成contract_info.py**: 自动获取全市场期货合约信息和保证金数据
- **✅ 新增资金配置系统**: 根据资金规模智能配置品种和仓位
- **✅ 支持策略参数**: 2倍开仓手数，固定止盈一半+浮动止盈一半
- **✅ 独立文件夹部署**: 所有文件整理到独立文件夹，便于部署
- **✅ 功能测试通过**: 所有核心功能验证正常

### 🎯 核心特性

#### 📊 品种筛选系统
- **vnpy数据库集成**: 直接使用vnpy数据库，支持全市场期货品种
- **智能数据重采样**: 1分钟基础数据自动合成多周期K线
- **多周期协同验证**: 日线确定趋势方向 + 分钟线捕捉入场时机
- **动态波动检测**: 实时监控ATR扩张、布林带扩张、波动率指数
- **智能突破识别**: Donchian通道突破、MACD金叉死叉、KDJ超买超卖
- **综合评分系统**: 多维度评分，自动排序推荐
- **实时缓存机制**: 提高数据获取效率，减少重复计算

#### 💰 资金配置系统
- **智能资金配置**: 根据总资金自动配置品种和仓位
- **激进模式**: 8万元配置支持120%保证金使用率
- **黑名单过滤**: 自动排除低流动性品种，提高交易效率
- **全仓入场**: 移除分批建仓，采用一次性全仓入场策略
- **保证金调整**: 超限时智能降仓，优先保留高评分品种
- **风险等级管理**: 不同品种采用不同风险系数
- **保证金计算**: 实时获取保证金数据，精确计算所需资金
- **策略参数支持**: 支持2倍开仓、固定+浮动止盈策略
- **预设配置方案**: 5万、8万(激进)、10万、20万、50万、100万元等预设方案
- **风险控制**: 多层风险控制，确保资金安全

### 📊 数据来源

- **数据库**: vnpy数据库 (database_manager)
- **基础数据**: 1分钟K线数据
- **合成数据**: 5分钟、15分钟、30分钟、日线 (由1分钟数据重采样生成)
- **合约信息**: contract_info.py (自动获取全市场期货合约)
- **支持交易所**: SHFE, DCE, CZCE, CFFEX, INE, GFEX

## 🚀 快速开始

### 环境要求

```bash
Python >= 3.7
pandas >= 1.3.0
numpy >= 1.20.0
talib >= 0.4.0
vnpy >= 2.0.0
```

### 安装依赖

```bash
pip install pandas numpy talib
# vnpy需要单独安装，请参考vnpy官方文档
```

### 前置条件

1. **vnpy环境**: 确保vnpy已正确安装并配置
2. **数据库**: vnpy数据库中需要有期货1分钟K线数据
3. **合约信息**: 需要contract_info.py文件或相应的Excel文件

### 基本使用

#### 1. 品种筛选
```bash
# 运行完整品种筛选
python futures_minute_scanner.py
```

#### 2. 资金配置
```bash
# 运行资金配置程序（包含品种筛选）
python capital_allocation.py

# 选择配置时输入对应数字:
# 1. 5万元: 保守配置，70%保证金使用
# 2. 8万元: 激进配置，120%保证金使用 ⭐推荐
# 3. 10万元: 标准配置，80%保证金使用
# 4. 20万元: 进阶配置，80%保证金使用
# 5. 50万元: 专业配置，85%保证金使用
# 6. 100万元: 机构配置，85%保证金使用
```

#### 3. 激进模式使用 (8万元配置)
```bash
# 启动资金配置程序
python capital_allocation.py

# 选择 "2" (8万元配置)
请选择配置 (直接回车使用默认10万元配置): 2

# 系统将自动:
# 1. 应用黑名单过滤 (排除7个低流动性品种)
# 2. 启用120%保证金使用率
# 3. 配置全仓入场策略
# 4. 显示固定+浮动止盈执行逻辑
```



## 📊 筛选逻辑

### 1. 趋势过滤器（必须同时满足）

| 指标 | 周期 | 条件 | 说明 |
|------|------|------|------|
| ADX | 日线 | > 25 | 趋势强度确认 |
| EMA排列 | 30分钟 | EMA12 > EMA36 > EMA72 | 多头排列 |
| DI方向 | 30分钟 | +DI > -DI (多头) 或 +DI < -DI (空头) | 方向确认 |

### 2. 波动扩张检测（满足任一）

| 指标 | 周期 | 条件 | 权重 |
|------|------|------|------|
| ATR扩张 | 15分钟 | 当前ATR > 1.8倍过去10根均值 | 40% |
| 布林带扩张 | 30分钟 | 带宽变化率 > 25% | 30% |
| 波动率指数 | 5分钟 | 当前波动率 > 历史平均 | 30% |

### 3. 突破信号确认（满足任一）

| 信号类型 | 周期 | 条件 | 评分 |
|----------|------|------|------|
| Donchian突破 | 5分钟 | 收盘价突破20周期通道 | 40分 |
| MACD信号 | 15分钟 | 金叉/死叉确认 | 30分 |
| KDJ信号 | 15分钟 | 超卖反弹/超买回落 | 20分 |
| 实体强度 | 5分钟 | 实体长度 > 2倍ATR | 10分 |

### 4. 排除条件

- 当日振幅 < 1.5倍15分钟ATR
- 30分钟ADX < 20
- 主力合约持仓量 < 10万手

## 📈 评分体系

### 综合评分计算

```
综合评分 = 趋势评分 × 30% + 波动率评分 × 40% + 突破信号评分 × 30%
```

### 评级标准

| 评分范围 | 状态 | 建议操作 |
|----------|------|----------|
| 80-100 | 重点关注 | 优先考虑入场 |
| 60-79 | 准备入场 | 密切监控，等待时机 |
| 40-59 | 观察 | 继续观察，暂不操作 |
| 0-39 | 排除 | 不建议交易 |

## 🔧 配置参数

### 主要参数说明

```python
# 趋势过滤器参数
'adx_threshold': 25,           # ADX趋势强度阈值
'ema_periods': [12, 36, 72],   # EMA周期组合
'di_period': 14,               # DI计算周期

# 波动扩张检测参数
'atr_multiplier': 1.8,         # ATR扩张倍数
'bb_expansion_threshold': 25,   # 布林带扩张阈值(%)
'vix_threshold': 20,           # 波动率指数阈值

# 突破信号确认参数
'donchian_period': 20,         # Donchian通道周期
'macd_fast': 12,               # MACD快线
'macd_slow': 26,               # MACD慢线
'macd_signal': 9,              # MACD信号线
```

### 自定义配置

修改 `system_config.py` 文件中的参数：

```python
from system_config import update_scanner_config, update_capital_config

# 修改筛选器参数
update_scanner_config('trend_filters', 'adx_threshold', 30)
update_scanner_config('volatility_filters', 'atr_multiplier', 2.0)

# 修改资金配置参数
update_capital_config('max_symbols', 10)
update_capital_config('max_risk_per_symbol', 0.025)
```

### 统一配置文件说明

`system_config.py` 包含所有系统配置：

```python
# 筛选器配置
SCANNER_CONFIG = {
    'trend_filters': {...},      # 趋势过滤器参数
    'volatility_filters': {...}, # 波动率检测参数
    'breakout_signals': {...},   # 突破信号参数
    'scoring_weights': {...},    # 评分权重配置
    # ... 更多配置
}

# 资金配置
CAPITAL_CONFIG = {...}           # 基础资金配置
CAPITAL_PRESETS = {...}          # 预设配置方案

# 黑名单配置
BLACKLIST = {
    'symbols': ['JR', 'LR', 'RI', 'PM', 'WH', 'RS'],
    'reasons': {...}
}
```

## 📁 文件结构

```
futures_screening_system/
├── futures_minute_scanner.py    # 主筛选器程序
├── capital_allocation.py        # 资金配置程序
├── system_config.py            # 统一配置文件 (筛选器+资金配置)
├── contract_info.py            # 合约信息管理
├── 期货全品种手续费保证金.xls   # 合约保证金数据
├── readme_期货筛选器.md        # 使用说明
└── 期货品种筛选结果_*.csv     # 输出结果文件
```

### 核心文件说明

| 文件名 | 功能描述 | 主要作用 |
|--------|----------|----------|
| `futures_minute_scanner.py` | 品种筛选引擎 | 执行品种筛选逻辑，输出评分结果 |
| `capital_allocation.py` | 资金配置引擎 | 整合筛选结果，进行资金配置 |
| `system_config.py` | 统一配置管理 | 管理筛选器参数、资金配置、黑名单等所有配置 |
| `contract_info.py` | 合约信息 | 获取合约信息和保证金数据 |

## 📊 输出结果

### 1. 品种筛选结果

#### 控制台输出
```
期货品种筛选结果汇总
================================================================================
总品种数: 80
合格品种: 6
重点关注: 2
准备入场: 4

品种         状态       评分    排除原因
CY888.CZCE   重点关注   57.7
ss888.SHFE   重点关注   50.2
pb888.SHFE   准备入场   46.6
jd888.DCE    准备入场   42.9
...
```

#### CSV文件输出
自动保存为 `期货品种筛选结果_YYYYMMDD_HHMMSS.csv`，包含完整的筛选结果和评分详情。

### 2. 激进模式配置 (8万元)

#### 🔥 激进模式特性
```
⚡ 激进模式执行策略:
1. 全仓入场: 一次性建立全部仓位
2. 固定止盈: 盈利达2倍ATR时，平仓50%仓位
3. 浮动止盈: 剩余仓位使用1.5倍ATR移动止损
4. 保证金释放: 固定止盈后保证金减少40-50%
5. 隔夜控制: 波动率>70品种在14:55前平仓

💰 保证金检查:
   总保证金需求: 95,200元
   保证金上限: 96,000元 (120%)
   保证金占用率: 119.0%
   ✅ 保证金使用正常

⚠️ 黑名单品种排除:
  - JR888.CZCE (评分45.2): 粳稻流动性差
  - LR888.CZCE (评分38.9): 晚籼稻交易量小
  - RI888.CZCE (评分41.3): 早籼稻波动不足
```

#### 🎯 激进模式优势
- **资金利用率高**: 120%保证金使用，最大化资金效率
- **执行效率高**: 全仓入场，无需分批建仓
- **风险控制精准**: 固定+浮动止盈，风险收益比优化
- **品种质量高**: 黑名单过滤，专注高流动性品种

### 3. 资金配置结果

#### 控制台输出
```
📊 资金配置报告
================================================================================
总资金: 100,000元
需要保证金: 19,885元 (19.9%)
预估总风险: 1,988元 (2.0%)
剩余资金: 80,115元

📋 详细配置方案:
------------------------------------------------------------------------------------------------------------------------
品种           评分     状态       建议手数     开仓手数     保证金          预估风险         风险比例
------------------------------------------------------------------------------------------------------------------------
CY888.CZCE   57.7   重点关注     1        2        19,885       1,988        2.0%
------------------------------------------------------------------------------------------------------------------------
合计                           1        2        19,885       1,988        2.0%

⚠️  风险提示:
1. 策略开仓为2倍手数，固定止盈一半，浮动止盈一半
2. 建议手数为策略参数lots的建议值
3. 开仓手数为实际开仓时的手数（建议手数×2）
```

## ⚙️ 高级功能

### 1. 资金配置预设方案

系统提供多种预设配置方案，适合不同资金规模：

| 配置方案 | 总资金 | 最大品种数 | 单品种最大风险 | 适用场景 |
|----------|--------|------------|----------------|----------|
| 5万元 | 50,000元 | 3个 | 3% | 小资金起步 |
| 10万元 | 100,000元 | 5个 | 2% | 中等资金 |
| 20万元 | 200,000元 | 8个 | 1.5% | 较大资金 |
| 50万元 | 500,000元 | 12个 | 1% | 大资金 |
| 100万元 | 1,000,000元 | 15个 | 0.8% | 超大资金 |

### 2. 风险等级管理

系统根据品种特性设置不同风险等级：

```python
# 低风险品种（农产品、基础金属）
'low_risk': {
    'symbols': ['C', 'CS', 'A', 'B', 'M', 'Y', 'P', 'CU', 'AL'],
    'risk_multiplier': 0.8
}

# 中等风险品种（化工、能源）
'medium_risk': {
    'symbols': ['RU', 'TA', 'MA', 'PP', 'V', 'L'],
    'risk_multiplier': 1.0
}

# 高风险品种（贵金属、股指）
'high_risk': {
    'symbols': ['AU', 'AG', 'SC', 'LU', 'IF', 'IH'],
    'risk_multiplier': 1.5
}
```

### 3. 自定义配置

```python
# 修改资金配置
from system_config import get_capital_config, update_capital_config

# 自定义配置
update_capital_config('total_capital', 150000)      # 总资金15万
update_capital_config('max_symbols', 6)             # 最多6个品种
update_capital_config('max_risk_per_symbol', 0.015) # 单品种最大风险1.5%
update_capital_config('reserve_ratio', 0.2)         # 储备资金比例20%

# 或者获取配置进行修改
custom_config = get_capital_config('10万元')
custom_config['total_capital'] = 150000
```

## 📅 使用建议

### 运行频率

- **每30分钟运行一次**：在30分钟K线收盘时
- **每日重点扫描时间**：
  - 08:45 (开盘前)
  - 10:30 (上午中段)
  - 13:30 (下午开盘)
  - 14:30 (下午中段)
  - 21:30 (夜盘开盘后)

### 交易建议

#### 1. 品种选择策略
- **重点关注品种**：优先考虑，但仍需结合实际市场情况
- **准备入场品种**：密切监控，等待更好的入场时机
- **观察品种**：继续观察，暂不操作
- **排除品种**：不建议交易

#### 2. 资金管理策略
- **分散投资**：不要将资金集中在单一品种
- **分批建仓**：采用2倍开仓策略，分批建立仓位
- **止盈策略**：固定止盈一半仓位，浮动止盈另一半
- **风险控制**：单品种风险不超过总资金的2-3%

#### 3. 实盘操作建议
- **模拟测试**：先在模拟环境中测试策略效果
- **小资金试验**：实盘初期使用小资金验证系统
- **逐步放大**：确认系统稳定后逐步增加资金规模
- **定期评估**：定期评估系统表现，调整参数

### 注意事项

⚠️ **重要提醒**：
- 本系统仅供参考，不构成投资建议
- 实际交易前请结合基本面分析和市场情况
- 严格执行风险管理和资金管理策略
- 建议在模拟环境中充分测试后再用于实盘
- 期货交易存在较大风险，请谨慎操作

## 🔍 故障排除

### 常见问题

1. **vnpy数据库连接失败**
   ```
   解决方案：
   - 检查vnpy是否正确安装和配置
   - 确认数据库中有1分钟K线数据
   - 检查数据库连接配置
   ```

2. **合约信息获取失败**
   ```
   解决方案：
   - 确认期货全品种手续费保证金.xls文件存在
   - 检查contract_info.py中的文件路径
   - 验证Excel文件格式是否正确
   ```

3. **TA-Lib计算错误**
   ```
   解决方案：
   - 确保数据长度足够（至少100根K线）
   - 检查数据质量，排除异常值
   - 确认TA-Lib正确安装
   ```

4. **资金配置计算错误**
   ```
   解决方案：
   - 检查保证金数据是否完整
   - 确认配置参数是否合理
   - 验证品种代码格式是否正确
   ```

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 测试单个模块
from contract_info import ContractInfo
contract_info = ContractInfo("期货全品种手续费保证金.xls")
print(contract_info.get_all_symbols())

# 测试资金配置
from capital_allocation import CapitalAllocation
from system_config import get_capital_config
allocator = CapitalAllocation("10万元")
print(allocator.config)

# 测试统一配置
config = get_capital_config("8万元")
print(f"激进模式: {config.get('aggressive_mode', False)}")
print(f"保证金上限: {config.get('total_margin_limit', 1.0)*100}%")
```

### 功能验证

可以通过运行各个模块来验证功能：

```bash
# 验证统一配置文件
python system_config.py

# 验证品种筛选功能
python futures_minute_scanner.py

# 验证资金配置功能
python capital_allocation.py
```

## � 系统架构

### 模块关系图

```
┌─────────────────────────────────────────────────────────────┐
│                    期货智能筛选与资金配置系统                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  品种筛选模块    │    │  资金配置模块    │                │
│  │                │    │                │                │
│  │ • 技术指标计算   │    │ • 保证金计算     │                │
│  │ • 多周期验证     │◄──►│ • 风险评估       │                │
│  │ • 综合评分       │    │ • 仓位分配       │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                        │
│           ▼                       ▼                        │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  数据管理模块    │    │  配置管理模块    │                │
│  │                │    │                │                │
│  │ • vnpy数据库     │    │ • 筛选参数       │                │
│  │ • 数据重采样     │    │ • 资金配置       │                │
│  │ • 合约信息       │    │ • 风险参数       │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

### 数据流程

1. **数据获取**: vnpy数据库 → 1分钟K线数据
2. **数据处理**: 重采样 → 多周期K线数据
3. **技术分析**: 计算技术指标 → 生成交易信号
4. **品种筛选**: 综合评分 → 筛选合格品种
5. **资金配置**: 风险评估 → 分配资金和仓位
6. **结果输出**: 生成报告 → 交易建议

## � 技术支持

### 系统要求
- **操作系统**: Windows 10+, Linux, macOS
- **Python版本**: 3.7+
- **vnpy版本**: 2.0+
- **内存要求**: 建议8GB以上
- **硬盘空间**: 建议10GB以上（用于数据存储）

### 版本信息
- **当前版本**: v2.2.0
- **更新时间**: 2025-07-02
- **兼容性**: Python 3.7+, VN.PY 2.0+
- **维护状态**: 积极维护中

### 更新日志
- **v2.2.0** (2025-07-02): 📋配置文件统一管理
  - 合并scanner_config.py和capital_config.py为system_config.py
  - 统一管理所有系统参数，便于维护和修改
  - 优化配置文件结构，提高可读性
  - 更新所有模块以使用统一配置文件
- **v2.1.0** (2025-07-02): 🔥激进模式升级
  - 新增8万元激进配置 (120%保证金使用率)
  - 实现黑名单机制 (自动排除6个低流动性品种)
  - 移除分批建仓，采用全仓入场策略
  - 添加智能保证金调整功能
  - 完善固定+浮动止盈执行说明
- **v2.0.0** (2025-07-02): 新增资金配置系统，独立文件夹部署
- **v1.5.0** (2025-06-15): 完成vnpy数据库集成
- **v1.0.0** (2025-01-01): 初始版本发布

---

**免责声明**: 本系统仅供学习和研究使用，不构成投资建议。期货交易存在较大风险，请谨慎操作。
