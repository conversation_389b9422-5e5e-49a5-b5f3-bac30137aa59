void processFrontConnected(Task *task);

void processFrontDisconnected(Task *task);

void processHeartBeatWarning(Task *task);

void processPackageStart(Task *task);

void processPackageEnd(Task *task);

void processRspSubscribeTopic(Task *task);

void processRspUserLogin(Task *task);

void processRspUserLogout(Task *task);

void processRspUserPasswordUpdate(Task *task);

void processRspStockInsert(Task *task);

void processRspStockCancel(Task *task);

void processRspOptionsInsert(Task *task);

void processRspOptionsCancel(Task *task);

void processRspQuoteInsert(Task *task);

void processRspForQuote(Task *task);

void processRspQuoteCancel(Task *task);

void processRspStockLock(Task *task);

void processRspExercise(Task *task);

void processRspExerciseCancel(Task *task);

void processRspQryPartAccount(Task *task);

void processRspQryStockOrder(Task *task);

void processRspQryOptionsOrder(Task *task);

void processRspQryQuoteOrder(Task *task);

void processRspQryStockTrade(Task *task);

void processRspQryOptionsTrade(Task *task);

void processRspQryPosition(Task *task);

void processRspQryTopic(Task *task);

void processRspQryStock(Task *task);

void processRspQryOptions(Task *task);

void processRtnOptionsOrder(Task *task);

void processRtnStockOrder(Task *task);

void processRtnQuoteOrder(Task *task);

void processRtnOptionsTrade(Task *task);

void processRtnStockTrade(Task *task);

void processRtnExercise(Task *task);

void processRspQryRate(Task *task);

void processRspQryClient(Task *task);

void processRspQryClientMargin(Task *task);

void processRspQryExercise(Task *task);

void processRtnWithdrawDeposit(Task *task);

void processRspMarginCombAction(Task *task);

void processRtnMarginCombAction(Task *task);

void processRspQrySseCombPosition(Task *task);

void processRspCombExercise(Task *task);

