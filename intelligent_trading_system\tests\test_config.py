"""
配置模块测试
测试系统配置、交易配置和风险配置的加载和验证
"""

import unittest
import os
import sys
from dataclasses import asdict

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from config import get_system_config, get_trading_config, get_risk_config
from config.system_config import SystemConfig, DatabaseConfig, LoggingConfig, SchedulerConfig
from config.trading_config import TradingConfig, ScreeningConfig, StrategyConfig
from config.risk_config import RiskConfig, CapitalAllocationConfig


class TestConfigModule(unittest.TestCase):
    """配置模块测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.system_config = None
        self.trading_config = None
        self.risk_config = None
    
    def test_system_config_loading(self):
        """测试系统配置加载"""
        try:
            self.system_config = get_system_config()
            self.assertIsInstance(self.system_config, SystemConfig)
            print("✓ 系统配置加载成功")
        except Exception as e:
            self.fail(f"系统配置加载失败: {e}")
    
    def test_system_config_structure(self):
        """测试系统配置结构"""
        config = get_system_config()
        
        # 检查数据库配置
        self.assertIsInstance(config.database, DatabaseConfig)
        self.assertTrue(hasattr(config.database, 'path'))
        self.assertTrue(hasattr(config.database, 'driver'))
        
        # 检查日志配置
        self.assertIsInstance(config.logging, LoggingConfig)
        self.assertTrue(hasattr(config.logging, 'level'))
        self.assertTrue(hasattr(config.logging, 'file_enabled'))
        
        # 检查调度器配置
        self.assertIsInstance(config.scheduler, SchedulerConfig)
        self.assertTrue(hasattr(config.scheduler, 'timezone'))
        
        print("✓ 系统配置结构验证通过")
    
    def test_trading_config_loading(self):
        """测试交易配置加载"""
        try:
            self.trading_config = get_trading_config()
            self.assertIsInstance(self.trading_config, TradingConfig)
            print("✓ 交易配置加载成功")
        except Exception as e:
            self.fail(f"交易配置加载失败: {e}")
    
    def test_trading_config_structure(self):
        """测试交易配置结构"""
        config = get_trading_config()
        
        # 检查基本配置
        self.assertTrue(hasattr(config, 'default_strategy'))
        self.assertTrue(hasattr(config, 'blacklist_symbols'))
        self.assertIsInstance(config.blacklist_symbols, list)
        
        # 检查筛选配置
        self.assertIsInstance(config.screening, ScreeningConfig)
        self.assertTrue(hasattr(config.screening, 'trend_threshold'))
        self.assertTrue(hasattr(config.screening, 'volatility_min'))
        self.assertTrue(hasattr(config.screening, 'volatility_max'))
        
        # 检查策略配置
        self.assertIsInstance(config.strategy, StrategyConfig)
        self.assertTrue(hasattr(config.strategy, 'default_size'))
        
        print("✓ 交易配置结构验证通过")
    
    def test_risk_config_loading(self):
        """测试风险配置加载"""
        try:
            self.risk_config = get_risk_config()
            self.assertIsInstance(self.risk_config, RiskConfig)
            print("✓ 风险配置加载成功")
        except Exception as e:
            self.fail(f"风险配置加载失败: {e}")
    
    def test_risk_config_structure(self):
        """测试风险配置结构"""
        config = get_risk_config()
        
        # 检查基本风险配置
        self.assertTrue(hasattr(config, 'max_total_position'))
        self.assertTrue(hasattr(config, 'max_single_position'))
        self.assertTrue(hasattr(config, 'max_drawdown_limit'))
        
        # 检查资金分配配置
        self.assertIsInstance(config.capital_allocation, CapitalAllocationConfig)
        self.assertTrue(hasattr(config.capital_allocation, 'conservative'))
        self.assertTrue(hasattr(config.capital_allocation, 'moderate'))
        self.assertTrue(hasattr(config.capital_allocation, 'aggressive'))
        
        print("✓ 风险配置结构验证通过")
    
    def test_config_values_validation(self):
        """测试配置值验证"""
        system_config = get_system_config()
        trading_config = get_trading_config()
        risk_config = get_risk_config()
        
        # 验证系统配置值
        self.assertIn(system_config.logging.level, ['DEBUG', 'INFO', 'WARNING', 'ERROR'])
        self.assertIsInstance(system_config.logging.file_enabled, bool)
        
        # 验证交易配置值
        self.assertGreater(trading_config.screening.trend_threshold, 0)
        self.assertLess(trading_config.screening.trend_threshold, 1)
        self.assertGreater(trading_config.screening.volatility_min, 0)
        self.assertGreater(trading_config.screening.volatility_max, trading_config.screening.volatility_min)
        
        # 验证风险配置值
        self.assertGreater(risk_config.max_total_position, 0)
        self.assertLessEqual(risk_config.max_total_position, 1)
        self.assertGreater(risk_config.max_single_position, 0)
        self.assertLessEqual(risk_config.max_single_position, risk_config.max_total_position)
        
        print("✓ 配置值验证通过")
    
    def test_config_serialization(self):
        """测试配置序列化"""
        system_config = get_system_config()
        trading_config = get_trading_config()
        risk_config = get_risk_config()
        
        try:
            # 测试转换为字典
            system_dict = asdict(system_config)
            trading_dict = asdict(trading_config)
            risk_dict = asdict(risk_config)
            
            self.assertIsInstance(system_dict, dict)
            self.assertIsInstance(trading_dict, dict)
            self.assertIsInstance(risk_dict, dict)
            
            print("✓ 配置序列化测试通过")
        except Exception as e:
            self.fail(f"配置序列化失败: {e}")
    
    def test_config_modification(self):
        """测试配置修改"""
        # 获取原始配置
        original_config = get_system_config()
        
        # 修改配置
        modified_config = get_system_config()
        modified_config.logging.level = "DEBUG"
        
        # 验证修改
        self.assertEqual(modified_config.logging.level, "DEBUG")
        self.assertNotEqual(original_config.logging.level, modified_config.logging.level)
        
        print("✓ 配置修改测试通过")
    
    def test_config_defaults(self):
        """测试配置默认值"""
        system_config = get_system_config()
        trading_config = get_trading_config()
        risk_config = get_risk_config()
        
        # 检查系统配置默认值
        self.assertIsNotNone(system_config.database.path)
        self.assertIsNotNone(system_config.logging.level)
        
        # 检查交易配置默认值
        self.assertIsNotNone(trading_config.default_strategy)
        self.assertIsInstance(trading_config.blacklist_symbols, list)
        
        # 检查风险配置默认值
        self.assertGreater(risk_config.max_total_position, 0)
        self.assertGreater(risk_config.max_single_position, 0)
        
        print("✓ 配置默认值测试通过")
    
    def test_config_consistency(self):
        """测试配置一致性"""
        trading_config = get_trading_config()
        risk_config = get_risk_config()
        
        # 检查风险配置一致性
        self.assertLessEqual(risk_config.max_single_position, risk_config.max_total_position)
        
        # 检查筛选配置一致性
        self.assertLess(trading_config.screening.volatility_min, trading_config.screening.volatility_max)
        
        # 检查资金分配配置一致性
        conservative = risk_config.capital_allocation.conservative
        moderate = risk_config.capital_allocation.moderate
        aggressive = risk_config.capital_allocation.aggressive
        
        self.assertLessEqual(conservative.risk_ratio, moderate.risk_ratio)
        self.assertLessEqual(moderate.risk_ratio, aggressive.risk_ratio)
        
        print("✓ 配置一致性测试通过")


if __name__ == "__main__":
    unittest.main()
