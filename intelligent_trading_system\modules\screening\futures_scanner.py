"""
期货品种筛选器
集成现有的期货筛选系统，提供品种评测和筛选功能
"""

import sys
import os
import pandas as pd
import numpy as np
import talib
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Optional, Any
import time

# 添加vnpy路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from vnpy.trader.database import get_database
from vnpy.trader.constant import Exchange, Interval
from vnpy.trader.object import BarData

from ...config import get_trading_config
from ...utils.logger import get_logger
from ...utils.helpers import safe_float, format_datetime


class FuturesScanner:
    """期货品种筛选器"""
    
    def __init__(self, contract_info_path: str = None):
        """初始化筛选器"""
        self.logger = get_logger("FuturesScanner")
        self.trading_config = get_trading_config()
        
        # 数据库连接
        self.database = get_database()
        
        # 获取品种列表
        self.symbols = self._get_futures_symbols()
        
        # 筛选配置
        self.screening_config = self.trading_config.screening_config
        
        # 缓存机制
        self.data_cache = {}
        self.cache_timeout = 300  # 5分钟缓存超时
        
        self.logger.info(f"期货筛选器初始化完成，共 {len(self.symbols)} 个品种")
    
    def _get_futures_symbols(self) -> List[str]:
        """获取期货品种列表"""
        symbols = []
        
        # 从交易配置获取启用的品种
        enabled_symbols = self.trading_config.get_enabled_symbols()
        if enabled_symbols:
            symbols.extend(enabled_symbols)
        else:
            # 如果没有配置，使用默认品种列表
            default_symbols = [
                "RB888.SHFE", "HC888.SHFE", "I888.DCE", "J888.DCE", "JM888.DCE",
                "CU888.SHFE", "AL888.SHFE", "ZN888.SHFE", "PB888.SHFE", "NI888.SHFE",
                "AU888.SHFE", "AG888.SHFE", "RU888.SHFE", "BU888.SHFE", "FU888.SHFE",
                "C888.DCE", "CS888.DCE", "A888.DCE", "M888.DCE", "Y888.DCE",
                "CF888.CZCE", "SR888.CZCE", "TA888.CZCE", "MA888.CZCE", "RM888.CZCE"
            ]
            symbols.extend(default_symbols)
        
        # 过滤黑名单品种
        filtered_symbols = []
        for symbol in symbols:
            if not self.trading_config.is_symbol_blacklisted(symbol):
                filtered_symbols.append(symbol)
            else:
                self.logger.debug(f"品种 {symbol} 在黑名单中，已跳过")
        
        return filtered_symbols
    
    def _get_cache_key(self, symbol: str, period: str) -> str:
        """生成缓存键"""
        return f"{symbol}_{period}"
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.data_cache:
            return False
        
        cache_time = self.data_cache[cache_key].get('timestamp', 0)
        return (time.time() - cache_time) < self.cache_timeout
    
    def fetch_data(self, symbol: str, period: str = '30') -> Optional[pd.DataFrame]:
        """获取期货K线数据"""
        cache_key = self._get_cache_key(symbol, period)
        
        # 检查缓存
        if self._is_cache_valid(cache_key):
            return self.data_cache[cache_key]['data']
        
        try:
            # 从vnpy数据库获取1分钟数据
            df_1min = self._get_1min_data_from_vnpy(symbol)
            
            if df_1min is None or df_1min.empty:
                self.logger.warning(f"未找到 {symbol} 的1分钟数据")
                return None
            
            # 根据需要的周期合成数据
            if period == '1':
                df = df_1min
            elif period == 'D':
                df = self._resample_to_daily(df_1min)
            else:
                # 合成其他分钟周期
                df = self._resample_to_minutes(df_1min, int(period))
            
            if df is not None and not df.empty:
                # 标准化列名
                df = self._standardize_columns(df)
                
                # 缓存数据
                self.data_cache[cache_key] = {
                    'data': df,
                    'timestamp': time.time()
                }
                
                return df
        
        except Exception as e:
            self.logger.error(f"获取 {symbol} {period}分钟数据失败: {e}")
        
        return None
    
    def _get_1min_data_from_vnpy(self, symbol: str) -> Optional[pd.DataFrame]:
        """从vnpy数据库获取1分钟数据"""
        try:
            # 解析品种代码
            if '.' in symbol:
                symbol_part, exchange_str = symbol.split('.')
            else:
                symbol_part = symbol
                exchange_str = "SHFE"  # 默认交易所
            
            # 映射交易所
            exchange_map = {
                'SHFE': Exchange.SHFE,
                'DCE': Exchange.DCE,
                'CZCE': Exchange.CZCE,
                'CFFEX': Exchange.CFFEX,
                'INE': Exchange.INE
            }
            exchange = exchange_map.get(exchange_str, Exchange.SHFE)
            
            # 设置查询参数
            end_time = datetime.now()
            start_time = end_time - timedelta(days=50)  # 获取最近50天数据
            
            # 查询1分钟数据
            bars = self.database.load_bar_data(
                symbol=symbol_part,
                exchange=exchange,
                interval=Interval.MINUTE,
                start=start_time,
                end=end_time
            )
            
            if not bars:
                return None
            
            # 转换为DataFrame
            data = []
            for bar in bars:
                data.append({
                    'datetime': bar.datetime,
                    'open': bar.open_price,
                    'high': bar.high_price,
                    'low': bar.low_price,
                    'close': bar.close_price,
                    'volume': bar.volume,
                    'turnover': getattr(bar, 'turnover', 0)
                })
            
            df = pd.DataFrame(data)
            if not df.empty:
                df['datetime'] = pd.to_datetime(df['datetime'])
                df.set_index('datetime', inplace=True)
                df.sort_index(inplace=True)
            
            self.logger.debug(f"成功获取 {symbol} 的 {len(df)} 根1分钟K线")
            return df
        
        except Exception as e:
            self.logger.error(f"从vnpy数据库获取 {symbol} 数据失败: {e}")
            return None
    
    def _resample_to_minutes(self, df: pd.DataFrame, minutes: int) -> pd.DataFrame:
        """重采样到指定分钟周期"""
        if df.empty:
            return df
        
        # 重采样规则
        rule = f"{minutes}T"
        
        # 重采样
        resampled = df.resample(rule).agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum',
            'turnover': 'sum'
        }).dropna()
        
        return resampled
    
    def _resample_to_daily(self, df: pd.DataFrame) -> pd.DataFrame:
        """重采样到日线"""
        if df.empty:
            return df
        
        # 重采样到日线
        daily = df.resample('D').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum',
            'turnover': 'sum'
        }).dropna()
        
        return daily
    
    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化列名"""
        if df.empty:
            return df
        
        # 确保所有必要的列都存在
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in df.columns:
                df[col] = 0
        
        # 确保数据类型正确
        for col in required_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
        
        return df
    
    def calculate_trend_indicators(self, df_daily: pd.DataFrame, df_30m: pd.DataFrame) -> Dict:
        """计算趋势指标"""
        result = {
            'daily_adx': 0,
            'ema_alignment': False,
            'di_direction': 0,  # 1=多头, -1=空头, 0=无方向
            'trend_score': 0
        }
        
        try:
            # 日线ADX计算
            if len(df_daily) >= 20:
                high_daily = df_daily['high'].values
                low_daily = df_daily['low'].values
                close_daily = df_daily['close'].values
                
                adx = talib.ADX(high_daily, low_daily, close_daily, timeperiod=14)
                if not np.isnan(adx[-1]):
                    result['daily_adx'] = adx[-1]
            
            # 30分钟EMA排列
            if len(df_30m) >= 72:
                close_30m = df_30m['close'].values
                ema12 = talib.EMA(close_30m, timeperiod=12)
                ema36 = talib.EMA(close_30m, timeperiod=36)
                ema72 = talib.EMA(close_30m, timeperiod=72)
                
                if not any(np.isnan([ema12[-1], ema36[-1], ema72[-1]])):
                    result['ema_alignment'] = ema12[-1] > ema36[-1] > ema72[-1]
            
            # 30分钟DI方向判断
            if len(df_30m) >= 20:
                high_30m = df_30m['high'].values
                low_30m = df_30m['low'].values
                close_30m = df_30m['close'].values
                
                plus_di = talib.PLUS_DI(high_30m, low_30m, close_30m, timeperiod=14)
                minus_di = talib.MINUS_DI(high_30m, low_30m, close_30m, timeperiod=14)
                
                if not (np.isnan(plus_di[-1]) or np.isnan(minus_di[-1])):
                    if plus_di[-1] > minus_di[-1]:
                        result['di_direction'] = 1  # 多头
                    elif plus_di[-1] < minus_di[-1]:
                        result['di_direction'] = -1  # 空头
            
            # 计算趋势综合评分
            score = 0
            if result['daily_adx'] > 20:  # ADX阈值
                score += 30
            if result['ema_alignment']:
                score += 40
            if abs(result['di_direction']) == 1:
                score += 30
            
            result['trend_score'] = score
        
        except Exception as e:
            self.logger.error(f"计算趋势指标失败: {e}")

        return result

    def calculate_volatility_indicators(self, df_15m: pd.DataFrame, df_30m: pd.DataFrame, df_5m: pd.DataFrame) -> Dict:
        """计算波动率指标"""
        result = {
            'atr_expansion': 0,
            'bb_expansion': 0,
            'vix_score': 0,
            'volatility_score': 0
        }

        try:
            # 15分钟ATR扩张检测
            if len(df_15m) >= 24:
                high_15m = df_15m['high'].values
                low_15m = df_15m['low'].values
                close_15m = df_15m['close'].values

                atr = talib.ATR(high_15m, low_15m, close_15m, timeperiod=14)
                if len(atr) >= 11:
                    current_atr = atr[-1]
                    avg_atr = np.mean(atr[-11:-1])  # 过去10根ATR均值

                    if avg_atr > 0:
                        atr_ratio = current_atr / avg_atr
                        result['atr_expansion'] = max(0, (atr_ratio - 1) * 100)

            # 30分钟布林带扩张
            if len(df_30m) >= 20:
                close_30m = df_30m['close'].values
                upper, middle, lower = talib.BBANDS(close_30m, timeperiod=20, nbdevup=2, nbdevdn=2)

                if len(upper) >= 2:
                    current_width = (upper[-1] - lower[-1]) / middle[-1] * 100
                    prev_width = (upper[-2] - lower[-2]) / middle[-2] * 100

                    if prev_width > 0:
                        bb_expansion = (current_width - prev_width) / prev_width * 100
                        result['bb_expansion'] = max(0, bb_expansion)

            # 5分钟成交量变化
            if len(df_5m) >= 10:
                volumes = df_5m['volume'].values
                current_vol = np.mean(volumes[-5:])  # 最近5根平均成交量
                prev_vol = np.mean(volumes[-10:-5])  # 前5根平均成交量

                if prev_vol > 0:
                    vol_change = (current_vol - prev_vol) / prev_vol * 100
                    result['vix_score'] = max(0, vol_change)

            # 计算波动率综合评分
            score = (result['atr_expansion'] * 0.4 +
                    result['bb_expansion'] * 0.3 +
                    result['vix_score'] * 0.3)

            result['volatility_score'] = min(100, score)

        except Exception as e:
            self.logger.error(f"计算波动率指标失败: {e}")

        return result

    def calculate_breakout_signals(self, df_5m: pd.DataFrame, df_15m: pd.DataFrame) -> Dict:
        """计算突破信号"""
        result = {
            'donchian_breakout': 0,  # 1=向上突破, -1=向下突破, 0=无突破
            'macd_signal': 0,        # 1=金叉, -1=死叉, 0=无信号
            'kdj_signal': 0,         # 1=超卖反弹, -1=超买回落, 0=无信号
            'body_strength': 0,      # 实体强度评分
            'breakout_score': 0
        }

        try:
            # 5分钟唐奇安通道突破
            if len(df_5m) >= 20:
                high_5m = df_5m['high'].values
                low_5m = df_5m['low'].values
                close_5m = df_5m['close'].values

                # 计算20周期高低点
                highest = talib.MAX(high_5m, timeperiod=20)
                lowest = talib.MIN(low_5m, timeperiod=20)

                if len(highest) >= 2 and len(lowest) >= 2:
                    current_close = close_5m[-1]
                    prev_highest = highest[-2]
                    prev_lowest = lowest[-2]

                    if current_close > prev_highest:
                        result['donchian_breakout'] = 1  # 向上突破
                    elif current_close < prev_lowest:
                        result['donchian_breakout'] = -1  # 向下突破

            # 15分钟MACD信号
            if len(df_15m) >= 35:
                close_15m = df_15m['close'].values
                macd, macdsignal, macdhist = talib.MACD(close_15m, fastperiod=12, slowperiod=26, signalperiod=9)

                if len(macdhist) >= 2:
                    if macdhist[-2] <= 0 and macdhist[-1] > 0:
                        result['macd_signal'] = 1  # 金叉
                    elif macdhist[-2] >= 0 and macdhist[-1] < 0:
                        result['macd_signal'] = -1  # 死叉

            # 15分钟KDJ信号
            if len(df_15m) >= 20:
                high_15m = df_15m['high'].values
                low_15m = df_15m['low'].values
                close_15m = df_15m['close'].values

                slowk, slowd = talib.STOCH(high_15m, low_15m, close_15m,
                                         fastk_period=9, slowk_period=3, slowd_period=3)

                if len(slowk) >= 2 and len(slowd) >= 2:
                    k_curr, d_curr = slowk[-1], slowd[-1]
                    k_prev, d_prev = slowk[-2], slowd[-2]

                    # 超卖反弹
                    if k_prev <= d_prev and k_curr > d_curr and d_curr < 20:
                        result['kdj_signal'] = 1
                    # 超买回落
                    elif k_prev >= d_prev and k_curr < d_curr and d_curr > 80:
                        result['kdj_signal'] = -1

            # 最新K线实体强度计算
            if len(df_5m) >= 15:
                latest_bar = df_5m.iloc[-1]
                body_size = abs(latest_bar['close'] - latest_bar['open'])

                # 计算ATR作为参考
                high_5m = df_5m['high'].values
                low_5m = df_5m['low'].values
                close_5m = df_5m['close'].values
                atr = talib.ATR(high_5m, low_5m, close_5m, timeperiod=14)

                if not np.isnan(atr[-1]) and atr[-1] > 0:
                    body_ratio = body_size / atr[-1]
                    result['body_strength'] = min(100, body_ratio * 50)

            # 计算突破综合评分
            score = 0
            if abs(result['donchian_breakout']) == 1:
                score += 40
            if abs(result['macd_signal']) == 1:
                score += 30
            if abs(result['kdj_signal']) == 1:
                score += 20
            score += result['body_strength'] * 0.1

            result['breakout_score'] = min(100, score)

        except Exception as e:
            self.logger.error(f"计算突破信号失败: {e}")

        return result

    def evaluate_symbol(self, symbol: str) -> Dict[str, Any]:
        """评估单个品种"""
        result = {
            '品种': symbol,
            '趋势方向': '未知',
            '最佳周期': '无',
            '突破强度': 0,
            '波动率评分': 0,
            '综合评分': 0,
            '状态': '排除',
            '排除原因': ''
        }

        try:
            # 检查黑名单
            if self.trading_config.is_symbol_blacklisted(symbol):
                result['排除原因'] = '黑名单品种'
                return result

            # 获取多周期数据
            df_daily = self.fetch_data(symbol, 'D')
            df_30m = self.fetch_data(symbol, '30')
            df_15m = self.fetch_data(symbol, '15')
            df_5m = self.fetch_data(symbol, '5')

            # 检查数据有效性
            if any(df is None or df.empty for df in [df_daily, df_30m, df_15m, df_5m]):
                result['排除原因'] = '数据不足'
                return result

            # 计算各类指标
            trend_indicators = self.calculate_trend_indicators(df_daily, df_30m)
            volatility_indicators = self.calculate_volatility_indicators(df_15m, df_30m, df_5m)
            breakout_signals = self.calculate_breakout_signals(df_5m, df_15m)

            # 应用排除条件
            exclude_reason = self._check_exclusion_conditions(df_30m, df_15m, trend_indicators, df_daily)
            if exclude_reason:
                result['排除原因'] = exclude_reason
                return result

            # 确定趋势方向
            if trend_indicators['di_direction'] == 1 and trend_indicators['ema_alignment']:
                result['趋势方向'] = '多头'
            elif trend_indicators['di_direction'] == -1:
                result['趋势方向'] = '空头'
            else:
                result['趋势方向'] = '震荡'

            # 确定最佳周期
            result['最佳周期'] = self._determine_best_period(breakout_signals, volatility_indicators)

            # 计算各项评分
            result['突破强度'] = round(breakout_signals['breakout_score'], 1)
            result['波动率评分'] = round(volatility_indicators['volatility_score'], 1)

            # 计算综合评分
            综合评分 = (trend_indicators['trend_score'] * 0.3 +
                      volatility_indicators['volatility_score'] * 0.4 +
                      breakout_signals['breakout_score'] * 0.3)
            result['综合评分'] = round(综合评分, 1)

            # 确定状态
            if result['综合评分'] >= 80:
                result['状态'] = '重点关注'
            elif result['综合评分'] >= 60:
                result['状态'] = '准备入场'
            elif result['综合评分'] >= 40:
                result['状态'] = '观察'
            else:
                result['状态'] = '排除'
                result['排除原因'] = '评分过低'

        except Exception as e:
            result['排除原因'] = f'计算错误: {str(e)}'
            self.logger.error(f"评估品种 {symbol} 失败: {e}")

        return result

    def _check_exclusion_conditions(self, df_30m: pd.DataFrame, df_15m: pd.DataFrame,
                                   trend_indicators: Dict, df_daily: pd.DataFrame = None) -> str:
        """检查排除条件"""
        try:
            # 检查30分钟ADX是否过低
            if len(df_30m) >= 20:
                high_30m = df_30m['high'].values
                low_30m = df_30m['low'].values
                close_30m = df_30m['close'].values

                adx_30m = talib.ADX(high_30m, low_30m, close_30m, timeperiod=14)
                if not np.isnan(adx_30m[-1]) and adx_30m[-1] < 8:
                    return "30分钟ADX过低"

            # 检查成交量是否过低
            if len(df_15m) >= 5:
                recent_volumes = df_15m['volume'].tail(5).mean()
                if recent_volumes < 1000:  # 成交量阈值
                    return "成交量过低"

            # 检查价格是否异常
            if len(df_30m) >= 1:
                latest_close = df_30m['close'].iloc[-1]
                if latest_close <= 0:
                    return "价格异常"

        except Exception as e:
            return f"排除条件检查失败: {str(e)}"

        return ""

    def _determine_best_period(self, breakout_signals: Dict, volatility_indicators: Dict) -> str:
        """确定最佳交易周期"""
        scores = {
            '5分钟': 0,
            '15分钟': 0,
            '30分钟': 0
        }

        # 根据突破信号强度分配周期偏好
        if breakout_signals['donchian_breakout'] != 0:
            scores['5分钟'] += 40
        if breakout_signals['macd_signal'] != 0:
            scores['15分钟'] += 30
        if breakout_signals['kdj_signal'] != 0:
            scores['15分钟'] += 20

        # 根据波动率分配周期偏好
        if volatility_indicators['atr_expansion'] > 50:
            scores['15分钟'] += 30
        if volatility_indicators['bb_expansion'] > 30:
            scores['30分钟'] += 25
        if volatility_indicators['vix_score'] > 40:
            scores['5分钟'] += 20

        # 返回得分最高的周期
        best_period = max(scores, key=scores.get)
        return best_period if scores[best_period] > 0 else '无'

    def run_scan(self, max_workers: int = 4) -> List[Dict[str, Any]]:
        """执行品种扫描"""
        self.logger.info(f"开始扫描 {len(self.symbols)} 个期货品种...")

        results = []
        start_time = time.time()

        # 使用多线程并发处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_symbol = {
                executor.submit(self.evaluate_symbol, symbol): symbol
                for symbol in self.symbols
            }

            # 收集结果
            completed = 0
            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    result = future.result()
                    results.append(result)
                    completed += 1

                    # 实时显示进度
                    status = result['状态']
                    score = result['综合评分']
                    progress = f"[{completed}/{len(self.symbols)}]"

                    if result['排除原因']:
                        self.logger.info(f"{progress} {symbol:12} | {status:8} | 评分: {score:5.1f} | 原因: {result['排除原因']}")
                    else:
                        self.logger.info(f"{progress} {symbol:12} | {status:8} | 评分: {score:5.1f}")

                except Exception as e:
                    self.logger.error(f"处理品种 {symbol} 失败: {e}")
                    results.append({
                        '品种': symbol,
                        '趋势方向': '错误',
                        '最佳周期': '无',
                        '突破强度': 0,
                        '波动率评分': 0,
                        '综合评分': 0,
                        '状态': '排除',
                        '排除原因': str(e)
                    })
                    completed += 1

        # 按综合评分排序
        results.sort(key=lambda x: x['综合评分'], reverse=True)

        end_time = time.time()
        self.logger.info(f"扫描完成，耗时: {end_time - start_time:.2f} 秒")

        return results

    def display_results(self, results: List[Dict[str, Any]]):
        """显示扫描结果"""
        if not results:
            self.logger.warning("没有扫描结果")
            return

        # 统计信息
        total_count = len(results)
        focus_count = len([r for r in results if r['状态'] == '重点关注'])
        ready_count = len([r for r in results if r['状态'] == '准备入场'])
        watch_count = len([r for r in results if r['状态'] == '观察'])
        excluded_count = len([r for r in results if r['状态'] == '排除'])

        self.logger.info("=" * 80)
        self.logger.info("期货品种筛选结果")
        self.logger.info("=" * 80)
        self.logger.info(f"总品种数: {total_count}")
        self.logger.info(f"重点关注: {focus_count} | 准备入场: {ready_count} | 观察: {watch_count} | 排除: {excluded_count}")
        self.logger.info("=" * 80)

        # 显示前20个结果
        display_count = min(20, len(results))
        self.logger.info(f"{'品种':12} | {'状态':8} | {'评分':6} | {'趋势':6} | {'周期':8} | {'突破':6} | {'波动':6}")
        self.logger.info("-" * 80)

        for i, result in enumerate(results[:display_count]):
            symbol = result['品种']
            status = result['状态']
            score = result['综合评分']
            trend = result['趋势方向']
            period = result['最佳周期']
            breakout = result['突破强度']
            volatility = result['波动率评分']

            self.logger.info(f"{symbol:12} | {status:8} | {score:6.1f} | {trend:6} | {period:8} | {breakout:6.1f} | {volatility:6.1f}")

        if len(results) > display_count:
            self.logger.info(f"... 还有 {len(results) - display_count} 个结果")

    def save_results(self, results: List[Dict[str, Any]], filename: str = None) -> str:
        """保存扫描结果到CSV文件"""
        if not results:
            self.logger.warning("没有结果可保存")
            return ""

        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"futures_screening_results_{timestamp}.csv"

            # 转换为DataFrame
            df = pd.DataFrame(results)

            # 重新排列列顺序
            columns_order = ['品种', '状态', '综合评分', '趋势方向', '最佳周期',
                           '突破强度', '波动率评分', '排除原因']
            df = df.reindex(columns=columns_order)

            # 保存到CSV
            df.to_csv(filename, index=False, encoding='utf-8-sig')

            self.logger.info(f"结果已保存到: {filename}")
            return filename

        except Exception as e:
            self.logger.error(f"保存结果失败: {e}")
            return ""

    def get_filtered_results(self, results: List[Dict[str, Any]],
                           min_score: float = 60,
                           status_filter: List[str] = None) -> List[Dict[str, Any]]:
        """获取过滤后的结果"""
        if status_filter is None:
            status_filter = ['重点关注', '准备入场']

        filtered = []
        for result in results:
            if (result['综合评分'] >= min_score and
                result['状态'] in status_filter and
                not result['排除原因']):
                filtered.append(result)

        return filtered

    def clear_cache(self):
        """清除数据缓存"""
        self.data_cache.clear()
        self.logger.info("数据缓存已清除")


if __name__ == "__main__":
    # 测试期货筛选器
    scanner = FuturesScanner()

    # 执行扫描
    results = scanner.run_scan()

    # 显示结果
    scanner.display_results(results)

    # 保存结果
    filename = scanner.save_results(results)

    # 获取高分品种
    high_score_results = scanner.get_filtered_results(results, min_score=70)
    print(f"\n高分品种数量: {len(high_score_results)}")

    print("期货筛选器测试完成")
