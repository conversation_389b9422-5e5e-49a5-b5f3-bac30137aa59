"""
交易配置管理模块
管理CTA策略参数、品种配置、交易时间等
"""

import os
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict, field
from pathlib import Path


@dataclass
class StrategyConfig:
    """策略配置"""
    name: str = "FenZhouQiPlusStrategy"
    class_name: str = "FenZhouQiPlusStrategy"
    module_name: str = "fenzhouqiplus_strategy"
    
    # 策略参数
    parameters: Dict[str, Any] = field(default_factory=lambda: {
        'k_1': 1,
        'k_3': 3,
        'k_5': 5,
        'k_15': 15,
        'k_30': 30,
        'atr_window': 30,
        'sl_multiplier': 2.0,
        'macd_boll_count_fz': 0.05,
        'dk_fz': 0.9,
        'ping_zy': 5.0,
        'zy': 10.0,
        'lots': 1,
        'donchian_period': 20,
        'use_trailing_stop': True,
        'AF': 0.002,
        'AF_max': 0.2,
        'trailing_start_ratio': 0.5,
        'daily_loss_limit': 1000,
        'contract_multiplier': 10
    })
    
    # 策略状态
    enabled: bool = True
    auto_start: bool = True


@dataclass
class AccountConfig:
    """账户配置"""
    name: str = "CTP"
    gateway_name: str = "CTP"
    
    # 连接配置
    settings: Dict[str, Any] = field(default_factory=lambda: {
        "用户名": "",
        "密码": "",
        "经纪商代码": "",
        "交易服务器": "",
        "行情服务器": "",
        "产品名称": "",
        "授权编码": ""
    })
    
    # 账户状态
    enabled: bool = True
    auto_connect: bool = True
    reconnect_interval: int = 30  # 重连间隔(秒)
    max_reconnect_times: int = 10  # 最大重连次数


@dataclass
class SymbolConfig:
    """品种配置"""
    symbol: str
    exchange: str
    enabled: bool = True
    strategy_name: str = ""
    
    # 品种参数
    size: int = 10              # 合约乘数
    pricetick: float = 1.0      # 最小价格变动
    margin_ratio: float = 0.1   # 保证金比例
    commission_ratio: float = 0.0001  # 手续费比例
    
    # 交易参数
    max_lots: int = 4           # 最大手数
    min_volume: int = 100       # 最小成交量要求


@dataclass
class TradingConfig:
    """交易配置管理类"""
    
    # 基础配置
    trading_mode: str = "live"  # live/simulation
    
    # 账户配置
    accounts: List[AccountConfig] = field(default_factory=list)
    
    # 策略配置
    strategies: List[StrategyConfig] = field(default_factory=list)
    
    # 品种配置
    symbols: List[SymbolConfig] = field(default_factory=list)
    
    # 交易控制
    max_symbols: int = 8        # 最大交易品种数
    opening_multiplier: int = 2  # 开仓倍数
    fixed_profit_ratio: float = 0.5  # 固定止盈比例
    trailing_profit_ratio: float = 0.5  # 浮动止盈比例
    
    # 品种筛选配置
    screening_config: Dict[str, Any] = field(default_factory=lambda: {
        'min_score': 25,
        'preferred_status': ['重点关注', '准备入场', '观察'],
        'blacklist': ['JR', 'LR', 'RI', 'PM', 'WH', 'RS', 'CJ'],
        'min_volume': 1000,
        'min_liquidity': 0.5
    })
    
    # 交易时间配置
    trading_sessions: Dict[str, Dict[str, str]] = field(default_factory=lambda: {
        '日盘': {
            'start': '09:00',
            'end': '15:00',
            'break_start': '11:30',
            'break_end': '13:30'
        },
        '夜盘': {
            'start': '21:00',
            'end': '02:30'
        }
    })
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.accounts:
            self.accounts = [AccountConfig()]
        
        if not self.strategies:
            self.strategies = [StrategyConfig()]
    
    def get_account_config(self, name: str) -> Optional[AccountConfig]:
        """获取账户配置"""
        for account in self.accounts:
            if account.name == name:
                return account
        return None
    
    def get_strategy_config(self, name: str) -> Optional[StrategyConfig]:
        """获取策略配置"""
        for strategy in self.strategies:
            if strategy.name == name:
                return strategy
        return None
    
    def get_symbol_config(self, symbol: str) -> Optional[SymbolConfig]:
        """获取品种配置"""
        for sym_config in self.symbols:
            if sym_config.symbol == symbol:
                return sym_config
        return None
    
    def add_symbol_config(self, symbol: str, exchange: str, **kwargs) -> SymbolConfig:
        """添加品种配置"""
        # 检查是否已存在
        existing = self.get_symbol_config(symbol)
        if existing:
            return existing
        
        # 创建新配置
        config = SymbolConfig(symbol=symbol, exchange=exchange, **kwargs)
        self.symbols.append(config)
        return config
    
    def remove_symbol_config(self, symbol: str) -> bool:
        """移除品种配置"""
        for i, config in enumerate(self.symbols):
            if config.symbol == symbol:
                del self.symbols[i]
                return True
        return False
    
    def get_enabled_symbols(self) -> List[str]:
        """获取启用的品种列表"""
        return [config.symbol for config in self.symbols if config.enabled]
    
    def get_blacklisted_symbols(self) -> List[str]:
        """获取黑名单品种"""
        return self.screening_config.get('blacklist', [])
    
    def is_symbol_blacklisted(self, symbol: str) -> bool:
        """检查品种是否在黑名单中"""
        blacklist = self.get_blacklisted_symbols()
        # 检查完整品种名和基础品种名
        base_symbol = symbol.split('.')[0] if '.' in symbol else symbol
        return symbol in blacklist or base_symbol in blacklist
    
    @classmethod
    def load_from_file(cls, config_file: str) -> 'TradingConfig':
        """从配置文件加载配置"""
        if not os.path.exists(config_file):
            config = cls()
            config.save_to_file(config_file)
            return config
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            config = cls()
            config._update_from_dict(data)
            return config
            
        except Exception as e:
            print(f"加载交易配置文件失败: {e}")
            return cls()
    
    def save_to_file(self, config_file: str):
        """保存配置到文件"""
        try:
            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(self), f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存交易配置文件失败: {e}")
    
    def _update_from_dict(self, data: Dict[str, Any]):
        """从字典更新配置"""
        for key, value in data.items():
            if key == 'accounts' and isinstance(value, list):
                self.accounts = [AccountConfig(**item) for item in value]
            elif key == 'strategies' and isinstance(value, list):
                self.strategies = [StrategyConfig(**item) for item in value]
            elif key == 'symbols' and isinstance(value, list):
                self.symbols = [SymbolConfig(**item) for item in value]
            elif hasattr(self, key):
                setattr(self, key, value)


# 全局配置实例
_trading_config: Optional[TradingConfig] = None


def get_trading_config() -> TradingConfig:
    """获取交易配置实例"""
    global _trading_config
    if _trading_config is None:
        config_file = os.path.join(
            Path(__file__).parent.parent,
            "config",
            "trading_config.json"
        )
        _trading_config = TradingConfig.load_from_file(config_file)
    return _trading_config


def reload_trading_config():
    """重新加载交易配置"""
    global _trading_config
    _trading_config = None
    return get_trading_config()


if __name__ == "__main__":
    # 测试交易配置模块
    config = TradingConfig()
    
    # 添加测试品种
    config.add_symbol_config("RB888", "SHFE", size=10, pricetick=1.0)
    config.add_symbol_config("CU888", "SHFE", size=5, pricetick=10.0)
    
    print("交易配置:")
    print(f"交易模式: {config.trading_mode}")
    print(f"最大品种数: {config.max_symbols}")
    print(f"启用品种: {config.get_enabled_symbols()}")
    print(f"黑名单品种: {config.get_blacklisted_symbols()}")
    
    # 保存配置文件
    config_file = os.path.join(
        Path(__file__).parent.parent,
        "config",
        "trading_config.json"
    )
    config.save_to_file(config_file)
    print(f"配置已保存到: {config_file}")
